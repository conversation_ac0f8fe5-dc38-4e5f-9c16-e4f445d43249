<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.CheckFacilityMapper">
    
    <resultMap type="CheckFacility" id="ScCheckFacilityResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="model"    column="model"    />
        <result property="code"    column="code"    />
        <result property="mainUsage"    column="main_usage"    />
        <result property="startPeriod"    column="start_period"    />
        <result property="endPeriod"    column="end_period"    />
        <result property="flag"    column="flag"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="currentTaskId"    column="current_task_id"    />
        <result property="currentTaskRoadName"    column="current_task_road_name"    />
    </resultMap>

    <sql id="selectScCheckFacilityVo">
        select id, name, model, code, main_usage, start_period, end_period, flag, remark, create_time, update_time from sc_check_facility
    </sql>

    <select id="selectScCheckFacilityList" parameterType="CheckFacility" resultMap="ScCheckFacilityResult">
        SELECT 
            cf.id, cf.name, cf.model, cf.code, cf.main_usage, cf.start_period, cf.end_period, 
            cf.flag, cf.remark, cf.create_time, cf.update_time,
            latest_task.task_id as current_task_id,
            latest_task.road_name as current_task_road_name
        FROM sc_check_facility cf
        LEFT JOIN (
            SELECT 
                cfr.facility_id,
                t.id as task_id,
                t.road_name,
                ROW_NUMBER() OVER (PARTITION BY cfr.facility_id ORDER BY cfr.create_time DESC) as rn
            FROM sc_check_facility_record cfr
            INNER JOIN sc_check_facility_apply cfa ON cfr.apply_id = cfa.id
            INNER JOIN sc_task t ON cfa.task_id = t.id
            WHERE cfa.type = 1 AND cfa.status = 1  -- 出库申请且已通过
        ) latest_task ON cf.id = latest_task.facility_id AND latest_task.rn = 1
        <where>  
            <if test="name != null  and name != ''"> and cf.name like concat('%', #{name}, '%')</if>
            <if test="model != null  and model != ''"> and cf.model like concat('%', #{model}, '%')</if>
            <if test="code != null  and code != ''"> and cf.code like concat('%', #{code}, '%')</if>
            <if test="mainUsage != null  and mainUsage != ''"> and cf.main_usage like concat('%', #{mainUsage}, '%')</if>
            <if test="startPeriod != null  and startPeriod != ''"> and cf.start_period like concat('%', #{startPeriod}, '%')</if>
            <if test="endPeriod != null  and endPeriod != ''"> and cf.end_period like concat('%', #{endPeriod}, '%')</if>
            <if test="flag != null"> and cf.flag = #{flag}</if>
        </where>
        order by cf.id desc
    </select>
    
    <select id="selectScCheckFacilityById" parameterType="Long" resultMap="ScCheckFacilityResult">
        <include refid="selectScCheckFacilityVo"/>
        where id = #{id}
    </select>
        
    <select id="selectScCheckFacilityByCode" parameterType="String" resultMap="ScCheckFacilityResult">
        <include refid="selectScCheckFacilityVo"/>
        where code = #{code}
    </select>
    <select id="selectCheckFacilityByCheckFacilityIds" resultType="com.tunnel.domain.CheckFacility">
        select * from sc_check_facility
         where id in
         <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
         </foreach>
    </select>

    <insert id="insertScCheckFacility" parameterType="CheckFacility" useGeneratedKeys="true" keyProperty="id">
        insert into sc_check_facility
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="model != null and model != ''">model,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="mainUsage != null and mainUsage != ''">main_usage,</if>
            <if test="startPeriod != null and startPeriod != ''">start_period,</if>
            <if test="endPeriod != null and endPeriod != ''">end_period,</if>
            <if test="flag != null">flag,</if>
            <if test="remark != null">remark,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="model != null and model != ''">#{model},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="mainUsage != null and mainUsage != ''">#{mainUsage},</if>
            <if test="startPeriod != null and startPeriod != ''">#{startPeriod},</if>
            <if test="endPeriod != null and endPeriod != ''">#{endPeriod},</if>
            <if test="flag != null">#{flag},</if>
            <if test="remark != null">#{remark},</if>
            now(),
            now()
         </trim>
    </insert>

    <update id="updateScCheckFacility" parameterType="CheckFacility">
        update sc_check_facility
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="model != null and model != ''">model = #{model},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="mainUsage != null and mainUsage != ''">main_usage = #{mainUsage},</if>
            <if test="startPeriod != null and startPeriod != ''">start_period = #{startPeriod},</if>
            <if test="endPeriod != null and endPeriod != ''">end_period = #{endPeriod},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScCheckFacilityById" parameterType="Long">
        delete from sc_check_facility where id = #{id}
    </delete>

    <delete id="deleteScCheckFacilityByIds" parameterType="String">
        delete from sc_check_facility where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>