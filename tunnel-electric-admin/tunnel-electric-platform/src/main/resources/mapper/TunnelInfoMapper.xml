<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TunnelInfoMapper">

    <sql id="selectTunnelInfoVo">
        select id, tunnel_code, up_tunnel_code, down_tunnel_code, tunnel_name, tunnel_id,
               company_name, road_name, road_code, up_start_code, down_start_code,up_end_code,down_end_code, longitude,
               latitude, up_tunnel_length, down_tunnel_length, total_tunnel_length, tunnel_type,
                work_pic1, work_pic2, work_pic3, remark, create_time, update_time, status
        from sc_tunnel_info
    </sql>

    <select id="selectTunnelInfoList" parameterType="TunnelInfo" resultType="TunnelInfo">
        select i.*,t.section
        from sc_tunnel_info i
        left join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="tunnelId != null"> and i.id = #{tunnelId}</if>
            <if test="section != null  and section != ''"> and t.section = #{section}</if>
            <if test="tunnelCode != null  and tunnelCode != ''"> and i.tunnel_code = #{tunnelCode}</if>
            <if test="tunnelName != null  and tunnelName != ''"> and i.tunnel_name like concat('%', #{tunnelName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and i.company_name like concat('%', #{companyName}, '%')</if>
            <if test="roadName != null  and roadName != ''"> and i.road_name like concat('%', #{roadName}, '%')</if>
            <if test="roadCode != null  and roadCode != ''"> and i.road_code = #{roadCode}</if>
            <if test="status != null"> and i.status = #{status}</if>
        </where>
    </select>

    <select id="selectTunnelInfoById" parameterType="Long"  resultType="TunnelInfo">
        select * from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        where i.id = #{id}
    </select>
    <select id="selectDistinctTunnelCode" resultType="com.tunnel.domain.TunnelInfo">
        select distinct i.id,i.tunnel_code,i.tunnel_name
        from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="roadCode != null"> and i.road_code = #{roadCode}</if>
            <if test="roadName != null"> and i.road_name = #{roadName}</if>
            <if test="companyName != null"> and i.company_name = #{companyName}</if>
            <if test="section != null"> and t.section = #{section}</if>
        </where>
    </select>
    <select id="selectByTunnelCodes" resultType="com.tunnel.domain.TunnelInfo">
        select * from sc_tunnel_info where tunnel_code in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectByParams" resultType="com.tunnel.domain.TunnelInfo">
        select * from sc_tunnel_info
        where tunnel_id = #{tunnelId}
    </select>
    <select id="selectByTunnelId" resultType="com.tunnel.domain.TunnelInfo">
        select * from sc_tunnel_info where tunnel_id = #{tunnelId}
    </select>
    <select id="selectDistinctTunnelList" resultType="com.tunnel.domain.TunnelInfo">
        select  i.tunnel_name, i.id,i.complete_status
        from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="roadName != null"> and i.road_name = #{roadName}</if>
            <if test="tunnelCode != null"> and i.tunnel_code = #{tunnelCode}</if>
            <if test="roadCode != null"> and i.road_code = #{roadCode}</if>
            <if test="companyName != null"> and i.company_name = #{companyName}</if>
            <if test="section != null"> and t.section = #{section}</if>
            <if test="completeStatus != null"> and i.complete_status = #{completeStatus}</if>
              and i.id in
                <foreach item="id" collection="tunnelIds" separator="," open="(" close=")">
                    #{id}
                </foreach>
        </where>
    </select>

    <select id="selectDistinctCompany" resultType="TunnelInfo">
        select distinct i.company_name
        from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="section != null"> and t.section = #{section}</if>
        </where>
    </select>
    <select id="selectDistinctRoad" resultType="com.tunnel.domain.TunnelInfo">
        select distinct i.road_name,i.road_code
        from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="companyName != null"> and i.company_name = #{companyName}</if>
            <if test="section != null"> and t.section = #{section}</if>
        </where>
    </select>
    <select id="listAll" resultType="com.tunnel.domain.TunnelInfo">
        select * from sc_tunnel_info
    </select>
    <select id="listDistinctRoad" resultType="com.tunnel.domain.TunnelInfo">
        select distinct road_name,road_code from sc_tunnel_info
    </select>
    <select id="selectListByTunnelId" resultType="com.tunnel.domain.TunnelInfo">
        select * from sc_tunnel_info where tunnel_id = #{tunnelId}
    </select>
    <select id="selectListByRoadName" resultType="com.tunnel.domain.TunnelInfo">
        select i.*,t.section from sc_tunnel_info i left join sc_tunnel t on i.tunnel_id = t.id
         where road_name = #{roadName}
    </select>
    <select id="selectTunnelByIds" resultType="com.tunnel.domain.TunnelInfo">
        select *
        from sc_tunnel_info t
        where FIND_IN_SET(t.id, #{tunnelIds})
    </select>
    <select id="selectListByRoadNameList" resultType="com.tunnel.domain.TunnelInfo">
        select * from sc_tunnel_info where road_name in
        <foreach item="item" collection="roadNameList" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    
    <!-- 查询不重复的路线编码列表（用于级联下拉框） -->
    <select id="selectDistinctRoadCode" resultType="com.tunnel.domain.TunnelInfo">
        select distinct i.road_code
        from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="companyName != null and companyName != ''"> and i.company_name = #{companyName}</if>
            <if test="roadName != null and roadName != ''"> and i.road_name = #{roadName}</if>
            <if test="section != null and section != ''"> and t.section = #{section}</if>
        </where>
        order by i.road_code
    </select>
    
    <!-- 查询不重复的隧道名称列表（用于级联下拉框） -->
    <select id="selectDistinctTunnelName" resultType="com.tunnel.domain.TunnelInfo">
        select distinct i.tunnel_name
        from sc_tunnel_info i
        inner join sc_tunnel t on i.tunnel_id = t.id
        <where>
            <if test="companyName != null and companyName != ''"> and i.company_name = #{companyName}</if>
            <if test="roadName != null and roadName != ''"> and i.road_name = #{roadName}</if>
            <if test="roadCode != null and roadCode != ''"> and i.road_code = #{roadCode}</if>
            <if test="section != null and section != ''"> and t.section = #{section}</if>
        </where>
        order by i.tunnel_name
    </select>

    <insert id="batchAdd">
        INSERT INTO sc_tunnel_info (tunnel_code, up_tunnel_code, down_tunnel_code, tunnel_name, tunnel_id,
        company_name, road_name, road_code, up_start_code, down_start_code,up_end_code,down_end_code, longitude,
        latitude, up_tunnel_length, down_tunnel_length, total_tunnel_length, tunnel_type,
         work_pic1, work_pic2, work_pic3, remark)
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (#{item.tunnelCode}, #{item.upTunnelCode}, #{item.downTunnelCode}, #{item.tunnelName}, #{item.tunnelId},
             #{item.companyName}, #{item.roadName}, #{item.roadCode}, #{item.upStartCode},
            #{item.downStartCode},#{item.upEndCode},#{item.downEndCode}, #{item.longitude}, #{item.latitude}, #{item.upTunnelLength},
            #{item.downTunnelLength}, #{item.totalTunnelLength}, #{item.tunnelType},
            #{item.workPic1}, #{item.workPic2}, #{item.workPic3}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateTunnelInfo">
        UPDATE sc_tunnel_info
        <set>
            <if test="upTunnelCode != null"> up_tunnel_code = #{upTunnelCode}, </if>
            <if test="tunnelCode != null"> tunnel_code = #{tunnelCode}, </if>
            <if test="downTunnelCode != null"> down_tunnel_code = #{downTunnelCode}, </if>
            <if test="tunnelName != null"> tunnel_name = #{tunnelName}, </if>
            <if test="tunnelId != null"> tunnel_id = #{tunnelId}, </if>
            <if test="companyName != null"> company_name = #{companyName}, </if>
            <if test="roadName != null"> road_name = #{roadName}, </if>
            <if test="roadCode != null"> road_code = #{roadCode}, </if>
            <if test="upStartCode != null"> up_start_code = #{upStartCode}, </if>
            <if test="downStartCode != null"> down_start_code = #{downStartCode}, </if>
            <if test="longitude != null"> longitude = #{longitude}, </if>
            <if test="latitude != null"> latitude = #{latitude}, </if>
            <if test="upTunnelLength != null"> up_tunnel_length = #{upTunnelLength}, </if>
            <if test="downTunnelLength != null"> down_tunnel_length = #{downTunnelLength}, </if>
            <if test="totalTunnelLength != null"> total_tunnel_length = #{totalTunnelLength}, </if>
            <if test="tunnelType != null"> tunnel_type = #{tunnelType}, </if>
            work_pic1 = #{workPic1},
            work_pic2 = #{workPic2},
            work_pic3 = #{workPic3},
            <if test="remark != null"> remark = #{remark}, </if>
            <if test="status != null"> status = #{status}, </if>
        </set>
        WHERE id = #{id}
    </update>
    <update id="updateTunnelInfoCompleteStatus">
        UPDATE sc_tunnel_info
        SET complete_status = abs(complete_status-1)
        WHERE id = #{id}
    </update>

    <delete id="deleteTunnelInfoById" parameterType="Long">
        delete from sc_tunnel_info where id = #{id}
    </delete>

    <delete id="deleteTunnelInfoByIds" parameterType="String">
        delete from sc_tunnel_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>