<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TunnelCheckMapper">
    

    <sql id="selectTunnelCheckVo">
        select *
        from sc_tunnel_check
    </sql>

    <select id="selectTunnelCheckList" parameterType="TunnelCheck" resultType="TunnelCheck">
        select c.id,c.tunnel_id,c.facility_id,c.item_id,c.score,c.num,c.check_code,
            IF(c.name IS NULL OR c.name = '', f.name, c.name) AS name,
               c.resistor,
               c.pic_url1,c.pic_url2,c.pic_url3,c.work_pic1,c.work_pic2,c.work_pic3,
               c.remark,c.create_time,c.update_time,c.longitude,c.latitude,c.address,
            t.section section,c.type,c.rate,c.start_time,c.end_time,
            c.check_facility_ids,c.standard_ids,c.low_temperature,c.high_temperature,c.low_humidity,c.high_humidity,
            s.tunnel_name tunnelName,s.tunnel_code tunnelCode,s.company_name companyName,s.road_code roadCode,
            f.tunnel_id tunnelId,
            f.check_code checkCode,f.part_name partName,f.part_code partCode,f.item_name itemName,f.item_code itemCode,
            f.location location,f.location_name locationName,f.code code,f.equip_code equipCode,f.status,
            e.check_content checkContent,
            e.question_desc questionDesc,if(c.suggestion is null,e.suggestion,c.suggestion) suggestion,s.road_name,
            e.unit,e.weight,c.check_status,c.check_remark,
            -- 获取检测人员姓名
            (SELECT nick_name FROM sys_user WHERE user_id = c.creator LIMIT 1) AS checkUserName,
            -- 获取复核人员姓名
            (SELECT nick_name FROM sys_user WHERE user_id = c.modifier LIMIT 1) AS reviewerName
            <if test="groupBy!=null">
            ,count(distinct c.facility_id)questionNum
            </if>
            from sc_tunnel_check c
            inner join sc_facility_info f on f.id = c.facility_id
            inner join sc_tunnel_info s on s.id = c.tunnel_id
            inner join sc_tunnel t on s.tunnel_id =t.id
            inner join sc_check_enum e on e.id =c.item_id
            <where>
                <if test="tunnelCodeOrName != null  and tunnelCodeOrName != ''">
                    and (s.tunnel_code like concat('%',#{tunnelCodeOrName},'%') or s.tunnel_name  like concat('%',#{tunnelCodeOrName},'%') )
                </if>
                <if test="id != null"> and c.id = #{id}</if>
                <if test="type != null"> and c.type = #{type}</if>
                <if test="companyName != null"> and t.company_name = #{companyName}</if>
                <if test="roadName != null"> and s.road_name = #{roadName}</if>
                <if test="tunnelId != null"> and c.tunnel_id = #{tunnelId}</if>
                <if test="facilityId != null "> and c.facility_id = #{facilityId}</if>
                <if test="itemId != null "> and c.item_id = #{itemId}</if>
                <if test="creator !=null">and c.creator = #{creator}</if>
                <if test="partCode != null"> and e.part_code=#{partCode}</if>
                <if test="itemCode != null"> and e.item_code=#{itemCode}</if>
                <if test="location != null"> and f.location=#{location}</if>
                <if test="questionDesc != null"> and e.question_desc =#{questionDesc}</if>
                <if test="recheckStatus != null"> and c.recheck_status = #{recheckStatus}</if>
                <if test="recheckStatus == null"> and c.recheck_status = 0</if>
                <if test="tunnelList != null and tunnelList.size()>0">
                    and  s.id in
                    <foreach item="item" collection="tunnelList" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            <if test="groupBy!=null">
                group by s.tunnel_code,f.part_code,f.item_code,e.question_desc
            </if>
            order by s.road_name,c.id desc
    </select>



    <select id="selectTunnelCheckListAll" parameterType="TunnelCheck" resultType="TunnelCheck">
        select c.id,c.tunnel_id,c.facility_id,c.item_id,c.score,c.num,c.check_code,
        IF(c.name IS NULL OR c.name = '', f.name, c.name) AS name
        ,c.resistor,
        c.pic_url1,c.pic_url2,c.pic_url3,c.work_pic1,c.work_pic2,c.work_pic3,
        c.remark,c.create_time,c.update_time,c.longitude,c.latitude,c.address,
        t.section section,c.type,c.rate,c.start_time,c.end_time,
        c.check_facility_ids,c.standard_ids,c.low_temperature,c.high_temperature,c.low_humidity,c.high_humidity,
        s.tunnel_name tunnelName,s.tunnel_code tunnelCode,s.company_name companyName,s.road_code roadCode,
        f.tunnel_id tunnelId,
        f.check_code checkCode,f.part_name partName,f.part_code partCode,f.item_name itemName,f.item_code itemCode,
        f.location location,f.location_name locationName,f.code code,f.equip_code equipCode,f.status,
        e.check_content checkContent,
        e.question_desc questionDesc,if(c.suggestion is null,e.suggestion,c.suggestion) suggestion,s.road_name,
        e.unit,e.weight,c.check_status,c.check_remark,
        -- 获取检测人员姓名
        (SELECT nick_name FROM sys_user WHERE user_id = c.creator LIMIT 1) AS checkUserName,
        -- 获取复核人员姓名
        (SELECT nick_name FROM sys_user WHERE user_id = c.modifier LIMIT 1) AS reviewerName
        <if test="groupBy!=null">
            ,count(distinct c.facility_id)questionNum
        </if>
        from sc_tunnel_check c
        inner join sc_facility_info f on f.id = c.facility_id
        inner join sc_tunnel_info s on s.id = c.tunnel_id
        inner join sc_tunnel t on s.tunnel_id =t.id
        left join sc_check_enum e on e.id =c.item_id
        <where>
            <if test="tunnelCodeOrName != null  and tunnelCodeOrName != ''">
                and (s.tunnel_code like concat('%',#{tunnelCodeOrName},'%') or s.tunnel_name  like concat('%',#{tunnelCodeOrName},'%') )
            </if>
            <if test="id != null"> and c.id = #{id}</if>
            <if test="type != null"> and c.type = #{type}</if>
            <if test="companyName != null"> and t.company_name = #{companyName}</if>
            <if test="roadName != null"> and s.road_name = #{roadName}</if>
            <if test="tunnelId != null"> and c.tunnel_id = #{tunnelId}</if>
            <if test="facilityId != null "> and c.facility_id = #{facilityId}</if>
            <if test="itemId != null "> and c.item_id = #{itemId}</if>
            <if test="creator !=null">and c.creator = #{creator}</if>
            <if test="partCode != null"> and f.part_code=#{partCode}</if>
            <if test="itemCode != null"> and f.item_code=#{itemCode}</if>
            <if test="location != null"> and f.location=#{location}</if>
            <if test="questionDesc != null"> and e.question_desc =#{questionDesc}</if>
            <if test="recheckStatus != null"> and c.recheck_status = #{recheckStatus}</if>
            <if test="recheckStatus == null"> and c.recheck_status = 0 </if>
            <if test="tunnelList != null and tunnelList.size()>0">
                and  s.id in
                <foreach item="item" collection="tunnelList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="groupBy!=null">
            group by s.tunnel_code,f.part_code,f.item_code,e.question_desc
        </if>
        order by s.road_name,c.id desc
    </select>

    <select id="selectTunnelCheckListCount" parameterType="TunnelCheck" resultType="Long">
        select count(1) from (
            select 1
            from sc_tunnel_check c
            inner join sc_facility_info f on f.id = c.facility_id
            inner join sc_tunnel_info s on s.id = c.tunnel_id
            inner join sc_tunnel t on s.tunnel_id =t.id
            left join sc_check_enum e on e.id =c.item_id
            <where>
                <if test="id != null"> and c.id = #{id}</if>
                <if test="tunnelCodeOrName != null  and tunnelCodeOrName != ''">
                    and (s.tunnel_code like concat('%',#{tunnelCodeOrName},'%') or s.tunnel_name  like concat('%',#{tunnelCodeOrName},'%') )
                </if>
                <if test="companyName != null"> and t.company_name = #{companyName}</if>
                <if test="roadName != null"> and s.road_name = #{roadName}</if>
                <if test="tunnelId != null"> and c.tunnel_id = #{tunnelId}</if>
                <if test="facilityId != null "> and c.facility_id = #{facilityId}</if>
                <if test="itemId != null "> and c.item_id = #{itemId}</if>
                <if test="creator !=null">and c.creator = #{creator}</if>
                <if test="partCode != null"> and f.part_code=#{partCode}</if>
                <if test="itemCode != null"> and f.item_code=#{itemCode}</if>
                <if test="questionDesc != null"> and e.question_desc =#{questionDesc}</if>
                <if test="recheckStatus != null"> and c.recheck_status = #{recheckStatus}</if>
                <if test="recheckStatus == null"> and c.recheck_status = 0</if>
                <if test="tunnelList != null and tunnelList.size()>0">
                    and  s.id in
                    <foreach item="item" collection="tunnelList" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            <if test="groupBy!=null">
                group by s.tunnel_code,f.part_code,f.item_code,e.question_desc
            </if>
        ) t
    </select>
    
    <select id="selectTunnelCheckById" parameterType="Long" resultType="TunnelCheck">
        <include refid="selectTunnelCheckVo"/>
        where id = #{id}
    </select>

    <select id="selectByFacilityId" resultType="com.tunnel.domain.TunnelCheck">
        select * from sc_tunnel_check c where facility_id = #{facilityId} and c.recheck_status = 0
    </select>
    <select id="selectByParams" resultType="com.tunnel.domain.TunnelCheck">
        select * from sc_tunnel_check c
        <where>
                and check_year = #{checkYear}
                and tunnel_id = #{tunnelId}
                and facility_id = #{facilityId}
                and item_id is null
                 and c.recheck_status = 0
        </where>
    </select>
    <select id="selectListByTunnelId" resultType="com.tunnel.domain.TunnelCheck">
        select c.*,i.part_name,i.part_code,i.item_name,i.item_code,i.location,i.location_name,i.code,
               IF(c.name IS NULL OR c.name = '', i.name, c.name) AS name,i.equip_code
        from sc_tunnel_check c
        inner join sc_facility_info i on i.id = c.facility_id
        where c.tunnel_id = #{tunnelId}
        and c.recheck_status = 0
    </select>

    <insert id="insertTunnelCheck" parameterType="TunnelCheck" useGeneratedKeys="true" keyProperty="id">
        insert into sc_tunnel_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            check_year,
            <if test="tunnelId != null and tunnelId != ''">tunnel_id,</if>
            <if test="facilityId != null">facility_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="score != null">score,</if>
            <if test="resistor != null">resistor,</if>
            <if test="num != null">num,</if>
            <if test="name != null">name,</if>
            <if test="checkCode != null">check_code,</if>
            <if test="picUrl1 != null and picUrl1 != ''">pic_url1,</if>
            <if test="picUrl2 != null and picUrl2 != ''">pic_url2,</if>
            <if test="picUrl3 != null and picUrl3 != ''">pic_url3,</if>
            <if test="workPic1 != null and workPic1 != ''">work_pic1,</if>
            <if test="workPic2 != null and workPic2 != ''">work_pic2,</if>
            <if test="workPic3 != null and workPic3 != ''">work_pic3,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="creator != null">creator,</if>
            <if test="type != null">type,</if>
            <if test="rate != null">rate,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkRemark != null and checkRemark != ''">check_remark,</if>
            <if test="checkFacilityIds != null and checkFacilityIds != ''">check_facility_ids,</if>
            <if test="standardIds != null and standardIds != ''">standard_ids,</if>
            <if test="lowTemperature != null">low_temperature,</if>
            <if test="highTemperature != null">high_temperature,</if>
            <if test="lowHumidity != null">low_humidity,</if>
            <if test="highHumidity != null">high_humidity,</if>
            <if test="startTime != null and startTime != ''">start_time,</if>
            <if test="endTime != null and endTime != ''">end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{checkYear},
            <if test="tunnelId != null and tunnelId != ''">#{tunnelId},</if>
            <if test="facilityId != null">#{facilityId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="score != null">#{score},</if>
            <if test="resistor != null">#{resistor},</if>
            <if test="num != null">#{num},</if>
            <if test="name != null">#{name},</if>
            <if test="checkCode != null">#{checkCode},</if>
            <if test="picUrl1 != null and picUrl1 != ''">#{picUrl1},</if>
            <if test="picUrl2 != null and picUrl2 != ''">#{picUrl2},</if>
            <if test="picUrl3 != null and picUrl3 != ''">#{picUrl3},</if>
            <if test="workPic1 != null and workPic1 != ''">#{workPic1},</if>
            <if test="workPic2 != null and workPic2 != ''">#{workPic2},</if>
            <if test="workPic3 != null and workPic3 != ''">#{workPic3},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="creator != null">#{creator},</if>
            <if test="type != null">#{type},</if>
            <if test="rate != null">#{rate},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkRemark != null and checkRemark != ''">#{checkRemark},</if>
            <if test="checkFacilityIds != null and checkFacilityIds != ''">#{checkFacilityIds},</if>
            <if test="standardIds != null and standardIds != ''">#{standardIds},</if>
            <if test="lowTemperature != null">#{lowTemperature},</if>
            <if test="highTemperature != null">#{highTemperature},</if>
            <if test="lowHumidity != null">#{lowHumidity},</if>
            <if test="highHumidity != null">#{highHumidity},</if>
            <if test="startTime != null and startTime != ''">#{startTime},</if>
            <if test="endTime != null and endTime != ''">#{endTime},</if>
         </trim>
    </insert>

    <update id="updateTunnelCheck" parameterType="TunnelCheck">
        update sc_tunnel_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="tunnelId != null and tunnelId != ''">tunnel_id = #{tunnelId},</if>
            <if test="facilityId != null">facility_id = #{facilityId},</if>
            item_id = #{itemId},
            score = #{score},
            resistor = #{resistor},
            rate = #{rate},
            name = #{name},
            check_code = #{checkCode},
            pic_url1 = #{picUrl1},
            pic_url2 = #{picUrl2},
            pic_url3 = #{picUrl3},
            work_pic1 = #{workPic1},
            work_pic2 = #{workPic2},
            work_pic3 = #{workPic3},
            remark = #{remark},
            check_remark = #{checkRemark},
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="address != null">address = #{address},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="startTime != null and startTime != ''">start_time = #{startTime},</if>
            <if test="endTime != null and endTime != ''">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateSuggestionById">
        update sc_tunnel_check set suggestion = #{suggestion} where id = #{id}
    </update>
    <update id="updateCheckCodeByTunnelCheck">
       update sc_tunnel_check
       set
       check_code = #{checkCode},
       resistor = #{resistor},
       rate = #{rate},
       name = #{name},
       item_id = #{itemId},
       score=#{score},
       check_status = 0,
       check_remark = null,
       modifier = #{modifier}
       where check_year = #{checkYear} and tunnel_id=#{tunnelId}
       and facility_id=#{facilityId} and item_id=#{itemId}
    </update>

    <delete id="deleteTunnelCheckById" parameterType="Long">
        delete from sc_tunnel_check where id = #{id}
    </delete>

    <delete id="deleteTunnelCheckByIds" parameterType="String">
        delete from sc_tunnel_check where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteCheckByIds">
        delete from sc_tunnel_check where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询检测明细列表（未分组） -->
    <select id="selectTunnelCheckDetailList" parameterType="TunnelCheck" resultType="TunnelCheck">
        select c.id,c.tunnel_id,c.facility_id,c.item_id,c.score,c.num,c.check_code,
        IF(c.name IS NULL OR c.name = '', f.name, c.name) AS name,
               c.resistor,
               c.pic_url1,c.pic_url2,c.pic_url3,c.work_pic1,c.work_pic2,c.work_pic3,
               c.remark,c.create_time,c.update_time,c.longitude,c.latitude,c.address,c.type,c.check_status,c.check_remark,
            t.section section,c.rate,
            c.check_facility_ids,c.standard_ids,c.low_temperature,c.high_temperature,c.low_humidity,c.high_humidity,c.start_time,c.end_time,
            s.tunnel_name tunnelName,s.tunnel_code tunnelCode,s.company_name companyName,s.road_code roadCode,
            f.tunnel_id tunnelId,
            f.check_code checkCode,f.part_name partName,f.part_code partCode,f.item_name itemName,f.item_code itemCode,
            f.location location,f.location_name locationName,f.code code,f.equip_code equipCode,f.status,
            e.check_content checkContent,
            e.question_desc questionDesc,if(c.suggestion is null,e.suggestion,c.suggestion) suggestion,s.road_name,
                         e.unit,e.weight,c.check_status,c.check_remark,
             -- 获取检测人员姓名
             (SELECT nick_name FROM sys_user WHERE user_id = c.creator LIMIT 1) AS checkUserName,
             -- 获取复核人员姓名
             (SELECT nick_name FROM sys_user WHERE user_id = c.modifier LIMIT 1) AS reviewerName
             from sc_tunnel_check c
            inner join sc_facility_info f on f.id = c.facility_id
            inner join sc_tunnel_info s on s.id = c.tunnel_id
            inner join sc_tunnel t on s.tunnel_id =t.id
            left join sc_check_enum e on e.id =c.item_id
            <where>
                <if test="tunnelId != null"> and c.tunnel_id = #{tunnelId}</if>
                <if test="type != null"> and c.type = #{type}</if>
                <if test="partCode != null"> and f.part_code=#{partCode}</if>
                <if test="itemCode != null"> and f.item_code=#{itemCode}</if>
                <if test="questionDesc != null"> and e.question_desc =#{questionDesc}</if>
                <if test="recheckStatus != null"> and c.recheck_status = #{recheckStatus}</if>
                <if test="recheckStatus == null"> and (c.recheck_status = 0 or c.recheck_status is null)</if>
            </where>
            order by c.create_time desc
    </select>

    <!-- 查询检测明细总数（未分组） -->
    <select id="selectTunnelCheckDetailCount" parameterType="TunnelCheck" resultType="Long">
        select count(1)
        from sc_tunnel_check c
        inner join sc_facility_info f on f.id = c.facility_id
        inner join sc_tunnel_info s on s.id = c.tunnel_id
        inner join sc_tunnel t on s.tunnel_id =t.id
        left join sc_check_enum e on e.id =c.item_id
        <where>
            <if test="tunnelId != null"> and c.tunnel_id = #{tunnelId}</if>
            <if test="partCode != null"> and f.part_code=#{partCode}</if>
            <if test="itemCode != null"> and f.item_code=#{itemCode}</if>
            <if test="questionDesc != null"> and e.question_desc =#{questionDesc}</if>
            <if test="recheckStatus != null"> and c.recheck_status = #{recheckStatus}</if>
            <if test="recheckStatus == null"> and (c.recheck_status = 0 or c.recheck_status is null)</if>
        </where>
    </select>
    <select id="selectByTunnelCheck" resultType="com.tunnel.domain.TunnelCheck">
        select * from sc_tunnel_check c
         where check_year=#{checkYear} and tunnel_id=#{tunnelId} and facility_id=#{facilityId} and item_id=#{itemId}
        and c.recheck_status = 0
    </select>
    <select id="selectUnQuestionListByCheckEnumRelation" resultType="com.tunnel.domain.TunnelCheck">
        select c.*,i.part_name,i.part_code,i.item_name,i.item_code,i.location,i.location_name,i.code,
               IF(c.name IS NULL OR c.name = '', i.name, c.name) AS name,i.equip_code
        from sc_tunnel_check c
        inner join sc_facility_info i on i.id = c.facility_id
        where c.tunnel_id = #{tunnelId}
          and i.part_code=#{partCode} and i.item_code= #{itemCode}
        and c.remark is not null
        and c.recheck_status = 0
    </select>
    <select id="selectGoodCheckNum" resultType="java.lang.Integer">
        select count(distinct facility_id)
        from sc_tunnel_check c
        inner join sc_facility_info f on f.id = c.facility_id
        where c.tunnel_id=#{tunnelId} and c.item_id is null
        and f.part_code=#{partCode}
        and f.item_code=#{itemCode}
        and c.recheck_status = 0
    </select>
    <select id="selectQuestionCheckNum" resultType="java.lang.Integer">
        select count(distinct facility_id)
        from sc_tunnel_check c
        inner join sc_facility_info f on f.id = c.facility_id
        inner join sc_check_enum e on e.id = c.item_id
        where c.tunnel_id=#{tunnelId} and c.item_id is not null
        and f.part_code=#{partCode}
        and f.item_code=#{itemCode}
        and e.question_desc=#{questionDesc}
        and c.recheck_status = 0
    </select>
    <select id="getLastTunnelCheckByTunnelId" resultType="com.tunnel.domain.TunnelCheck">
        select * from sc_tunnel_check c
        where tunnel_id = #{tunnelId} and check_facility_ids is not null
        and c.recheck_status = 0
        order by create_time desc
        limit 1
    </select>
    <select id="selectListByTunnelIds" resultType="com.tunnel.domain.TunnelCheck">
        select c.*,e.*,s.tunnel_name tunnelName,s.tunnel_code tunnelCode,s.company_name companyName,s.road_code roadCode
        from sc_tunnel_check  c
        inner join sc_check_enum e on e.id = c.item_id
        inner join sc_tunnel_info s on s.id = c.tunnel_id
        where c.tunnel_id in
        <foreach item="id" collection="tunnelIds" separator="," open="(" close=")">
            #{id}
        </foreach>
        and c.recheck_status = 0
    </select>

    <!-- 批量更新复核状态 -->
    <update id="batchUpdateCheckStatus">
        update sc_tunnel_check 
        set check_status = #{checkStatus},
            check_remark = #{checkRemark},
            modifier = #{modifier}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateRecheckStatus">
        update sc_tunnel_check
        <set>
            modifier = #{modifier},
        <if test="recheckStatus != null">recheck_status = #{recheckStatus},</if>
        <if test="recheckId != null">recheck_id = #{recheckId},</if>
        </set>
        where id = #{id}
    </update>
</mapper>