<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.FacilityInfoMapper">
    
    <sql id="selectFacilityInfoVo">
        select id, tunnel_id, check_code, part_name, part_code, item_name, item_code,name,
               location, location_name,equip_code, unit, pic_url, remark, create_time, update_time, resistor
        from sc_facility_info
    </sql>

    <select id="selectDistinctFacilityInfoList" parameterType="FacilityInfo" resultType="FacilityInfo">
        select distinct f.tunnel_id,f.part_code,f.part_name,f.item_code,f.item_name,f.location,f.location_name,
               t.tunnel_code,t.tunnel_name
        from sc_facility_info f
        inner join sc_tunnel_info t on f.tunnel_id = t.id
        <where>
            <if test="roadName != null"> and t.road_name = #{roadName}</if>
            <if test="tunnelId != null"> and t.id = #{tunnelId}</if>
            <if test="type != null"> and f.type = #{type}</if>
            <if test="roadCode != null  and roadCode != ''"> and t.road_code = #{roadCode}</if>
            <if test="checkCode != null  and checkCode != ''"> and f.check_code = #{checkCode}</if>
            <if test="partName != null  and partName != ''"> and f.part_name like concat('%', #{partName}, '%')</if>
            <if test="partCode != null  and partCode != ''"> and f.part_code = #{partCode}</if>
            <if test="itemName != null  and itemName != ''"> and f.item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and f.item_code = #{itemCode}</if>
            <if test="location != null  and location != ''"> and f.location = #{location}</if>
            <if test="equipCode != null  and equipCode != ''"> and f.equip_code = #{equipCode}</if>
            <if test="creator != null"> and f.creator = #{creator}</if>
            <if test="code != null and code != ''"> and f.code = #{code}</if>
        </where>
        order by f.tunnel_id desc,f.part_code asc
    </select>



    <select id="selectFacilityInfoList" parameterType="FacilityInfo" resultType="FacilityInfo">
        select f.*,
        t.tunnel_code,t.tunnel_name
        from sc_facility_info f
        inner join sc_tunnel_info t on f.tunnel_id = t.id
        <where>
            <if test="tunnelId != null"> and t.id = #{tunnelId}</if>
            <if test="type != null"> and f.type = #{type}</if>
            <if test="checkCode != null  and checkCode != ''"> and f.check_code = #{checkCode}</if>
            <if test="partName != null  and partName != ''"> and f.part_name like concat('%', #{partName}, '%')</if>
            <if test="partCode != null  and partCode != ''"> and f.part_code = #{partCode}</if>
            <if test="itemName != null  and itemName != ''"> and f.item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and f.item_code = #{itemCode}</if>
            <if test="location != null  and location != ''"> and f.location = #{location}</if>
            <if test="equipCode != null  and equipCode != ''"> and f.equip_code = #{equipCode}</if>
            <if test="creator != null"> and f.creator = #{creator}</if>
            <if test="code != null and code != ''"> and f.code = #{code}</if>
        </where>
        order by f.part_code asc
    </select>
    
    <select id="selectFacilityInfoById" parameterType="Long" resultType="FacilityInfo">
        select f.*,t.tunnel_code,t.tunnel_name from sc_facility_info f
        inner join sc_tunnel_info t on f.tunnel_id = t.id
        where f.id=#{id}
        order by f.id desc
    </select>
    <select id="selectDistinctPart" resultType="com.tunnel.domain.FacilityInfo">
        select distinct part_name, part_code
        from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
        </where>
        order by part_code asc
    </select>
    <select id="selectDistinctItem" resultType="com.tunnel.domain.FacilityInfo">
        select distinct item_name, item_code
        from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="location != null and location != ''">
                and location=#{location}
            </if>
        </where>
    </select>
    <select id="selectDistinctLocation" resultType="com.tunnel.domain.FacilityInfo">
        select distinct location, location_name
        from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id=#{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code=#{itemCode}
            </if>
        </where>
    </select>

    <select id="selectDistinctCode" resultType="com.tunnel.domain.FacilityInfo">
        select distinct code,status from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code=#{itemCode}
            </if>
            <if test="location != null and location != ''">
                and location=#{location}
            </if>
        </where>
        order by code asc
    </select>
    <select id="selectDistinctName" resultType="com.tunnel.domain.FacilityInfo">
        select * from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code=#{itemCode}
            </if>
            <if test="code != null and code != ''">
                and code=#{code}
            </if>
             and location in
            <foreach item="item" collection="locationList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        limit 1
    </select>

    <select id="selectCountDistinctName" resultType="java.lang.Integer">
        select count(distinct name) from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code=#{itemCode}
            </if>
            <if test="locationList != null and locationList.size()>0">
                and location in
                <foreach item="item" collection="locationList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and name is not null
        </where>
    </select>


    <select id="countCodeByParams" resultType="java.lang.Integer">
        select count(1) from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code=#{itemCode}
            </if>
            <if test="location != null and location != ''">
                and location=#{location}
            </if>
        </where>
        order by code desc limit 1
    </select>


    <select id="selectMaxCodeByParams" resultType="FacilityInfo">
        select * from sc_facility_info
        <where>
            <if test="tunnelId != null">
                and tunnel_id = #{tunnelId}
            </if>
            <if test="partCode != null and partCode != ''">
                and part_code=#{partCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code=#{itemCode}
            </if>
            <if test="location != null and location != ''">
                and location=#{location}
            </if>
        </where>
        order by code desc limit 1
    </select>

    <select id="selectByParams" resultType="com.tunnel.domain.FacilityInfo">
        select * from sc_facility_info
        where tunnel_id = #{tunnelId}
          and part_code = #{partCode}
          and item_code = #{itemCode}
          and location = #{location}
    </select>
    <select id="selectHoleLocationByTunnelId" resultType="com.tunnel.domain.FacilityInfo">
        select distinct location from sc_facility_info
        where tunnel_id = #{tunnelId}
        and location_name = #{locationName}
        order by location asc
    </select>
    <select id="selectByEquipCode" resultType="com.tunnel.domain.FacilityInfo">
        select * from sc_facility_info
        where equip_code = #{equipCode}
    </select>
    <select id="selectByIdList" resultType="com.tunnel.domain.FacilityInfo">
        select * from sc_facility_info
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.facilityId}
        </foreach>
    </select>
    <select id="selectListByTunnelId" resultType="com.tunnel.domain.FacilityInfo">
        select * from sc_facility_info
        where tunnel_id = #{tunnelId}
    </select>
    <select id="selectListByTunnelIdGroupByItemCode" resultType="com.tunnel.domain.FacilityInfo">
        select count(1) num ,i.part_code,i.item_code,i.part_name,i.item_name,e.unit
        from sc_facility_info i
        left join (select distinct part_code,item_code,unit from sc_check_enum) e on i.part_code=e.part_code and i.item_code=e.item_code
        where tunnel_id = #{tunnelId}
        group by part_code,item_code,part_name,item_name,unit
        order by part_code,item_code asc
    </select>
    <select id="selectDistinctPartList" resultType="java.lang.String">
        select distinct part_name from sc_facility_info
        where tunnel_id in
        <foreach item="item" collection="tunnelIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by part_code asc
    </select>

    <insert id="insertFacilityInfo" parameterType="FacilityInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sc_facility_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tunnelId != null and tunnelId != ''">tunnel_id,</if>
            <if test="checkCode != null and checkCode != ''">check_code,</if>
            <if test="partName != null and partName != ''">part_name,</if>
            <if test="partCode != null and partCode != ''">part_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="locationName != null and locationName != ''">location_name,</if>
            <if test="name != null">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="equipCode != null and equipCode != ''">equip_code,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="picUrl != null and picUrl != ''">pic_url,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="resistor != null">resistor,</if>
            <if test="creator != null">creator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tunnelId != null and tunnelId != ''">#{tunnelId},</if>
            <if test="checkCode != null and checkCode != ''">#{checkCode},</if>
            <if test="partName != null and partName != ''">#{partName},</if>
            <if test="partCode != null and partCode != ''">#{partCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="locationName != null and locationName != ''">#{locationName},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="equipCode != null and equipCode != ''">#{equipCode},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="picUrl != null and picUrl != ''">#{picUrl},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="resistor != null">#{resistor},</if>
            <if test="creator != null">#{creator},</if>
         </trim>
    </insert>
    <insert id="batchAdd">
        insert into sc_facility_info
        (tunnel_id, check_code, part_name, part_code, item_name, item_code, location, name, code, equip_code, unit, remark, location_name, resistor,type
        )
        values
        <foreach collection="dataList" item="item" separator=",">
            (
            #{item.tunnelId},#{item.checkCode},#{item.partName},#{item.partCode},#{item.itemName},#{item.itemCode},#{item.location},#{item.name},#{item.code},#{item.equipCode}
            ,#{item.unit},#{item.remark},#{item.locationName},#{item.resistor},#{item.type}
            )
        </foreach>
    </insert>

    <update id="updateFacilityInfo" parameterType="FacilityInfo">
        update sc_facility_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="tunnelId != null and tunnelId != ''">tunnel_id = #{tunnelId},</if>
            <if test="checkCode != null and checkCode != ''">check_code = #{checkCode},</if>
            <if test="partName != null and partName != ''">part_name = #{partName},</if>
            <if test="partCode != null and partCode != ''">part_code = #{partCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="locationName != null and locationName != ''">location_name = #{locationName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="equipCode != null and equipCode != ''">equip_code = #{equipCode},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="picUrl != null and picUrl != ''">pic_url = #{picUrl},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="resistor != null">resistor = #{resistor},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdateByFacilityInfoList" parameterType="java.util.List">
        update sc_facility_info
        <set>
            check_code =
            <foreach collection="list" item="item" separator=" " open="case id" close="end">
                when #{item.id} then #{item.checkCode}
            </foreach>
        </set>
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="updateStatusById">
        update sc_facility_info
        set status = #{status},resistor = #{resistor}
        <if test="selectAll !=1">
          ,name = #{name}
        </if>
        <if test="checkCode !=null">
            ,check_code = #{checkCode}
        </if>
        where id = #{id}
    </update>

    <delete id="deleteFacilityInfoById" parameterType="Long">
        delete from sc_facility_info where id = #{id}
    </delete>

    <delete id="deleteFacilityInfoByIds" parameterType="String">
        delete from sc_facility_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByParams">
        delete from sc_facility_info
        where tunnel_id = #{tunnelId}
        and part_code = #{partCode}
        and item_code = #{itemCode}
        and location = #{location}
        and code = #{code}
    </delete>
    <delete id="deleteFacilityInfoByList">
        delete from sc_facility_info where
         (tunnel_id, part_code, item_code, location)
         in
        <foreach collection="list" item="item" open="(" separator="," close=")">
               (#{item.tunnelId},  #{item.partCode}, #{item.itemCode}, #{item.location})
        </foreach>
    </delete>

    <select id="countFacilityByRoadNameAndPartName" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sc_facility_info f
        INNER JOIN sc_tunnel_info t ON f.tunnel_id = t.id
        WHERE t.road_name = #{roadName}
        AND f.part_name = #{partName}
    </select>

    <select id="countFacilityByRoadNameGroupByPart" resultType="com.tunnel.domain.FacilityPartCount">
        SELECT f.part_name as partName,f.item_name as itemName, count(1) as count
        FROM sc_facility_info f
        INNER JOIN sc_tunnel_info t ON f.tunnel_id = t.id
        WHERE t.road_name = #{roadName}
        GROUP BY f.part_name,f.item_name
        ORDER BY count DESC
    </select>
</mapper>