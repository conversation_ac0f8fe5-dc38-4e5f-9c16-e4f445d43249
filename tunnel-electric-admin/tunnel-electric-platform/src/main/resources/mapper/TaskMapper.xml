<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.TaskMapper">
    
    <resultMap type="Task" id="TaskResult">
        <result property="id"    column="id"    />
        <result property="roadName"    column="road_name"    />
        <result property="roadCode"    column="road_code"    />
        <result property="tunnelIds"    column="tunnel_ids"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <result property="creatorName"    column="creator_name"    />
    </resultMap>

    <resultMap type="com.tunnel.domain.CheckFacilityRecord" id="CheckFacilityRecordResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="facilityId"    column="facility_id"    />
        <result property="beforeStatus"    column="before_status"    />
        <result property="useStatus"    column="use_status"    />
        <result property="afterStatus"    column="after_status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <result property="facilityName"    column="facility_name"    />
        <result property="facilityModel"    column="facility_model"    />
        <result property="applyType"    column="applyType"    />
    </resultMap>

    <sql id="selectTaskVo">
        select id, road_name, tunnel_ids, status, remark, create_time, update_time, creator, modifier from sc_task
    </sql>

    <select id="selectTaskList" parameterType="Task" resultMap="TaskResult">
        select t.id, t.road_name, t.tunnel_ids, t.status, t.remark, t.create_time, t.update_time, t.creator, t.modifier,
               u.nick_name as creator_name,
               ti.road_code
        from sc_task t
        left join sys_user u on t.creator = u.user_id
        left join sc_tunnel_info ti on ti.road_name = t.road_name
        <where>  
            <if test="roadName != null and roadName != ''"> and t.road_name like concat('%', #{roadName}, '%')</if>
            <if test="tunnelIds != null and tunnelIds != ''"> and t.tunnel_ids like concat('%', #{tunnelIds}, '%')</if>
            <if test="status != null"> and t.status = #{status}</if>
            <if test="creator != null"> and t.creator = #{creator}</if>
            <if test="loginUserId != null">
                and (t.creator = #{loginUserId} or  exists (select 1 from sc_task_user tu where tu.task_id = t.id and tu.user_id = #{loginUserId}))
            </if>
        </where>
        group by t.id, t.road_name, t.tunnel_ids, t.status, t.remark, t.create_time, t.update_time, t.creator, t.modifier, u.nick_name, ti.road_code
        order by t.create_time desc
    </select>
    
    <select id="selectTaskById" parameterType="Long" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where id = #{id}
    </select>

    <select id="selectRoadList" resultType="TunnelInfo">
        select distinct road_code as roadCode, road_name as roadName from sc_tunnel_info
    </select>

    <select id="selectTunnelListByRoadCode" parameterType="String" resultType="TunnelInfo">
        select id as tunnelId, tunnel_name as tunnelName from sc_tunnel_info where road_name = #{roadName}
    </select>

    <select id="selectUserList" resultType="SysUser">
        select distinct u.nick_name as nickName, u.user_id as userId
        from sys_user u
        inner join sys_user_role r on u.user_id = r.user_id 
        inner join sys_role s on r.role_id = s.role_id 
        where (s.role_key like '%jilu%' or s.role_key like '%jiance%' or s.role_key like '%fuhe%')
          and u.del_flag='0' and s.del_flag='0'
    </select>

    <select id="selectTunnelDetailsByIds" parameterType="String" resultType="TunnelInfo">
        select 
            t.id as id,
            t.road_name as roadName,
            t.road_code as roadCode,
            t.tunnel_name as tunnelName,
            t.tunnel_type as tunnelType,
            t.company_name as companyName
        from sc_tunnel_info t
        where FIND_IN_SET(t.id, #{tunnelIds})
        order by t.id
    </select>

    <insert id="insertTask" parameterType="Task" useGeneratedKeys="true" keyProperty="id">
        insert into sc_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="originalId != null and originalId != ''">original_id,</if>
            <if test="roadName != null and roadName != ''">road_name,</if>
            <if test="tunnelIds != null and tunnelIds != ''">tunnel_ids,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="creator != null">creator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="originalId != null and originalId != ''">#{originalId},</if>
            <if test="roadName != null and roadName != ''">#{roadName},</if>
            <if test="tunnelIds != null and tunnelIds != ''">#{tunnelIds},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creator != null">#{creator},</if>
         </trim>
    </insert>

    <update id="updateTask" parameterType="Task">
        update sc_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskById" parameterType="Long">
        delete from sc_task where id = #{id}
    </delete>

    <delete id="deleteTaskByIds" parameterType="String">
        delete from sc_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFacilities" resultType="CheckFacility">
        select  *
        from sc_check_facility
        order by id
    </select>

    <select id="selectInFacilities" parameterType="Long" resultType="CheckFacility">
        select f.*
        from sc_check_facility f
        where f.id in (
            select distinct cfr.facility_id
            from sc_check_facility_record cfr
            inner join sc_check_facility_apply cfa on cfr.apply_id = cfa.id
            where cfa.type = 1 and cfa.task_id = #{taskId}
        )
        order by f.id
    </select>

    <select id="countFacilityApplyByTaskId" parameterType="Long" resultType="int">
        select count(*) from sc_check_facility_apply where task_id = #{taskId}
    </select>

    <select id="selectFacilityStatusList" parameterType="Long" resultMap="CheckFacilityRecordResult">
        select 
          distinct r.id,
            r.task_id,
            r.facility_id,
            r.before_status,
            r.use_status,
            r.after_status,
            r.remark,
            r.create_time,
            r.update_time,
            r.creator,
            r.modifier,
            f.name as facility_name,
            f.model as facility_model,
            a.type as applyType
        from sc_check_facility_record r
        left join sc_check_facility_apply a on r.apply_id = a.id
        left join sc_check_facility f on r.facility_id = f.id
        where r.task_id = #{taskId}
        order by r.id
    </select>

    <update id="updateFacilityRecord" parameterType="com.tunnel.domain.CheckFacilityRecord">
        update sc_check_facility_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="beforeStatus != null">before_status = #{beforeStatus},</if>
            <if test="useStatus != null">use_status = #{useStatus},</if>
            <if test="afterStatus != null">after_status = #{afterStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>
    
    <select id="selectTaskIdsByUserAndTunnel" resultType="Long">
        select distinct t.id
        from sc_task t
        left join sc_task_user tu on t.id = tu.task_id
        where (t.creator = #{userId} or tu.user_id = #{userId})
          and FIND_IN_SET(#{tunnelId}, t.tunnel_ids) > 0
    </select>

    <!-- 将任务的设备设置为入库状态 -->
    <update id="updateFacilityStatusToInStock" parameterType="Long">
        update sc_check_facility_record cfr
        inner join sc_check_facility_apply cfa on cfr.apply_id = cfa.id
        set cfr.after_status = 1
        where cfa.task_id = #{taskId}
          and cfa.type = 0
    </update>

    <!-- 查询任务的设备申请记录 -->
    <select id="selectFacilityApplyByTaskId" parameterType="Long" resultType="com.tunnel.domain.CheckFacilityApply">
        select *
        from sc_check_facility_apply
        where task_id = #{taskId} and type = 1 and status=1
    </select>

    <!-- 查询任务的设备记录 -->
    <select id="selectFacilityRecordByTaskId" parameterType="Long" resultType="com.tunnel.domain.CheckFacilityRecord">
        select cfr.id, cfr.task_id, cfr.facility_id, cfr.apply_id, cfr.before_status, cfr.use_status, cfr.after_status, 
               cfr.remark, cfr.create_time, cfr.update_time, cfr.creator, cfr.modifier
        from sc_check_facility_record cfr
        inner join sc_check_facility_apply cfa on cfr.apply_id = cfa.id
        where cfa.task_id = #{taskId} and cfa.type = 1
    </select>
    <select id="selectFacilityRecordByApplyId" resultType="com.tunnel.domain.CheckFacilityRecord">
        select *
        from sc_check_facility_record cfr
        where cfr.apply_id = #{applyId}
    </select>


</mapper>