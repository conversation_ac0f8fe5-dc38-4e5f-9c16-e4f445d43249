package com.tunnel.mapper;

import com.tunnel.domain.CheckEnumRelation;
import com.tunnel.domain.TunnelCheck;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机电设施检测Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface TunnelCheckMapper {
    /**
     * 查询机电设施检测
     *
     * @param id 机电设施检测主键
     * @return 机电设施检测
     */
    public TunnelCheck selectTunnelCheckById(Long id);

    /**
     * 查询机电设施检测列表
     *
     * @param tunnelCheck 机电设施检测
     * @return 机电设施检测集合
     */
    public List<TunnelCheck> selectTunnelCheckList(TunnelCheck tunnelCheck);

    List<TunnelCheck> selectTunnelCheckListAll(TunnelCheck tunnelCheck);


    /**
     * 查询机电设施检测列表总数
     *
     * @param tunnelCheck 机电设施检测
     * @return 总数
     */
    public Long selectTunnelCheckListCount(TunnelCheck tunnelCheck);

    /**
     * 新增机电设施检测
     *
     * @param tunnelCheck 机电设施检测
     * @return 结果
     */
    public int insertTunnelCheck(TunnelCheck tunnelCheck);

    /**
     * 修改机电设施检测
     *
     * @param tunnelCheck 机电设施检测
     * @return 结果
     */
    public int updateTunnelCheck(TunnelCheck tunnelCheck);

    /**
     * 删除机电设施检测
     *
     * @param id 机电设施检测主键
     * @return 结果
     */
    public int deleteTunnelCheckById(Long id);

    /**
     * 批量删除机电设施检测
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTunnelCheckByIds(Long[] ids);

    List<TunnelCheck> selectByFacilityId(Long tunnelId);

    int updateSuggestionById(TunnelCheck tunnelCheck);

    int deleteCheckByIds(@Param("idList") List<Long> idList);

    List<TunnelCheck> selectByParams(TunnelCheck queryCheck);

    List<TunnelCheck> selectListByTunnelId(Long tunnelId);

    /**
     * 查询机电设施检测明细列表
     * 
     * @param tunnelCheck 机电设施检测
     * @return 机电设施检测集合
     */
    public List<TunnelCheck> selectTunnelCheckDetailList(TunnelCheck tunnelCheck);

    /**
     * 查询机电设施检测明细总数
     * 
     * @param tunnelCheck 机电设施检测
     * @return 总数
     */
    public Long selectTunnelCheckDetailCount(TunnelCheck tunnelCheck);

    int updateCheckCodeByTunnelCheck(TunnelCheck check);

    TunnelCheck selectByTunnelCheck(TunnelCheck check);

    List<TunnelCheck> selectUnQuestionListByCheckEnumRelation(CheckEnumRelation checkEnumRelation);

    int selectGoodCheckNum(TunnelCheck resultCheck);

    int selectQuestionCheckNum(TunnelCheck resultCheck);

    /**
     * 批量更新复核状态
     * 
     * @param ids 检测记录ID列表
     * @param checkStatus 复核状态
     * @param checkRemark 检测条件
     * @param modifier 复核人员ID
     * @return 更新记录数
     */
    int batchUpdateCheckStatus(@Param("ids") List<Long> ids, @Param("checkStatus") Integer checkStatus, 
                              @Param("checkRemark") String checkRemark, @Param("modifier") Long modifier);

    TunnelCheck getLastTunnelCheckByTunnelId(Long tunnelId);

    List<TunnelCheck> selectListByTunnelIds(@Param("tunnelIds") List<Long> tunnelIds, @Param("taskId") Long taskId);

    int updateRecheckStatus(TunnelCheck temp);
}
