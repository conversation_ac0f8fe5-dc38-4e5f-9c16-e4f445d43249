package com.tunnel.mapper;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.domain.CheckFacility;
import com.tunnel.domain.CheckFacilityApply;
import com.tunnel.domain.CheckFacilityRecord;
import com.tunnel.domain.Task;
import com.tunnel.domain.TunnelInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface TaskMapper
{
    /**
     * 查询任务
     * 
     * @param id 任务主键
     * @return 任务
     */
    public Task selectTaskById(Long id);

    /**
     * 查询任务列表
     * 
     * @param task 任务
     * @return 任务集合
     */
    public List<Task> selectTaskList(Task task);

    /**
     * 新增任务
     * 
     * @param task 任务
     * @return 结果
     */
    public int insertTask(Task task);

    /**
     * 修改任务
     * 
     * @param task 任务
     * @return 结果
     */
    public int updateTask(Task task);

    /**
     * 删除任务
     * 
     * @param id 任务主键
     * @return 结果
     */
    public int deleteTaskById(Long id);

    /**
     * 批量删除任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskByIds(Long[] ids);

    /**
     * 查询路段列表
     * 
     * @return 路段集合
     */
    public List<TunnelInfo> selectRoadList();

    /**
     * 根据路段编号查询隧道列表
     * 
     * @param roadCode 路段编号
     * @return 隧道集合
     */
    public List<TunnelInfo> selectTunnelListByRoadCode(String roadCode);

    /**
     * 查询用户列表
     * 
     * @return 用户集合
     */
    public List<SysUser> selectUserList();

    /**
     * 根据隧道ID查询隧道详情
     * 
     * @param tunnelIds 隧道ID（多个用逗号分隔）
     * @return 隧道详情集合
     */
    public List<TunnelInfo> selectTunnelDetailsByIds(String tunnelIds);

    /**
     * 获取出库设备列表
     * 
     * @return 出库设备集合
     */
    public List<CheckFacility> selectFacilities();

    /**
     * 获取入库设备列表
     * 
     * @param taskId 任务ID
     * @return 入库设备集合
     */
    public List<CheckFacility> selectInFacilities(Long taskId);

    /**
     * 查询任务的设备申请数量
     * @param taskId 任务ID
     * @return 申请数量
     */
    public int countFacilityApplyByTaskId(Long taskId);

    /**
     * 获取设备状态列表
     * @param taskId 任务ID
     * @return 设备状态列表
     */
    public List<CheckFacilityRecord> selectFacilityStatusList(Long taskId);

    /**
     * 更新设备记录
     * @param record 设备记录
     * @return 更新结果
     */
    public int updateFacilityRecord(CheckFacilityRecord record);
    
    /**
     * 根据用户ID和隧道ID查询任务ID列表
     * @param userId 用户ID
     * @param tunnelId 隧道ID
     * @return 任务ID列表
     */
    public List<Long> selectTaskIdsByUserAndTunnel(@Param("userId") Long userId, @Param("tunnelId") Long tunnelId);

    /**
     * 将任务的设备设置为入库状态
     * @param taskId 任务ID
     * @return 更新结果
     */
    public int updateFacilityStatusToInStock(Long taskId);

    /**
     * 查询原任务的设备申请记录
     * @param taskId 任务ID
     * @return 设备申请记录列表
     */
    public List<CheckFacilityApply> selectFacilityApplyByTaskId(Long taskId);

    /**
     * 查询原任务的设备记录
     * @param taskId 任务ID
     * @return 设备记录列表
     */
    public List<CheckFacilityRecord> selectFacilityRecordByTaskId(Long taskId);

    List<CheckFacilityRecord> selectFacilityRecordByApplyId(Long applyId);
}