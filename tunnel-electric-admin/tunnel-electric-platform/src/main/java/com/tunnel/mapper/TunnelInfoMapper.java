package com.tunnel.mapper;

import com.tunnel.domain.TunnelInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 隧道信息详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface TunnelInfoMapper {
    /**
     * 查询隧道信息详情
     *
     * @param id 隧道信息详情主键
     * @return 隧道信息详情
     */
    public TunnelInfo selectTunnelInfoById(Long id);

    /**
     * 查询隧道信息详情列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情集合
     */
    public List<TunnelInfo> selectTunnelInfoList(TunnelInfo tunnelInfo);


    /**
     * 修改隧道信息详情
     *
     * @param tunnelInfo 隧道信息详情
     * @return 结果
     */
    public int updateTunnelInfo(TunnelInfo tunnelInfo);

    /**
     * 删除隧道信息详情
     *
     * @param id 隧道信息详情主键
     * @return 结果
     */
    public int deleteTunnelInfoById(Long id);

    /**
     * 批量删除隧道信息详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTunnelInfoByIds(Long[] ids);

    List<TunnelInfo> selectDistinctTunnelCode(TunnelInfo tunnelInfo);

    List<TunnelInfo> selectByTunnelCodes(@Param("list") List<String> tunnelCodeList);

    TunnelInfo selectByParams(@Param("tunnelId") Long tunnelId, @Param("direction") Integer direction);

    TunnelInfo selectByTunnelId(Long tunnelId);

    List<TunnelInfo> selectDistinctTunnelList(TunnelInfo tunnel);


    List<TunnelInfo> selectDistinctCompany(TunnelInfo tunnelInfo);

    List<TunnelInfo> selectDistinctRoad(TunnelInfo tunnelInfo);

    int batchAdd(@Param("dataList") List<TunnelInfo> dataList);

    List<TunnelInfo> listAll();

    List<TunnelInfo> listDistinctRoad();

    List<TunnelInfo> selectListByTunnelId(Long tunnelId);

    int updateTunnelInfoCompleteStatus(TunnelInfo tunnelInfo);

    List<TunnelInfo> selectListByRoadName(String tunnelName);

    List<TunnelInfo> selectTunnelByIds(String tunnelIds);

    List<TunnelInfo> selectListByRoadNameList(@Param("roadNameList") List<String> roadNameList);
    
    /**
     * 查询不重复的路线编码列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情集合
     */
    List<TunnelInfo> selectDistinctRoadCode(TunnelInfo tunnelInfo);
    
    /**
     * 查询不重复的隧道名称列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情集合
     */
    List<TunnelInfo> selectDistinctTunnelName(TunnelInfo tunnelInfo);
}
