package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 检测设备对象 sc_check_facility
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class CheckFacility extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 设备名称
     */
    @Excel(name = "检测设备")
    private String name;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号")
    @NotBlank(message = "设备型号不能为空")
    private String model;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    @NotBlank(message = "设备编号不能为空")
    private String code;

    /**
     * 主要用途
     */
    @Excel(name = "主要用途")
    @NotBlank(message = "主要用途不能为空")
    private String mainUsage;

    /**
     * 校准起始时间
     */
    @Excel(name = "校准日期")
    private String startPeriod;

    /**
     * 校准结束时间
     */
    @Excel(name = "下次校准日期")
    private String endPeriod;

    /**
     * 是否在库（1=是，0=否）
     */
    @Excel(name = "是否在库", readConverterExp = "1=是,0=否")
    @NotNull(message = "是否在库不能为空")
    private Integer flag;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 当前设备所属任务ID（非数据库字段，用于显示）
     */
    private Long currentTaskId;

    /**
     * 当前设备所属任务路段名称（非数据库字段，用于显示）
     */
    private String currentTaskRoadName;

}