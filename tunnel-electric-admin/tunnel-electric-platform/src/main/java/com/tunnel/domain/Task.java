package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 任务对象 task
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class Task extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 路段名称 */
    @Excel(name = "路段名称")
    @NotBlank(message = "路段名称不能为空")
    private String roadName;

    /** 路段编号（查询时使用） */
    private String roadCode;

    /** 原任务ID */
    private Long originalId;

    /** 隧道ID,多个使用逗号拼接 */
    @Excel(name = "隧道ID")
    @NotBlank(message = "隧道ID不能为空")
    private String tunnelIds;

    /** 任务状态:0.已派发;1.进行中,2.已完成 */
    @Excel(name = "任务状态", dictType = "task_status")
    @NotNull(message = "任务状态不能为空")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 登录用户ID（用于查询权限控制） */
    private Long loginUserId;

    /** 创建人姓名 */
    private String creatorName;

    /** 任务人员配置列表（查询详情时使用） */
    private List<TaskUser> taskUsers;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("roadName", getRoadName())
            .append("tunnelIds", getTunnelIds())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("creator", getCreator())
            .append("modifier", getModifier())
            .toString();
    }
}