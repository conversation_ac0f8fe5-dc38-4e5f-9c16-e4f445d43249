package com.tunnel.domain;

import lombok.Data;

/**
 * 分部设备数量统计对象
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
public class FacilityPartCount {
    
    /**
     * 分部名称
     */
    private String partName;
    
    /**
     * 分项名称
     */
    private String itemName;
    
    /**
     * 设备数量
     */
    private Integer count;
    
    public FacilityPartCount() {
    }
    
    public FacilityPartCount(String partName, Integer count) {
        this.partName = partName;
        this.count = count;
    }
    
    public FacilityPartCount(String partName, String itemName, Integer count) {
        this.partName = partName;
        this.itemName = itemName;
        this.count = count;
    }
}
