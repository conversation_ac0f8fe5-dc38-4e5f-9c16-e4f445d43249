package com.tunnel.domain;

import java.math.BigDecimal;
import java.util.List;

import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import com.tunnel.common.annotation.Excel;

/**
 * 机电设施检测对象 sc_tunnel_check
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
public class TunnelCheck extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    private Long id;

    /**
     * 检测年份
     */
    private String checkYear;
    /**
     * 隧道表ID
     */
//    @Excel(name = "隧道表ID")
    private Long tunnelId;
    /**
     * 隧道设施表ID
     */
//    @Excel(name = "隧道设施表ID")
    private Long facilityId;

    /**
     * 检测病害表ID
     */
//    @Excel(name = "检测病害表ID")
    private Long itemId;

    /**
     * 缺陷照片1
     */
//    @Excel(name = "缺陷照片1")
    private String picUrl1;

    /**
     * 缺陷照片2
     */
//    @Excel(name = "缺陷照片2")
    private String picUrl2;

    /**
     * 缺陷照片3
     */
//    @Excel(name = "缺陷照片3")
    private String picUrl3;
    /**
     * 标段
     */
    private String section;
    /**
     * 隧道名称
     */
    @Excel(name = "隧道名称")
    private String tunnelName;
    /**
     * 单位名称
     */
    private String companyName;
    /**
     * 道路编号
     */
    private String roadCode;

    private String roadName;
    /**
     * 隧道编号
     */
    @Excel(name = "隧道编号")
    private String tunnelCode;
    /**
     * 桩号
     */
    @Excel(name = "桩号")
    private String checkCode;
    /**
     * 分部名称
     */
    @Excel(name = "分部名称")
    private String partName;
    /**
     * 分部code
     */
    @Excel(name = "分部编号")
    private String partCode;

    @Excel(name = "分项名称")
    private String itemName;
    /**
     * 分项code
     */
    @Excel(name = "分项编号")
    private String itemCode;
    /**
     * 位置
     */
    @Excel(name = "位置编号")
    private String location;

    /**
     * 设备状态:0.初始化 1.正常 2.异常
     */
    private Integer status;

    /**
     * 分类:1.人检,2.车检
     */
    private Integer type;

    /**
     * 位置名称
     */
    @Excel(name = "位置名称")
    private String locationName;
    /**
     * 编号
     */
    @Excel(name = "设备编号")
    private String code;

    @Excel(name = "缺陷描述")
    private String questionDesc;
    /**
     * 设备编号
     */
    private String equipCode;

    /**
     * 权重
     */
    private BigDecimal weight;

    @Excel(name = "缺陷详情")
    private String checkContent;

    /**
     * 分数
     */
    @Excel(name = "扣分值")
    private BigDecimal score;

//    @Excel(name = "设备数量")
    private Integer num=1;

//    @Excel(name = "设备名称")
    private String name;

//    @Excel(name = "电阻值")
    private BigDecimal resistor;

    /**
     * 工作照1
     */
    private String workPic1;
    /**
     * 工作照2
     */
    private String workPic2;
    /**
     * 工作照3
     */
    private String workPic3;
    /**
     * 隧道名称或编码
     */
    private String tunnelCodeOrName;
    /**
     * 经度
     */
    @Excel(name = "经度")
    private String  longitude;
    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String  latitude;
    /**
     * 地址
     */
    @Excel(name = "地址")
    private String  address;

    @Excel(name = "缺陷设备数量")
    private Integer questionNum;

    @Excel(name = "设备总量")
    private Integer facilityNum;

//    @Excel(name = "设备总量")
    /**
     * 正常设备数量
     */
    private Integer goodFacilityNum;

    @Excel(name = "单位")
    private String unit;

    @Excel(name = "建议措施")
    private String suggestion;
    
    /**
     * 检测设备id,多个使用逗号拼接
     */
    private String checkFacilityIds;
    
    /**
     * 检测规范,多个使用逗号拼接
     */
    private String standardIds;
    
    /**
     * 最低温度(℃)
     */
    private BigDecimal lowTemperature;
    
    /**
     * 最高温度(℃)
     */
    private BigDecimal highTemperature;
    
    /**
     * 最低湿度(%RH)
     */
    private BigDecimal lowHumidity;
    
    /**
     * 最高湿度(%RH)
     */
    private BigDecimal highHumidity;
    
    /**
     * 开始检测时间
     */
    @Excel(name = "开始检测时间")
    private String startTime;
    
    /**
     * 结束检测时间
     */
    @Excel(name = "结束检测时间")
    private String endTime;

    @Excel(name = "备注")
    private String remark;

    private List<Long> tunnelList;


    private List<Long> ids;

    private Integer groupBy;

    private Integer export;

    /**
     * 设备损坏比:灯具损坏比
     */
    private BigDecimal rate;

    /**
     * 复核状态:0.待复核;1.已复核
     */
    @Excel(name = "复核状态")
    private Integer checkStatus;

    /**
     * 检测条件
     */
    @Excel(name = "检测条件")
    private String checkRemark;

    /**
     * 复核人员姓名
     */
    @Excel(name = "复核人员")
    private String reviewerName;

    /**
     * 检测人员姓名
     */
    @Excel(name = "检测人员")
    private String checkUserName;

    /**
     * 是否全选
     */
    private Integer selectAll;

    /**
     * 是否复检后隐藏:0.不隐藏,1.隐藏
     */
    private Integer recheckStatus;

    /**
     * 复检记录ID（指向新生成的复检记录）
     */
    private Long recheckId;

    /**
     * 源检测记录ID（复检记录指向原始记录）
     */
    private Long sourceId;

}
