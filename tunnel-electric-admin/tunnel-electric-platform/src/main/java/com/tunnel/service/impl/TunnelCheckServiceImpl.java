package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.file.AliYunOSSFileUtil;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.*;
import com.tunnel.mapper.*;
import com.tunnel.service.TunnelCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机电设施检测Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Service
@Slf4j
public class TunnelCheckServiceImpl implements TunnelCheckService {
    @Autowired
    private TunnelCheckMapper tunnelCheckMapper;
    @Autowired
    private CheckEnumMapper checkEnumMapper;
    @Autowired
    private FacilityInfoMapper facilityInfoMapper;


    //base64文件前缀
    public static final String DATA_IMAGE = "data:image";
    @Autowired
    private TunnelInfoMapper tunnelInfoMapper;
    @Autowired
    private CheckFacilityMapper checkFacilityMapper;
    /**
     * 查询机电设施检测
     *
     * @param id 机电设施检测主键
     * @return 机电设施检测
     */
    @Override
    public TunnelCheck selectTunnelCheckById(Long id) {
        return tunnelCheckMapper.selectTunnelCheckById(id);
    }

    /**
     * 查询机电设施检测列表
     *
     * @param tunnelCheck 机电设施检测
     * @return 机电设施检测
     */
    @Override
    public List<TunnelCheck> selectTunnelCheckList(TunnelCheck tunnelCheck) {
        // Apply user-based filtering for recheck status
        applyUserBasedRecheckFilter(tunnelCheck);
        
        List<TunnelCheck> list;
        if(Objects.equals(tunnelCheck.getExport(),1)){
            list = tunnelCheckMapper.selectTunnelCheckList(tunnelCheck);
        }else {
            list = tunnelCheckMapper.selectTunnelCheckListAll(tunnelCheck);
        }
//        list=list.stream().filter(v -> Objects.equals("水泵故障无法启动",v.getQuestionDesc())).collect(Collectors.toList());
        // 使用tunnelCode+partCode+itemCode+location进行分组
        List<TunnelCheck> resultList=Lists.newArrayList();
        for (TunnelCheck resultCheck : list) {
            // 查询设备信息
            FacilityInfo facilityInfo = new FacilityInfo();
            facilityInfo.setTunnelId(resultCheck.getTunnelId());
            facilityInfo.setItemCode(resultCheck.getItemCode());
            facilityInfo.setPartCode(resultCheck.getPartCode());
            // 获取设备数量
            int temp = facilityInfoMapper.countCodeByParams(facilityInfo);
            resultCheck.setFacilityNum(temp);
            //查询对应的检测项为空的数据
            if(!Objects.equals(tunnelCheck.getExport(),1)) {
                int goodNum = tunnelCheckMapper.selectGoodCheckNum(resultCheck);
                resultCheck.setGoodFacilityNum(goodNum);
                int questionNum = tunnelCheckMapper.selectQuestionCheckNum(resultCheck);
                resultCheck.setQuestionNum(questionNum);
            }
            if(Objects.nonNull(resultCheck.getItemId())){
                tunnelCheck.setGroupBy(null);
                //s.tunnel_code,f.part_code,f.item_code,e.question_desc
                TunnelCheck check = new TunnelCheck();
                check.setTunnelId(resultCheck.getTunnelId());
                check.setPartCode(resultCheck.getPartCode());
                check.setItemCode(resultCheck.getItemCode());
                check.setQuestionDesc(resultCheck.getQuestionDesc());
                List<TunnelCheck> tempList = tunnelCheckMapper.selectTunnelCheckList(check);
                List<String> remarkList = tempList.stream().map(TunnelCheck::getRemark).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(remarkList)){
                    resultCheck.setRemark(String.join(",", remarkList));
                }else{
                    resultCheck.setRemark("");
                }
                List<FacilityInfo>  listInfo= facilityInfoMapper.selectByIdList(tempList);
                String checkContent=this.formatCheckCode(listInfo,resultCheck.getQuestionDesc(),tempList,true);
                resultCheck.setCheckContent(checkContent);
            }
            resultList.add(resultCheck);
        }
        
        // Sort resultList by tunnelName, partName, itemName, and questionDesc
        Collections.sort(resultList, (a, b) -> {
            // First compare by tunnelName
            int tunnelCompare = nullSafeCompare(a.getTunnelName(), b.getTunnelName());
            if (tunnelCompare != 0) return tunnelCompare;
            
            // Then compare by partName
            int partCompare = nullSafeCompare(a.getPartName(), b.getPartName());
            if (partCompare != 0) return partCompare;
            
            // Then compare by itemName
            int itemCompare = nullSafeCompare(a.getItemName(), b.getItemName());
            if (itemCompare != 0) return itemCompare;
            
            // Finally compare by questionDesc
            return nullSafeCompare(a.getQuestionDesc(), b.getQuestionDesc());
        });

        return resultList;
    }




    /**
     * 格式化位置名称，将"上行洞室"或"下行洞室"格式化为"上行X号洞室"
     * 同时处理数据异常情况，去掉"上行X号洞室#"或"下行X号洞室#"中的#符号
     * @param content 需要格式化的内容
     * @return 格式化后的内容
     */
    private static String formatLocationName(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        // 处理上行洞室
        StringBuilder result = new StringBuilder(content);
        int index = 0;
        while ((index = result.indexOf("上行洞室", index)) != -1) {
            // 查找洞室后面的数字
            int numStart = index + "上行洞室".length();
            StringBuilder numStr = new StringBuilder();
            while (numStart < result.length() && Character.isDigit(result.charAt(numStart))) {
                numStr.append(result.charAt(numStart));
                numStart++;
            }
            // 如果没有数字，默认为1
            String number = numStr.length() > 0 ? numStr.toString() : "1";
            // 替换文本
            result.replace(index, index + "上行洞室".length() + numStr.length(), "上行" + number + "号洞室");
            // 更新索引位置
            index += ("上行" + number + "号洞室").length();
        }

        // 处理下行洞室
        index = 0;
        while ((index = result.indexOf("下行洞室", index)) != -1) {
            // 查找洞室后面的数字
            int numStart = index + "下行洞室".length();
            StringBuilder numStr = new StringBuilder();
            while (numStart < result.length() && Character.isDigit(result.charAt(numStart))) {
                numStr.append(result.charAt(numStart));
                numStart++;
            }
            // 如果没有数字，默认为1
            String number = numStr.length() > 0 ? numStr.toString() : "1";
            // 替换文本
            result.replace(index, index + "下行洞室".length() + numStr.length(), "下行" + number + "号洞室");
            // 更新索引位置
            index += ("下行" + number + "号洞室").length();
        }

        // 处理轴流风机房
        index = 0;
        while ((index = result.indexOf("轴流风机房", index)) != -1) {
            // 查找洞室后面的数字
            int numStart = index + "轴流风机房".length();
            StringBuilder numStr = new StringBuilder();
            while (numStart < result.length() && Character.isDigit(result.charAt(numStart))) {
                numStr.append(result.charAt(numStart));
                numStart++;
            }
            // 如果没有数字，默认为1
            String number = numStr.length() > 0 ? numStr.toString() : "1";
            // 替换文本
            result.replace(index, index + "轴流风机房".length() + numStr.length(),number + "号轴流风机房");
            // 更新索引位置
            index += (number + "号轴流风机房").length();
        }
        return result.toString();
    }

    private int nullSafeCompare(String str1, String str2) {
        if (str1 == null && str2 == null) return 0;
        if (str1 == null) return -1;
        if (str2 == null) return 1;
        return str1.compareTo(str2);
    }

    /**
     * 拼装缺陷描述:设置范围+设备编号+缺陷描述
     * 根据图片规则：
     * 有设备名称：
     *   - 检测点连续：位置+检测点编号1~检测点编号3、位置+检测点编号6~检测点编号10......+空格+设备名称
     *   - 检测点不连续：位置+检测点编号1+空格+设备名称1、位置+检测点编号3+空格+设备名称3、位置+检测点编号8+空格+设备名称8......
     * 无设备名称：
     *   - 检测点连续：位置+检测点编号1~检测点编号3、位置+检测点编号6~检测点编号10......+空格+分项名称
     *   - 检测点不连续：位置+检测点编号1+空格+分项名称、位置+检测点编号5+空格+分项名称、位置+检测点编号8~12+空格+分项名称
     * @param listInfo 设备信息列表
     * @param questionDesc 问题描述
     * @param checkList 检测列表
     * @param num 是否显示编号
     * @return 格式化后的检测代码
     */
    @Override
    public String formatCheckCode(List<FacilityInfo> listInfo, String questionDesc, List<TunnelCheck> checkList, boolean num) {
        if (listInfo == null || listInfo.isEmpty()) {
            return questionDesc;
        }
        
        StringBuilder checkContent = new StringBuilder();
        
        // 按照位置和设备编号排序
        Collections.sort(listInfo, (a, b) -> {
            String locA = a.getLocation();
            String locB = b.getLocation();
            int locationCompare = nullSafeCompare(locA, locB);
            if (locationCompare != 0) return locationCompare;
            return nullSafeCompare(a.getCode(), b.getCode());
        });
        
        // 按照位置代码进行分组
        Map<String, List<FacilityInfo>> locationGroups = new LinkedHashMap<>();
        for (FacilityInfo info : listInfo) {
            String displayKey = getLocationDisplayKey(info);
            locationGroups.computeIfAbsent(displayKey, k -> new ArrayList<>()).add(info);
        }
        
        // 获取备注信息
        List<String> remarkList = checkList.stream()
                .map(TunnelCheck::getRemark)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        String remark = CollectionUtils.isEmpty(remarkList) ? "" : String.join(",", remarkList);
        
        // 处理每个位置及其设备
        for (Map.Entry<String, List<FacilityInfo>> entry : locationGroups.entrySet()) {
            List<FacilityInfo> facilities = entry.getValue();
            String locationDisplayName = getLocationDisplayName(facilities.get(0));
            
            // 对设备按编号排序
            Collections.sort(facilities, (a, b) -> {
                try {
                    return Integer.parseInt(a.getCode()) - Integer.parseInt(b.getCode());
                } catch (NumberFormatException e) {
                    return a.getCode().compareTo(b.getCode());
                }
            });
            
            // 检查是否有设备名称（由于分组KEY已经包含设备名称，同一组内的设备名称应该是一致的）
            boolean hasDeviceName = facilities.stream().anyMatch(f -> StringUtils.isNotBlank(f.getName()));
            
            if (hasDeviceName) {
                // 有设备名称的处理逻辑 - 同一组内设备名称相同
                String deviceName = facilities.stream()
                        .filter(f -> StringUtils.isNotBlank(f.getName()))
                        .findFirst()
                        .map(FacilityInfo::getName)
                        .orElse("");
                
                processDeviceGroup(checkContent, facilities, locationDisplayName, deviceName);
            } else {
                // 无设备名称的处理逻辑
                String itemName = facilities.get(0).getItemName();
                processDeviceGroup(checkContent, facilities, locationDisplayName, itemName);
            }
        }
        
        // 移除末尾的分隔符
        String content = checkContent.toString();
        if (content.endsWith("、")) {
            content = content.substring(0, content.length() - 1);
        }
        
        // 添加问题描述
        if (StringUtils.isNotBlank(questionDesc)) {
            content = content + " " + questionDesc;
        }
        
        // 添加备注信息
        if (StringUtils.isNotBlank(remark) && !num) {
            content += "," + remark;
        }
        
        return content;
    }
    
    /**
     * 处理设备分组（统一处理有设备名称和无设备名称的情况）
     * @param checkContent 内容构建器
     * @param facilities 设备列表
     * @param locationDisplayName 位置显示名称
     * @param displayName 显示名称（设备名称或分项名称）
     */
    private void processDeviceGroup(StringBuilder checkContent, List<FacilityInfo> facilities, String locationDisplayName, String displayName) {
        // 提取设备编号并转换为数字
        List<Integer> codes = facilities.stream()
                .map(f -> {
                    String code = f.getCode().replaceFirst("^0+", "");
                    return code.isEmpty() ? 1 : Integer.parseInt(code);
                })
                .sorted()
                .collect(Collectors.toList());
        
        // 查找连续片段
        List<List<Integer>> segments = findConsecutiveSegments(codes);
        
        for (List<Integer> segment : segments) {
            if (segment.size() == 1) {
                // 单个检测点：位置+检测点编号+空格+显示名称
                checkContent.append(locationDisplayName)
                        .append(segment.get(0))
                        .append("# ")
                        .append(displayName)
                        .append("、");
            } else {
                // 连续检测点：位置+检测点编号1~检测点编号n+空格+显示名称
                checkContent.append(locationDisplayName)
                        .append(segment.get(0))
                        .append("#～")
                        .append(segment.get(segment.size() - 1))
                        .append("# ")
                        .append(displayName)
                        .append("、");
            }
        }
    }

    /**
     * 查找数字列表中的连续片段
     * @param numbers 数字列表
     * @return 连续片段的列表
     */
    private List<List<Integer>> findConsecutiveSegments(List<Integer> numbers) {
        if (numbers == null || numbers.isEmpty()) {
            return Collections.emptyList();
        }
        // 先对数字列表进行排序
        Collections.sort(numbers);
        List<List<Integer>> segments = new ArrayList<>();
        List<Integer> currentSegment = new ArrayList<>();
        currentSegment.add(numbers.get(0));
        
        for (int i = 1; i < numbers.size(); i++) {
            int current = numbers.get(i);
            int previous = numbers.get(i - 1);
            // 如果是连续的
            if (current == previous + 1) {
                currentSegment.add(current);
            } else {
                // 不连续，开始新的片段
                segments.add(currentSegment);
                currentSegment = new ArrayList<>();
                currentSegment.add(current);
            }
        }
        // 添加最后一个片段
        segments.add(currentSegment);
        return segments;
    }
    
    /**
     * 获取用于分组的位置键
     * 包含位置信息和设备名称，确保相同位置但不同设备名称的设备能够正确分组
     */
    private String getLocationDisplayKey(FacilityInfo info) {
        String location = info.getLocation();
        String locationName = info.getLocationName();
        String deviceName = info.getName();
        
        StringBuilder keyBuilder = new StringBuilder();
        
        // 对于上行/下行洞室，使用location作为位置部分
        if ((location != null) && (location.startsWith("1") || location.startsWith("2"))) {
            keyBuilder.append(location);
        } else {
            // 对于其他位置，使用location+locationName作为位置部分
            keyBuilder.append(location).append(":").append(locationName != null ? locationName : "");
        }
        
        // 添加设备名称到分组KEY中
        // 如果设备名称为空，使用特殊标识符来区分有名称和无名称的设备
        if (StringUtils.isNotBlank(deviceName)) {
            keyBuilder.append("|device:").append(deviceName);
        } else {
            keyBuilder.append("|device:NO_NAME");
        }
        
        return keyBuilder.toString();
    }

    /**
     * 获取位置的显示名称
     */
    private String getLocationDisplayName(FacilityInfo info) {
        String location = info.getLocation();
        if (location == null) {
            return "";
        }
        // 其他情况：使用locationName
        String locationName = info.getLocationName();
        if(Objects.equals(info.getLocation(),"000")){
            return "";
        }
        // 上行/下行洞室处理
        if (Objects.equals(locationName,"上行洞室") || Objects.equals(locationName,"下行洞室")) {
            String direction = location.startsWith("1") ? "上行" : "下行";
            // 提取数字部分并移除前导零
            String numberPart = location.substring(1).replaceFirst("^0+", "");
            if (numberPart.isEmpty()) {
                numberPart = "1"; // 默认为1，如果全是0
            }
            return direction + numberPart + "号洞室";
        }else if(Objects.equals(locationName,"轴流风机房")){
            return Integer.valueOf(location.substring(location.length()-1,location.length())) + "号轴流风机房";
        }

        if (locationName != null && !locationName.isEmpty()) {
            return locationName;
        }
        // 如果locationName为空，则使用location代码
        return location;
    }
    /**
     * 新增机电设施检测
     *
     * @param tunnelCheck 机电设施检测
     * @return 结果
     */
    @Override
    public int insertTunnelCheck(TunnelCheck tunnelCheck) {
        tunnelCheck.setCreateTime(DateUtils.getNowDate());
        return tunnelCheckMapper.insertTunnelCheck(tunnelCheck);
    }

    /**
     * 修改机电设施检测
     *
     * @param tunnelCheck 机电设施检测
     * @return 结果
     */
    @Override
    public int updateTunnelCheck(TunnelCheck tunnelCheck) {
        tunnelCheck.setUpdateTime(DateUtils.getNowDate());
        return tunnelCheckMapper.updateTunnelCheck(tunnelCheck);
    }

    /**
     * 批量删除机电设施检测
     *
     * @param ids 需要删除的机电设施检测主键
     * @return 结果
     */
    @Override
    public int deleteTunnelCheckByIds(Long[] ids) {
        return tunnelCheckMapper.deleteTunnelCheckByIds(ids);
    }

    /**
     * 删除机电设施检测信息
     *
     * @param id 机电设施检测主键
     * @return 结果
     */
    @Override
    public int deleteTunnelCheckById(Long id) {
        return tunnelCheckMapper.deleteTunnelCheckById(id);
    }



    public  static List<String> locationCodeList = Lists.newArrayList("S01","X01", "SX", "XX", "SR", "SC", "XR", "XC", "WS", "WX", "X", "D");

    @Override
    @Transactional
    public void saveOrUpdateTunnelCheck(List<TunnelCheck> list) throws IOException {
        if(CollectionUtils.isEmpty(list)){
            throw new ServiceException("病害录入记录不能为空");
        }
        //查询隧道,上行,下行,中行的信息
        TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(list.get(0).getTunnelId());
        if(Objects.isNull(tunnelInfo)){
            throw new ServiceException("隧道详情不存在");
        }
        for (TunnelCheck check : list) {
//            //如果是新增,需要判断设备是否已经出库
//            if(Objects.isNull(check.getId())){
//                if(StringUtils.isNotEmpty(check.getCheckFacilityIds())){
//                    List<Long> ids = Arrays.asList(check.getCheckFacilityIds().split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
//                    List<CheckFacility> checkFacilityList = checkFacilityMapper.selectCheckFacilityByCheckFacilityIds(ids);
//                    for (CheckFacility checkFacility : checkFacilityList) {
//                        if(Objects.equals(checkFacility.getFlag(),1)){
//                            throw new ServiceException("设备未出库审批完成,不能进行数据录入");
//                        }
//                    }
//                }
//            }
            //查询分部是否存在
            if(Objects.equals(check.getStatus(),2)){
                CheckEnum query = new CheckEnum();
                query.setPartCode(check.getPartCode());
                query.setItemCode(check.getItemCode());
                query.setQuestionDesc(check.getQuestionDesc());
                CheckEnum checkEnum=checkEnumMapper.selectByParams(query);
                if(Objects.isNull(checkEnum)){
                    throw new ServiceException("病害checkEnum不存在,"+ JSON.toJSONString(query));
                }
                // 确保异常状态下 item_id 不为空
                if(Objects.isNull(checkEnum.getId())){
                    throw new ServiceException("病害记录异常：item_id不能为空");
                }
                if(!Objects.equals("灯具无法点亮",check.getQuestionDesc())){
                    //只有缺陷是"灯具无法点亮"的时候,才能填写设备损坏比
                    check.setRate(null);
                }
            }else{
                check.setItemId(null);
            }
            if(Objects.nonNull(check.getRate())){
                //如果设备损坏比不为空,需要校验值,是否在0到1之间
                if(check.getRate().compareTo(BigDecimal.ZERO)<0 || check.getRate().compareTo(BigDecimal.ONE)>0){
                    throw new ServiceException("设备损坏只能填0到1之间的数值");
                }
            }
            //查询对应隧道的资产是否存在
            FacilityInfo facilityInfo=new FacilityInfo();
            facilityInfo.setTunnelId(check.getTunnelId());
            facilityInfo.setPartCode(check.getPartCode());
            facilityInfo.setItemCode(check.getItemCode());
            facilityInfo.setLocation(check.getLocation());
            facilityInfo.setCode(check.getCode());
            List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectFacilityInfoList(facilityInfo);
            if(CollectionUtils.isEmpty(facilityInfoList)){
                throw new ServiceException("隧道资产不存在"+tunnelInfo.getTunnelCode()+"分部"+check.getPartName()+"分项"+check.getItemName()+"位置"+check.getLocation()+"编码"+check.getCode()+"对应资产不存在");
            }
            //如果是全选,直接把当前设备的名称给检测记录
            if(Objects.equals(check.getSelectAll(),1)){
                check.setName(facilityInfoList.get(0).getName());
            }
        }
        //处理照片,只需要处理个对象的照片,其他对象都是一样的照片
        TunnelCheck checkTemp = list.get(0);
        this.dealWithPhoto(checkTemp);
        for (TunnelCheck check : list) {
            try {
                check.setTunnelId(tunnelInfo.getId());
                check.setWorkPic1(tunnelInfo.getWorkPic1());
                check.setWorkPic2(tunnelInfo.getWorkPic2());
                check.setWorkPic3(tunnelInfo.getWorkPic3());
                //检测状态是异常的,需要传递异常相关的检测字段
                if(Objects.equals(check.getStatus(),2)){
                    CheckEnum query = new CheckEnum();
                    query.setPartCode(check.getPartCode());
                    query.setItemCode(check.getItemCode());
                    query.setQuestionDesc(check.getQuestionDesc());
                    CheckEnum checkEnum=checkEnumMapper.selectByParams(query);
                    if(Objects.isNull(checkEnum)){
                        throw new ServiceException("病害checkEnum不存在");
                    }
                    // 再次确保异常状态下 item_id 不为空
                    if(Objects.isNull(checkEnum.getId())){
                        throw new ServiceException("病害记录异常：item_id不能为空");
                    }
                    check.setItemId(checkEnum.getId());
                    if(Objects.isNull(check.getScore())){
                        check.setScore(checkEnum.getScore());
                    }
                }else{
                    check.setItemId(null);
                    check.setScore(null);
                }
                if(StringUtils.isEmpty(check.getSuggestion())){
                    check.setSuggestion(check.getSuggestion());
                }
                //查询对应隧道的资产是否存在
                FacilityInfo facilityInfo=new FacilityInfo();
                facilityInfo.setTunnelId(check.getTunnelId());
                facilityInfo.setPartCode(check.getPartCode());
                facilityInfo.setItemCode(check.getItemCode());
                facilityInfo.setLocation(check.getLocation());
                facilityInfo.setCode(check.getCode());
                List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectFacilityInfoList(facilityInfo);
                if(CollectionUtils.isEmpty(facilityInfoList)){
                    throw new ServiceException("隧道资产不存在"+tunnelInfo.getTunnelCode()+"分部:"+check.getPartName()+"分项:"+check.getItemName()+"位置:"+check.getLocation()+"编码:"+check.getCode()+"对应资产不存在");
                }
                //更新设备状态
                FacilityInfo temp =facilityInfoList.get(0);
                temp.setStatus(check.getStatus());
                temp.setResistor(check.getResistor());
                temp.setName(check.getName());
                if(!StringUtils.isEmpty(check.getCheckCode())){
                    temp.setCheckCode(check.getCheckCode());
                }
                temp.setSelectAll(check.getSelectAll());
                facilityInfoMapper.updateStatusById(temp);
                check.setFacilityId(temp.getId());
                check.setPicUrl1(checkTemp.getPicUrl1());
                check.setPicUrl2(checkTemp.getPicUrl2());
                check.setPicUrl3(checkTemp.getPicUrl3());
                check.setCheckYear(String.valueOf(DateUtil.year(new Date())));
                
                // 检查是否存在重复记录
                TunnelCheck queryCheck = new TunnelCheck();
                queryCheck.setCheckYear(check.getCheckYear());
                queryCheck.setTunnelId(check.getTunnelId());
                queryCheck.setFacilityId(check.getFacilityId());
                List<TunnelCheck> existList = Lists.newArrayList();
                if(Objects.isNull(check.getItemId())){
                    existList = tunnelCheckMapper.selectByParams(queryCheck);
                }
                if(Objects.equals("null",check.getRemark())){
                    check.setRemark("");
                }
                if(check.getId()==null){
                    //使用唯一索引来判断是否存在检测记录,如果存在,直接进行桩号更新
                    TunnelCheck tempCheck = tunnelCheckMapper.selectByTunnelCheck(check);
                    if(Objects.nonNull(tempCheck)){
                        check.setModifier(SecurityUtils.getUserId());
                        tunnelCheckMapper.updateCheckCodeByTunnelCheck(check);
                        continue;
                    }
                    // 新增时检查是否存在重复记录
                    if(!CollectionUtils.isEmpty(existList)){
                        throw new ServiceException("检测记录已存在");
                    }
                    check.setCreator(SecurityUtils.getUserId());
                    // 新增检测记录时设置为待复核状态
                    check.setCheckStatus(0);
                    tunnelCheckMapper.insertTunnelCheck(check);
                }else{
                    // 更新时检查是否存在其他重复记录
                    if(!CollectionUtils.isEmpty(existList) && existList.stream().anyMatch(item -> !item.getId().equals(check.getId()))){
                        throw new ServiceException("检测记录已存在");
                    }
                    TunnelCheck tunnelCheck=tunnelCheckMapper.selectTunnelCheckById(check.getId());
                    if(Objects.isNull(tunnelCheck)){
                        throw new ServiceException("病害不存在");
                    }
                    check.setModifier(SecurityUtils.getUserId());
                    // 修改检测记录时重置为待复核状态
                    check.setCheckStatus(0);
                    check.setCheckRemark(null); // 清空之前的检测条件
                    tunnelCheckMapper.updateTunnelCheck(check);
                }
            } catch (Exception e) {
                // 检查是否是唯一索引异常
                if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                    log.error(e.getMessage(), e);
                    throw new ServiceException("检测记录已存在,进行桩号更新");
                }
                // 其他异常直接抛出
                throw e;
            }
        }
    }



    @Override
    public List<TunnelCheck> checkListByPage(TunnelCheck tunnelCheck) {
        List<TunnelCheck> list = tunnelCheckMapper.selectTunnelCheckList(tunnelCheck);
        //查询设备数量
        String roadName = null;
        for (TunnelCheck check : list) {
            FacilityInfo facilityInfo = new FacilityInfo();
            facilityInfo.setTunnelId(check.getTunnelId());
            facilityInfo.setItemCode(check.getItemCode());
            facilityInfo.setPartCode(check.getPartCode());
            facilityInfo.setLocation(check.getLocation());
            int temp=facilityInfoMapper.countCodeByParams(facilityInfo);
            check.setFacilityNum(temp);
            if(Objects.equals(roadName,check.getRoadName())){
                check.setRoadName(null);
            }else{
                roadName=check.getRoadName();
            }
            if(StringUtils.isBlank(check.getName())){
                check.setName(" ");
            }
        }
        return list;
    }

    @Override
    public void deleteCheckById(Long id) {
        tunnelCheckMapper.deleteTunnelCheckById(id);
    }

    @Override
    public TunnelInfo checkList(TunnelInfo tunnelInfo) {
        if(Objects.isNull(tunnelInfo.getId())){
            throw new ServiceException("隧道ID不能为空");
        }
        //查询隧道,上行,下行,中行的信息
        TunnelInfo result = tunnelInfoMapper.selectTunnelInfoById(tunnelInfo.getId());
        if(Objects.isNull(result)){
            throw new ServiceException("隧道详情不存在");
        }
        TunnelCheck tunnelCheck = new TunnelCheck();
        tunnelCheck.setCreator(SecurityUtils.getUserId());
        tunnelCheck.setTunnelList(Lists.newArrayList(result.getId()));
        List<TunnelCheck> checkList=tunnelCheckMapper.selectTunnelCheckList(tunnelCheck);
        result.setCheckList(checkList);
        return result;
    }

    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            //生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);
            ExcelUtil excelUtil = new ExcelUtil(TunnelCheck.class);
            List<TunnelCheck> dataList = excelUtil.importExcel(bufferedInputStream, 0);
            List<String> tunnelCodeList = dataList.stream().map(TunnelCheck::getTunnelCode).distinct().collect(Collectors.toList());
            List<TunnelInfo> tunnelInfos = tunnelInfoMapper.selectByTunnelCodes(tunnelCodeList);
            Map<String, TunnelInfo> tunnelMap = tunnelInfos.stream().collect(Collectors.toMap(TunnelInfo::getTunnelCode, Function.identity(), (key1, key2) -> key1));
            if (!CollectionUtils.isEmpty(dataList)) {
                for (TunnelCheck tunnelCheck : dataList) {
                    if (!tunnelMap.containsKey(tunnelCheck.getTunnelCode())) {
                        throw new ServiceException("隧道编码不存在");
                    }
                    if(StringUtils.isBlank(tunnelCheck.getName())){
                        tunnelCheck.setName("");
                    }
                    tunnelCheck.setType(1);
                    tunnelCheck.setStatus(2);
                    tunnelCheck.setTunnelId(tunnelMap.get(tunnelCheck.getTunnelCode()).getId());
                }
                this.saveOrUpdateTunnelCheck(dataList);
                addResponse.setMsg("success");
                addResponse.setStatus(0);
            } else {
                addResponse.setMsg("导入数据不符合模板要求！");
                addResponse.setStatus(1);
            }
        } catch (IOException e) {
            e.printStackTrace();
            addResponse.setMsg("文件解析失败:" + e.getMessage());
            addResponse.setStatus(1);
        } catch (Exception e) {
            e.printStackTrace();
            addResponse.setMsg("导入数据失败:" + e.getMessage());
            addResponse.setStatus(1);
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    @Override
    @Transactional
    public int deleteCheckByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new ServiceException("参数不能为空");
        }
        return tunnelCheckMapper.deleteCheckByIds(idList);
    }


    /**
     * 处理图片上传
     *
     * @param check
     */
    private void dealWithPhoto(TunnelCheck check) throws IOException {
        if (StringUtils.isNotEmpty(check.getPicUrl1()) && check.getPicUrl1().startsWith("data") && check.getPicUrl1().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(check.getPicUrl1(), "jpg",this.initPicNo(1));
            check.setPicUrl1(workPic);
        }
        if (StringUtils.isNotEmpty(check.getPicUrl2()) && check.getPicUrl2().startsWith("data") && check.getPicUrl2().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(check.getPicUrl2(), "jpg",this.initPicNo(2));
            check.setPicUrl2(workPic);
        }
        if (StringUtils.isNotEmpty(check.getPicUrl3()) && check.getPicUrl3().startsWith("data") && check.getPicUrl3().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(check.getPicUrl3(), "jpg",this.initPicNo(3));
            check.setPicUrl3(workPic);
        }

    }


    /**
     * 图片名称
     * @param num
     * @return
     */
    private  String initPicNo(int num){
        return DateUtil.now().substring(5,19).replaceAll("-","").replaceAll(":","").replaceAll(" ","")+"0"+num;
    }

    @Override
    public Long selectTunnelCheckListCount(TunnelCheck tunnelCheck) {
        // Apply user-based filtering for recheck status
        applyUserBasedRecheckFilter(tunnelCheck);
        
        return tunnelCheckMapper.selectTunnelCheckListCount(tunnelCheck);
    }

    @Override
    public List<TunnelCheck> selectTunnelCheckDetailList(TunnelCheck tunnelCheck) {
        // Apply user-based filtering for recheck status
        applyUserBasedRecheckFilter(tunnelCheck);
        
        List<TunnelCheck> list = tunnelCheckMapper.selectTunnelCheckDetailList(tunnelCheck);
        // 遍历结果集，为每条记录计算设备数量
//        for (TunnelCheck check : list) {
//            FacilityInfo facilityInfo = new FacilityInfo();
//            facilityInfo.setTunnelId(check.getTunnelId());
//            facilityInfo.setItemCode(check.getItemCode());
//            facilityInfo.setPartCode(check.getPartCode());
//            facilityInfo.setLocation(check.getLocation());
//            // 获取设备数量
//            int facilityNum = facilityInfoMapper.countCodeByParams(facilityInfo);
//            check.setFacilityNum(facilityNum);
//        }
        return list;
    }

    @Override
    public Long selectTunnelCheckDetailCount(TunnelCheck tunnelCheck) {
        // Apply user-based filtering for recheck status
        applyUserBasedRecheckFilter(tunnelCheck);
        
        return tunnelCheckMapper.selectTunnelCheckDetailCount(tunnelCheck);
    }

    @Override
    @Transactional
    public BatchAddResponse batchImportCar(MultipartFile file) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            //生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);
            ExcelUtil excelUtil = new ExcelUtil(TunnelCheck.class);
            List<TunnelCheck> dataList = excelUtil.importExcel(bufferedInputStream, 0);
            List<String> tunnelCodeList = dataList.stream().map(TunnelCheck::getTunnelCode).distinct().collect(Collectors.toList());
            List<TunnelInfo> tunnelInfos = tunnelInfoMapper.selectByTunnelCodes(tunnelCodeList);
            Map<String, TunnelInfo> tunnelMap = tunnelInfos.stream().collect(Collectors.toMap(TunnelInfo::getTunnelCode, Function.identity(), (key1, key2) -> key1));
            if (!CollectionUtils.isEmpty(dataList)) {
                for (TunnelCheck tunnelCheck : dataList) {
                    if (!tunnelMap.containsKey(tunnelCheck.getTunnelCode())) {
                        throw new ServiceException("隧道编码不存在");
                    }
                    if(StringUtils.isBlank(tunnelCheck.getName())){
                        tunnelCheck.setName("");
                    }
                    tunnelCheck.setType(2);
                    tunnelCheck.setStatus(2);
                    tunnelCheck.setTunnelId(tunnelMap.get(tunnelCheck.getTunnelCode()).getId());
                }
                this.saveOrUpdateTunnelCheck(dataList);
                addResponse.setMsg("success");
                addResponse.setStatus(0);
            } else {
                addResponse.setMsg("导入数据不符合模板要求！");
                addResponse.setStatus(1);
            }
        } catch (IOException e) {
            e.printStackTrace();
            addResponse.setMsg("文件解析失败:" + e.getMessage());
            addResponse.setStatus(1);
        } catch (Exception e) {
            e.printStackTrace();
            addResponse.setMsg("导入数据失败:" + e.getMessage());
            addResponse.setStatus(1);
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    @Override
    @Transactional
    public int batchReview(List<Long> ids, String checkRemark) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要复核的记录");
        }
        // 复核状态设置为已复核(1)，获取当前用户ID作为复核人
        Long currentUserId = SecurityUtils.getUserId();
        return tunnelCheckMapper.batchUpdateCheckStatus(ids, 1, checkRemark, currentUserId);
    }

    @Override
    @Transactional
    public int oneClickRecheck(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要复检的记录");
        }
        
        int successCount = 0;
        Long currentUserId = SecurityUtils.getUserId();
        
        for (Long id : ids) {
            // 1. 获取原始检测记录
            TunnelCheck originalCheck = tunnelCheckMapper.selectTunnelCheckById(id);
            if (originalCheck == null) {
                continue;
            }
            // 2. 创建新的检测记录（复制原记录）
            TunnelCheck newCheck = new TunnelCheck();
            // 复制所有字段
            BeanUtils.copyProperties(originalCheck, newCheck);
            // 设置新记录的特殊字段
            newCheck.setSourceId(id); // 设置原检测记录ID作为source_id
            newCheck.setCreateBy(SecurityUtils.getUsername());
            newCheck.setCreateTime(DateUtils.getNowDate());


            TunnelCheck temp = new TunnelCheck();
            temp.setId(id);
            temp.setRecheckStatus(1);
            int updateResult = tunnelCheckMapper.updateRecheckStatus(temp);

            // 3. 插入新的检测记录
            int insertResult = tunnelCheckMapper.insertTunnelCheck(newCheck);
            if (insertResult > 0) {
                // 4. 更新原记录的复检状态
                // 设置 recheck_status=1, recheck_id=新记录的ID
                TunnelCheck updateOriginal = new TunnelCheck();
                updateOriginal.setId(id);
                updateOriginal.setRecheckStatus(1);
                updateOriginal.setRecheckId(newCheck.getId());
                updateOriginal.setModifier(SecurityUtils.getUserId());
                updateResult = tunnelCheckMapper.updateRecheckStatus(updateOriginal);
                if (updateResult > 0) {
                    successCount++;
                }
            }
        }
        
        return successCount;
    }

    /**
     * Apply user-based filtering for recheck status
     * 陈慧 can see records with recheck_status in (0,1), others only see recheck_status=0
     */
    private void applyUserBasedRecheckFilter(TunnelCheck tunnelCheck) {
        try {
            String currentUsername = SecurityUtils.getUsername();
            // If recheckStatus is explicitly set, respect it (for 陈慧's filtering)
            if (tunnelCheck.getRecheckStatus() != null) {
                return;
            }
            
            // If user is not 陈慧, force recheckStatus to 0 (only show non-rechecked records)
            if (!"陈慧".equals(currentUsername)) {
                tunnelCheck.setRecheckStatus(0);
            }
            // If user is 陈慧 and recheckStatus is null, don't set it (show all records)
        } catch (Exception e) {
            // If unable to get current user, default to showing only non-rechecked records
            tunnelCheck.setRecheckStatus(0);
        }
    }
}
