package com.tunnel.service;

import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.CheckFacilityRecord;
import com.tunnel.domain.Tunnel;
import com.tunnel.domain.TunnelInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/**
 * 隧道信息详情Service接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface TunnelInfoService {
    /**
     * 查询隧道信息详情
     *
     * @param id 隧道信息详情主键
     * @return 隧道信息详情
     */
    public TunnelInfo selectTunnelInfoById(Long id);

    /**
     * 查询隧道信息详情列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情集合
     */
    public List<TunnelInfo> selectTunnelInfoList(TunnelInfo tunnelInfo);

    /**
     * 新增隧道信息详情
     *
     * @param tunnelInfo 隧道信息详情
     * @return 结果
     */
    public int insertTunnelInfo(TunnelInfo tunnelInfo);

    /**
     * 修改隧道信息详情
     *
     * @param tunnelInfo 隧道信息详情
     * @return 结果
     */
    public int updateTunnelInfo(TunnelInfo tunnelInfo);

    /**
     * 批量删除隧道信息详情
     *
     * @param ids 需要删除的隧道信息详情主键集合
     * @return 结果
     */
    public int deleteTunnelInfoByIds(Long[] ids);

    /**
     * 删除隧道信息详情信息
     *
     * @param id 隧道信息详情主键
     * @return 结果
     */
    public int deleteTunnelInfoById(Long id);

    List<TunnelInfo> selectDistinctTunnelCode(TunnelInfo tunnelInfo);

    TunnelInfo updateTunnelInfoPic(TunnelInfo tunnelInfo) throws IOException;

    List<TunnelInfo> selectDistinctTunnelList(TunnelInfo tunnel);

    BatchAddResponse batchImport(MultipartFile file);


    List<TunnelInfo> selectDistinctCompany(TunnelInfo tunnelInfo);

    List<TunnelInfo> selectDistinctRoad(TunnelInfo tunnelInfo);

    List<TunnelInfo> listAll();

    List<TunnelInfo> listDistinctRoad(TunnelInfo tunnelInfo);
    
    /**
     * 更新隧道复核状态
     *
     * @param id 隧道信息详情主键
     * @return 结果
     */
    public int updateReviewStatus(Long id);

    void updateTunnelInfoCompleteStatus(TunnelInfo tunnelInfo);
    
    /**
     * 获取当前用户对应任务的检测设备
     *
     * @param tunnelId 隧道ID
     * @param userId 用户ID
     * @return 检测设备记录集合
     */
    List<CheckFacilityRecord> getCheckFacilitiesByUser(Long tunnelId, Long userId);
    
    /**
     * 查询不重复的路线编码列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情集合
     */
    List<TunnelInfo> selectDistinctRoadCode(TunnelInfo tunnelInfo);
    
    /**
     * 查询不重复的隧道名称列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情集合
     */
    List<TunnelInfo> selectDistinctTunnelName(TunnelInfo tunnelInfo);
}
