package com.tunnel.service;

import com.tunnel.domain.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 机电设施检测Service接口
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
public interface TunnelCheckService {
    /**
     * 查询机电设施检测
     *
     * @param id 机电设施检测主键
     * @return 机电设施检测
     */
    public TunnelCheck selectTunnelCheckById(Long id);

    /**
     * 查询机电设施检测列表
     *
     * @param tunnelCheck 机电设施检测
     * @return 机电设施检测集合
     */
    public List<TunnelCheck> selectTunnelCheckList(TunnelCheck tunnelCheck);

    /**
     * 查询机电设施检测列表总数
     *
     * @param tunnelCheck 机电设施检测
     * @return 总数
     */
    public Long selectTunnelCheckListCount(TunnelCheck tunnelCheck);

    /**
     * 新增机电设施检测
     *
     * @param tunnelCheck 机电设施检测
     * @return 结果
     */
    public int insertTunnelCheck(TunnelCheck tunnelCheck);

    /**
     * 修改机电设施检测
     *
     * @param tunnelCheck 机电设施检测
     * @return 结果
     */
    public int updateTunnelCheck(TunnelCheck tunnelCheck);

    /**
     * 批量删除机电设施检测
     *
     * @param ids 需要删除的机电设施检测主键集合
     * @return 结果
     */
    public int deleteTunnelCheckByIds(Long[] ids);

    /**
     * 删除机电设施检测信息
     *
     * @param id 机电设施检测主键
     * @return 结果
     */
    public int deleteTunnelCheckById(Long id);

    void saveOrUpdateTunnelCheck(List<TunnelCheck> list) throws IOException;

    List<TunnelCheck> checkListByPage(TunnelCheck tunnelCheck);

    void deleteCheckById(Long id);

    TunnelInfo checkList(TunnelInfo tunnelInfo);

    BatchAddResponse batchImport(MultipartFile file);

    int deleteCheckByIds(List<Long> ids);

    /**
     * 查询机电设施检测明细列表
     * 
     * @param tunnelCheck 机电设施检测
     * @return 机电设施检测集合
     */
    public List<TunnelCheck> selectTunnelCheckDetailList(TunnelCheck tunnelCheck);

    /**
     * 查询机电设施检测明细总数
     * 
     * @param tunnelCheck 机电设施检测
     * @return 总数
     */
    public Long selectTunnelCheckDetailCount(TunnelCheck tunnelCheck);

    BatchAddResponse batchImportCar(MultipartFile file);

    String formatCheckCode(List<FacilityInfo> listInfo, String questionDesc,List<TunnelCheck> checkList,boolean num);

    /**
     * 批量复核检测记录
     * 
     * @param ids 检测记录ID列表
     * @param checkRemark 检测条件
     * @return 结果
     */
    int batchReview(List<Long> ids, String checkRemark);

    /**
     * 一键复检检测记录
     * 
     * @param ids 检测记录ID列表
     * @return 结果
     */
    int oneClickRecheck(List<Long> ids);
}
