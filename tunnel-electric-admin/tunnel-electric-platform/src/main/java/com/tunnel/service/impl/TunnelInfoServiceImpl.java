package com.tunnel.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.FileUtil;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.file.AliYunOSSFileUtil;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.*;
import com.tunnel.mapper.*;
import com.tunnel.service.TunnelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tunnel.common.utils.SecurityUtils.getUserId;

/**
 * 隧道信息详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Service
@Slf4j
public class TunnelInfoServiceImpl implements TunnelInfoService {
    @Autowired
    private TunnelInfoMapper tunnelInfoMapper;
    @Resource
    private TunnelMapper tunnelMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private CheckFacilityRecordMapper checkFacilityRecordMapper;
    @Resource
    private TaskUserMapper taskUserMapper;
    //base64文件前缀
    public static final String DATA_IMAGE = "data:image";

    /**
     * 查询隧道信息详情
     *
     * @param id 隧道信息详情主键
     * @return 隧道信息详情
     */
    @Override
    public TunnelInfo selectTunnelInfoById(Long id) {
        return tunnelInfoMapper.selectTunnelInfoById(id);
    }

    /**
     * 查询隧道信息详情列表
     *
     * @param tunnelInfo 隧道信息详情
     * @return 隧道信息详情
     */
    @Override
    public List<TunnelInfo> selectTunnelInfoList(TunnelInfo tunnelInfo) {
        return tunnelInfoMapper.selectTunnelInfoList(tunnelInfo);
    }

    /**
     * 新增隧道信息详情
     *
     * @param tunnelInfo 隧道信息详情
     * @return 结果
     */
    @Override
    public int insertTunnelInfo(TunnelInfo tunnelInfo) {
        Tunnel tunnel = tunnelMapper.selectTunnelById(tunnelInfo.getTunnelId());
        if(Objects.isNull(tunnel)){
            throw new ServiceException("总隧道不存在");
        }
        tunnelInfo.setCompanyName(tunnel.getCompanyName());
        // 验证隧道编码是否已存在
        if(StringUtils.isNotEmpty(tunnelInfo.getTunnelCode()) && 
           !CollectionUtils.isEmpty(tunnelInfoMapper.selectByTunnelCodes(Collections.singletonList(tunnelInfo.getTunnelCode())))){
            throw new ServiceException("隧道编码已存在");
        }
        return tunnelInfoMapper.batchAdd(Lists.newArrayList(tunnelInfo));
    }

    /**
     * 修改隧道信息详情
     *
     * @param tunnelInfo 隧道信息详情
     * @return 结果
     */
    @Override
    public int updateTunnelInfo(TunnelInfo tunnelInfo) {
        if(Objects.isNull(tunnelInfo.getTunnelId()) || Objects.isNull(tunnelInfo.getId())){
            throw new ServiceException("参数错误");
        }
        // 如果修改了隧道ID,更新关联信息
        Tunnel tunnel = tunnelMapper.selectTunnelById(tunnelInfo.getTunnelId());
        if (tunnel != null) {
            tunnelInfo.setCompanyName(tunnel.getCompanyName());
        }
        return tunnelInfoMapper.updateTunnelInfo(tunnelInfo);
    }

    /**
     * 批量删除隧道信息详情
     *
     * @param ids 需要删除的隧道信息详情主键
     * @return 结果
     */
    @Override
    public int deleteTunnelInfoByIds(Long[] ids) {
        return tunnelInfoMapper.deleteTunnelInfoByIds(ids);
    }

    /**
     * 删除隧道信息详情信息
     *
     * @param id 隧道信息详情主键
     * @return 结果
     */
    @Override
    public int deleteTunnelInfoById(Long id) {
        return tunnelInfoMapper.deleteTunnelInfoById(id);
    }

    @Override
    public List<TunnelInfo> selectDistinctTunnelCode(TunnelInfo tunnelInfo) {
        return tunnelInfoMapper.selectDistinctTunnelCode(tunnelInfo);
    }

    @Override
    public TunnelInfo updateTunnelInfoPic(TunnelInfo tunnelInfo) throws IOException {
        this.dealWithPhoto(tunnelInfo);
        tunnelInfoMapper.updateTunnelInfo(tunnelInfo);
        tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(tunnelInfo.getId());
        return tunnelInfo;
    }

    @Override
    public List<TunnelInfo> selectDistinctTunnelList(TunnelInfo tunnel) {
        //查询当前登录用户对应的任务,sc_task_user,sc_task
        Long userId=SecurityUtils.getUserId();
        List<Task> taskList = taskUserMapper.selectTaskByUserId(userId);
        List<Long> tunnelIdList = Lists.newArrayList();
        for (Task task : taskList) {
            List<String> list= Arrays.asList(task.getTunnelIds().split(","));
            tunnelIdList.addAll(list.stream().map(Long::parseLong).collect(Collectors.toList()));
        }
        if(CollectionUtils.isEmpty(tunnelIdList)){
            return Lists.newArrayList();
        }
        tunnel.setTunnelIds(tunnelIdList);
        return tunnelInfoMapper.selectDistinctTunnelList(tunnel);
    }

    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            //生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);
            ExcelUtil excelUtil = new ExcelUtil(TunnelInfo.class);
            List<TunnelInfo> dataList = excelUtil.importExcel(bufferedInputStream, 0);
            List<Tunnel> tunnelList = tunnelMapper.selectByParamsList(dataList);
            //使用year+section+companyName进行Map映射
            Map<String, Tunnel> tunnelMap = tunnelList.stream().collect(Collectors.toMap(tunnel -> tunnel.getYear() +"#"+ tunnel.getSection() +"#"+ tunnel.getCompanyName(), Function.identity(), (key1, key2) -> key1));
            //特殊处理单行数据里面的多行数据
            if(!CollectionUtils.isEmpty(dataList)){
                List<TunnelInfo> resultList = new ArrayList<>();
                for (TunnelInfo temp : dataList) {
                    Tunnel tunnel = tunnelMap.get(temp.getYear() +"#"+ temp.getSection() +"#"+ temp.getCompanyName());
                    if(Objects.isNull(tunnel)){
                        throw new ServiceException("总隧道不存在");
                    }
                    if(Objects.nonNull(temp.getUpTunnelLength()) && Objects.nonNull(temp.getDownTunnelLength())){
                        temp.setTotalTunnelLength(temp.getUpTunnelLength().add(temp.getDownTunnelLength()));
                    }
                    temp.setTunnelId(tunnel.getId());
                    resultList.add(temp);
                }
                tunnelInfoMapper.batchAdd(resultList);
                addResponse.setMsg("success");
                addResponse.setStatus(0);
            }else {
                addResponse.setMsg("导入数据不符合模板要求！");
                addResponse.setStatus(1);
            }
        }catch (IOException e){
            e.printStackTrace();
            addResponse.setMsg("文件解析失败:"+e.getMessage());
            addResponse.setStatus(1);
        }catch (Exception e){
            e.printStackTrace();
            addResponse.setMsg("导入数据失败:"+e.getMessage());
            addResponse.setStatus(1);
        }finally {
            try {
                if(fileStream != null){
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }


    /**
     * 处理图片上传
     *
     * @param check
     */
    private void dealWithPhoto(TunnelInfo check) throws IOException {
        if (StringUtils.isNotEmpty(check.getWorkPic1()) && check.getWorkPic1().startsWith("data") && check.getWorkPic1().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(check.getWorkPic1(), "jpg",this.initPicNo(1));
            check.setWorkPic1(workPic);
        }
        if (StringUtils.isNotEmpty(check.getWorkPic2()) && check.getWorkPic2().startsWith("data") && check.getWorkPic2().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(check.getWorkPic2(), "jpg",this.initPicNo(2));
            check.setWorkPic2(workPic);
        }
        if (StringUtils.isNotEmpty(check.getWorkPic3()) && check.getWorkPic3().startsWith("data") && check.getWorkPic3().startsWith(DATA_IMAGE)) {
            String workPic = AliYunOSSFileUtil.uploadBase64File(check.getWorkPic3(), "jpg",this.initPicNo(3));
            check.setWorkPic3(workPic);
        }

    }

    /**
     * 图片名称
     * @param num
     * @return
     */
    private  String initPicNo(int num){
        return DateUtil.now().substring(5,19).replaceAll("-","").replaceAll(":","").replaceAll(" ","")+"0"+num;
    }


    @Override
    public List<TunnelInfo> selectDistinctCompany(TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoMapper.selectDistinctCompany(tunnelInfo);
        return list;
    }

    @Override
    public List<TunnelInfo> selectDistinctRoad(TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoMapper.selectDistinctRoad(tunnelInfo);
        return list;
    }

    @Override
    public List<TunnelInfo> listAll() {
        List<TunnelInfo> list = tunnelInfoMapper.listAll();
        return list;
    }

    @Override
    public List<TunnelInfo> listDistinctRoad(TunnelInfo tunnelInfo) {
        return tunnelInfoMapper.listDistinctRoad();
    }

    /**
     * 更新隧道复核状态
     *
     * @param id 隧道信息详情主键
     * @return 结果
     */
    @Override
    public int updateReviewStatus(Long id) {
        TunnelInfo tunnelInfo = new TunnelInfo();
        tunnelInfo.setId(id);
        tunnelInfo.setStatus(1);
        return tunnelInfoMapper.updateTunnelInfo(tunnelInfo);
    }

    @Override
    public void updateTunnelInfoCompleteStatus(TunnelInfo tunnelInfo) {
        tunnelInfoMapper.updateTunnelInfoCompleteStatus(tunnelInfo);
    }
    
    @Override
    public List<CheckFacilityRecord> getCheckFacilitiesByUser(Long tunnelId, Long userId) {
        // 根据用户ID和隧道ID查找任务
        List<Long> taskIds = taskMapper.selectTaskIdsByUserAndTunnel(userId, tunnelId);
        if (CollectionUtils.isEmpty(taskIds)) {
            return Lists.newArrayList();
        }
        
        // 查找任务对应的出库设备记录
        List<CheckFacilityRecord> facilities = Lists.newArrayList();
        for (Long taskId : taskIds) {
            //查找已经完成的出库设备记录
            List<CheckFacilityRecord> taskFacilities = checkFacilityRecordMapper.selectCheckFacilityRecordByTaskId(taskId,1);
            facilities.addAll(taskFacilities);
        }

        //facilities根据facilityId进行去重
        facilities = facilities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CheckFacilityRecord::getFacilityId))), ArrayList::new));
        return facilities;
    }
    
    @Override
    public List<TunnelInfo> selectDistinctRoadCode(TunnelInfo tunnelInfo) {
        return tunnelInfoMapper.selectDistinctRoadCode(tunnelInfo);
    }
    
    @Override
    public List<TunnelInfo> selectDistinctTunnelName(TunnelInfo tunnelInfo) {
        return tunnelInfoMapper.selectDistinctTunnelName(tunnelInfo);
    }
}
