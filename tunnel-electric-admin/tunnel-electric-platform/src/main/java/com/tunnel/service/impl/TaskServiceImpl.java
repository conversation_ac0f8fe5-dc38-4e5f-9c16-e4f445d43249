package com.tunnel.service.impl;

import java.util.List;
import java.util.Map;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.CheckFacility;
import com.tunnel.domain.CheckFacilityApply;
import com.tunnel.domain.CheckFacilityRecord;
import com.tunnel.domain.TaskUser;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.mapper.CheckFacilityApplyMapper;
import com.tunnel.mapper.CheckFacilityRecordMapper;
import com.tunnel.mapper.TaskUserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.tunnel.mapper.TaskMapper;
import com.tunnel.domain.Task;
import com.tunnel.service.TaskService;

/**
 * 任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class TaskServiceImpl implements TaskService {
    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private TaskUserMapper taskUserMapper;

    @Autowired
    private CheckFacilityRecordMapper checkFacilityRecordMapper;
    @Autowired
    private CheckFacilityApplyMapper checkFacilityApplyMapper;

    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    @Override
    public Task selectTaskById(Long id) {
        Task task = taskMapper.selectTaskById(id);
        if (task != null) {
            // 查询关联的人员配置
            List<TaskUser> taskUsers = taskUserMapper.selectTaskUserByTaskId(String.valueOf(id));
            task.setTaskUsers(taskUsers);
        }
        return task;
    }

    /**
     * 查询任务列表
     *
     * @param task 任务
     * @return 任务
     */
    @Override
    public List<Task> selectTaskList(Task task) {
        return taskMapper.selectTaskList(task);
    }

    /**
     * 新增任务
     *
     * @param task      任务
     * @param taskUsers 任务人员列表
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTask(Task task, List<TaskUser> taskUsers) {
        task.setCreateTime(DateUtils.getNowDate());
        task.setUpdateTime(DateUtils.getNowDate());
        task.setStatus(0);
        int result = taskMapper.insertTask(task);

        if (result > 0 && taskUsers != null && taskUsers.size() > 0) {
            for (TaskUser taskUser : taskUsers) {
                taskUser.setTaskId(String.valueOf(task.getId()));
                taskUser.setCreator(task.getCreator());
                taskUser.setCreateTime(DateUtils.getNowDate());
                taskUser.setUpdateTime(DateUtils.getNowDate());
            }
            taskUserMapper.insertTaskUserBatch(taskUsers);
        }

        return result;
    }

    /**
     * 修改任务
     *
     * @param task      任务
     * @param taskUsers 任务人员列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTask(Task task, List<TaskUser> taskUsers) {
        task.setUpdateTime(DateUtils.getNowDate());
        int result = taskMapper.updateTask(task);

        if (result > 0) {
            taskUserMapper.deleteTaskUserByTaskId(String.valueOf(task.getId()));

            if (taskUsers != null && taskUsers.size() > 0) {
                for (TaskUser taskUser : taskUsers) {
                    taskUser.setTaskId(String.valueOf(task.getId()));
                    taskUser.setCreator(task.getModifier());
                    taskUser.setCreateTime(DateUtils.getNowDate());
                    taskUser.setUpdateTime(DateUtils.getNowDate());
                }
                taskUserMapper.insertTaskUserBatch(taskUsers);
            }
        }

        return result;
    }

    /**
     * 批量删除任务
     *
     * @param ids 需要删除的任务主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTaskByIds(Long[] ids) {
        for (Long id : ids) {
            taskUserMapper.deleteTaskUserByTaskId(String.valueOf(id));
        }
        return taskMapper.deleteTaskByIds(ids);
    }

    /**
     * 删除任务信息
     *
     * @param id 任务主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTaskById(Long id) {
        taskUserMapper.deleteTaskUserByTaskId(String.valueOf(id));
        return taskMapper.deleteTaskById(id);
    }

    /**
     * 查询路段列表
     *
     * @return 路段集合
     */
    @Override
    public List<TunnelInfo> selectRoadList() {
        return taskMapper.selectRoadList();
    }

    /**
     * 根据路段编号查询隧道列表
     *
     * @param roadName 路段编号
     * @return 隧道集合
     */
    @Override
    public List<TunnelInfo> selectTunnelListByRoadCode(String roadName) {
        return taskMapper.selectTunnelListByRoadCode(roadName);
    }

    /**
     * 查询用户列表
     *
     * @return 用户集合
     */
    @Override
    public List<SysUser> selectUserList() {
        return taskMapper.selectUserList();
    }

    /**
     * 根据隧道ID查询隧道详情
     *
     * @param tunnelIds 隧道ID（多个用逗号分隔）
     * @return 隧道详情集合
     */
    @Override
    public List<TunnelInfo> selectTunnelDetailsByIds(String tunnelIds) {
        return taskMapper.selectTunnelDetailsByIds(tunnelIds);
    }

    /**
     * 获取出库设备列表
     *
     * @return 出库设备集合
     */
    @Override
    public List<CheckFacility> selectFacilities() {
        return taskMapper.selectFacilities();
    }

    /**
     * 获取入库设备列表
     *
     * @param taskId 任务ID
     * @return 设备列表
     */
    @Override
    public List<CheckFacility> selectInFacilities(Long taskId) {
        return taskMapper.selectInFacilities(taskId);
    }

    /**
     * 获取设备状态列表
     *
     * @param taskId 任务ID
     * @return 设备状态列表
     */
    @Override
    public List<CheckFacilityRecord> selectFacilityStatusList(Long taskId) {
        List<CheckFacilityRecord> list=taskMapper.selectFacilityStatusList(taskId);
        return list;
    }

    /**
     * 更新设备记录
     *
     * @param recordData 设备记录数据
     * @param userId     操作用户ID
     * @return 更新结果
     */
    @Override
    public int updateFacilityRecord(Map<String, Object> recordData, Long userId) {
        CheckFacilityRecord record = new CheckFacilityRecord();
        record.setId(Long.valueOf(recordData.get("id").toString()));

        if (recordData.get("beforeStatus") != null) {
            record.setBeforeStatus(Integer.valueOf(recordData.get("beforeStatus").toString()));
        }
        if (recordData.get("useStatus") != null) {
            record.setUseStatus(Integer.valueOf(recordData.get("useStatus").toString()));
        }
        if (recordData.get("afterStatus") != null) {
            record.setAfterStatus(Integer.valueOf(recordData.get("afterStatus").toString()));
        }
        if (recordData.get("remark") != null) {
            record.setRemark(recordData.get("remark").toString());
        }

        record.setModifier(userId);
        return taskMapper.updateFacilityRecord(record);
    }

    /**
     * 复制任务
     *
     * @param originalTaskId 原任务ID
     * @param roadName 新路段名称
     * @param tunnelIds 新隧道ID列表（逗号分隔）
     * @param remark 备注
     * @param userId 操作人ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int duplicateTask(Long originalTaskId, String roadName, String tunnelIds, String remark, Long userId) {
        // 1. 获取原任务信息
        Task originalTask = taskMapper.selectTaskById(originalTaskId);
        if (originalTask == null) {
            throw new ServiceException("原任务不存在");
        }

        // 2. 将原任务状态设置为已完成(2)
        Task updateOriginalTask = new Task();
        updateOriginalTask.setStatus(2); // 已完成
        updateOriginalTask.setModifier(userId);
        updateOriginalTask.setId(originalTaskId);
        taskMapper.updateTask(updateOriginalTask);

        // 3. 将原任务的设备设置为入库状态
        taskMapper.updateFacilityStatusToInStock(originalTaskId);

        // 4. 创建新任务
        Task newTask = new Task();
        newTask.setRoadName(roadName);
        newTask.setTunnelIds(tunnelIds); // 直接使用逗号分隔的隧道ID字符串
        newTask.setRemark(remark);
        newTask.setStatus(1);
        newTask.setCreator(userId);
        // 设置原任务ID
        newTask.setOriginalId(originalTaskId);

        // 插入新任务
        int result = taskMapper.insertTask(newTask);

        // 5. 复制原任务的人员配置
        List<TaskUser> originalTaskUsers = taskUserMapper.selectTaskUserByTaskId(String.valueOf(originalTaskId));
        if(CollectionUtils.isEmpty(originalTaskUsers)){
            throw new ServiceException("原任务人员配置不存在");
        }
        for (TaskUser taskUser : originalTaskUsers) {
            TaskUser newTaskUser = new TaskUser();
            newTaskUser.setTaskId(String.valueOf(newTask.getId()));
            newTaskUser.setUserId(taskUser.getUserId());
            newTaskUser.setTitle(taskUser.getTitle());
            newTaskUser.setCreator(userId);
            taskUserMapper.insertTaskUser(newTaskUser);
        }
        // 6. 复制原任务的设备到新任务，并设置为出库状态
        this.duplicateFacilityToNewTaskInJava(originalTaskId, newTask.getId(), userId);
        return result;
    }

    /**
     * 在Java代码中处理设备复制逻辑
     * 
     * @param originalTaskId 原任务ID
     * @param newTaskId 新任务ID
     * @param userId 操作人ID
     */
    private void duplicateFacilityToNewTaskInJava(Long originalTaskId, Long newTaskId, Long userId) {
        // 1. 查询原任务的设备申请记录
        List<CheckFacilityApply> originalApplies = taskMapper.selectFacilityApplyByTaskId(originalTaskId);
        if(CollectionUtils.isEmpty(originalApplies)){
            throw new ServiceException("原任务没有已出库的设备申请记录");
        }
        // 2. 为新任务创建设备申请记录
        for (CheckFacilityApply originalApply : originalApplies) {
            CheckFacilityApply newApply = new CheckFacilityApply();
            BeanUtils.copyProperties(originalApply, newApply);
            newApply.setType(1); // 出库类型
            newApply.setCreator(userId);
            newApply.setTaskId(newTaskId);
            // 插入新的设备申请记录
            checkFacilityApplyMapper.insertCheckFacilityApply(newApply);
            //查询老任务对应的设备使用记录
            List<CheckFacilityRecord> originalRecords = taskMapper.selectFacilityRecordByApplyId(originalApply.getId());
            for (CheckFacilityRecord originalRecord : originalRecords) {
                CheckFacilityRecord newRecord = new CheckFacilityRecord();
                BeanUtils.copyProperties(originalRecord, newRecord);
                newRecord.setApplyId(newApply.getId());
                newRecord.setTaskId(newTaskId);
                newRecord.setCreator(userId);
                checkFacilityRecordMapper.insertCheckFacilityRecord(newRecord);
            }
        }
    }
}