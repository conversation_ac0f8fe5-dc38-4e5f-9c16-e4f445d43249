package com.tunnel.service;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.domain.CheckFacility;
import com.tunnel.domain.CheckFacilityRecord;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskUser;
import com.tunnel.domain.TunnelInfo;

import java.util.List;
import java.util.Map;

/**
 * 任务Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface TaskService
{
    /**
     * 查询任务
     * 
     * @param id 任务主键
     * @return 任务
     */
    public Task selectTaskById(Long id);

    /**
     * 查询任务列表
     * 
     * @param task 任务
     * @return 任务集合
     */
    public List<Task> selectTaskList(Task task);

    /**
     * 新增任务
     * 
     * @param task 任务
     * @param taskUsers 任务人员列表
     * @return 结果
     */
    public int insertTask(Task task, List<TaskUser> taskUsers);

    /**
     * 修改任务
     * 
     * @param task 任务
     * @param taskUsers 任务人员列表
     * @return 结果
     */
    public int updateTask(Task task, List<TaskUser> taskUsers);

    /**
     * 批量删除任务
     * 
     * @param ids 需要删除的任务主键集合
     * @return 结果
     */
    public int deleteTaskByIds(Long[] ids);

    /**
     * 删除任务信息
     * 
     * @param id 任务主键
     * @return 结果
     */
    public int deleteTaskById(Long id);

    /**
     * 查询路段列表
     * 
     * @return 路段集合
     */
    public List<TunnelInfo> selectRoadList();

    /**
     * 根据路段编号查询隧道列表
     * 
     * @param roadCode 路段编号
     * @return 隧道集合
     */
    public List<TunnelInfo> selectTunnelListByRoadCode(String roadName);

    /**
     * 查询用户列表
     * 
     * @return 用户集合
     */
    public List<SysUser> selectUserList();

    /**
     * 根据隧道ID查询隧道详情
     * 
     * @param tunnelIds 隧道ID（多个用逗号分隔）
     * @return 隧道详情集合
     */
    public List<TunnelInfo> selectTunnelDetailsByIds(String tunnelIds);

    /**
     * 获取出库设备列表
     * 
     * @return 出库设备集合
     */
    public List<CheckFacility> selectFacilities();

    /**
     * 获取入库设备列表
     * 
     * @param taskId 任务ID
     * @return 入库设备集合
     */
    public List<CheckFacility> selectInFacilities(Long taskId);

    /**
     * 获取设备状态列表
     * 
     * @param taskId 任务ID
     * @return 设备状态记录集合
     */
    public List<CheckFacilityRecord> selectFacilityStatusList(Long taskId);

    /**
     * 更新设备记录
     * 
     * @param recordData 设备记录数据
     * @param userId 操作人ID
     * @return 结果
     */
    public int updateFacilityRecord(Map<String, Object> recordData, Long userId);

    /**
     * 复制任务
     * 
     * @param originalTaskId 原任务ID
     * @param roadName 新路段名称
     * @param tunnelIds 新隧道ID列表（逗号分隔）
     * @param remark 备注
     * @param userId 操作人ID
     * @return 结果
     */
    public int duplicateTask(Long originalTaskId, String roadName, String tunnelIds, String remark, Long userId);
}