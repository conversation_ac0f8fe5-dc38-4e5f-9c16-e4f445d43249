package com.tunnel.common.utils.word;

import com.tunnel.domain.TunnelInfo;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xwpf.usermodel.XWPFChart;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.docx4j.jaxb.Context;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.JAXBElement;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Word文档处理工具类
 */
public class WordDocumentUtil {
    
    private static final Logger log = LoggerFactory.getLogger(WordDocumentUtil.class);
    
    /**
     * 创建一个空的Word文档
     * @return WordprocessingMLPackage
     */
    public static WordprocessingMLPackage createWordDocument() {
        try {
            return WordprocessingMLPackage.createPackage();
        } catch (Exception e) {
            log.error("创建Word文档失败", e);
            throw new RuntimeException("创建Word文档失败", e);
        }
    }
    
    /**
     * 添加段落文本
     * @param document Word文档
     * @param text 文本内容
     * @param fontSize 字体大小（磅）
     * @param isBold 是否加粗
     * @param alignment 对齐方式 (1-左对齐, 2-居中, 3-右对齐)
     * @param color 颜色代码 (如"FF0000"表示红色)
     * @return P 段落对象
     */
    public static P addParagraph(WordprocessingMLPackage document, String text, int fontSize, boolean isBold, int alignment, String color) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            P p = createParagraph(text, fontSize, isBold, alignment, color);
            mdp.addObject(p);
            return p;
        } catch (Exception e) {
            log.error("添加段落失败", e);
            throw new RuntimeException("添加段落失败", e);
        }
    }
    
    /**
     * 创建段落对象
     * @param text 文本内容
     * @param fontSize 字体大小（磅）
     * @param isBold 是否加粗
     * @param alignment 对齐方式 (1-左对齐, 2-居中, 3-右对齐)
     * @param color 颜色代码 (如"FF0000"表示红色)
     * @return P 段落对象
     */
    public static P createParagraph(String text, int fontSize, boolean isBold, int alignment, String color) {
        ObjectFactory factory = new ObjectFactory();
        
        P p = factory.createP();
        
        // 设置段落对齐方式
        PPr ppr = factory.createPPr();
        Jc jc = factory.createJc();
        
        if (alignment == 1) {
            jc.setVal(JcEnumeration.LEFT);
        } else if (alignment == 2) {
            jc.setVal(JcEnumeration.CENTER);
        } else if (alignment == 3) {
            jc.setVal(JcEnumeration.RIGHT);
        } else {
            jc.setVal(JcEnumeration.LEFT); // 默认左对齐
        }
        
        ppr.setJc(jc);
        p.setPPr(ppr);
        
        // 创建文本运行
        R r = factory.createR();
        Text t = factory.createText();
        t.setValue(text);
        t.setSpace("preserve");
        r.getContent().add(t);
        
        // 设置字体样式
        RPr rpr = factory.createRPr();
        
        // 设置字体大小
        HpsMeasure sz = factory.createHpsMeasure();
        sz.setVal(BigInteger.valueOf(fontSize * 2)); // Word中字号是磅值的两倍
        rpr.setSz(sz);
        
        // 设置字体加粗
        if (isBold) {
            BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
            b.setVal(true);
            rpr.setB(b);
        }
        
        // 设置颜色
        if (color != null && !color.isEmpty()) {
            Color c = factory.createColor();
            c.setVal(color);
            rpr.setColor(c);
        }
        
        r.setRPr(rpr);
        p.getContent().add(r);
        
        return p;
    }
    
    /**
     * 添加表格
     * @param document Word文档
     * @param rows 行数
     * @param cols 列数
     * @return Tbl 表格对象
     */
    public static Tbl addTable(WordprocessingMLPackage document, int rows, int cols) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            Tbl table = createTable(rows, cols);
            mdp.addObject(table);
            return table;
        } catch (Exception e) {
            log.error("添加表格失败", e);
            throw new RuntimeException("添加表格失败", e);
        }
    }


    public static Tbl createTable(int rows, int cols){
        return WordDocumentUtil.createTable(rows, cols,3000L);
    }
    
    /**
     * 创建表格
     * @param rows 行数
     * @param cols 列数
     * @return Tbl 表格对象
     */
    public static Tbl createTable(int rows, int cols ,Long cellWidth) {
        ObjectFactory factory = new ObjectFactory();
        
        Tbl table = factory.createTbl();
        
        // 设置表格基本属性
        TblPr tblPr = factory.createTblPr();
        
        // 设置表格宽度
        TblWidth tblWidth = factory.createTblWidth();
        tblWidth.setType("pct");
        tblWidth.setW(BigInteger.valueOf(5000)); // 表格宽度，5000表示100%
        tblPr.setTblW(tblWidth);
        
        // 设置表格边框为实线（保持默认宽度）
        TblBorders borders = factory.createTblBorders();
        CTBorder solidBorder = factory.createCTBorder();
        solidBorder.setVal(STBorder.SINGLE); // 实线
        solidBorder.setColor("000000"); // 黑色
        // 不设置sz，使用默认宽度
        
        // 设置所有边框为实线
        borders.setTop(solidBorder);      // 外框线：上
        borders.setBottom(solidBorder);   // 外框线：下
        borders.setLeft(solidBorder);     // 外框线：左
        borders.setRight(solidBorder);    // 外框线：右
        borders.setInsideH(solidBorder);  // 内框线：水平
        borders.setInsideV(solidBorder);  // 内框线：垂直
        
        tblPr.setTblBorders(borders);
        
        // 设置表格布局为固定布局，确保列宽不会因内容变化而浮动
        CTTblLayoutType layout = factory.createCTTblLayoutType();
        layout.setType(STTblLayoutType.FIXED);
        tblPr.setTblLayout(layout);
        
        table.setTblPr(tblPr);
        
        // 创建表格网格（列宽）
        TblGrid tblGrid = factory.createTblGrid();
        for (int i = 0; i < cols; i++) {
            TblGridCol gridCol = factory.createTblGridCol();
            gridCol.setW(BigInteger.valueOf(cellWidth)); // 默认宽度
            tblGrid.getGridCol().add(gridCol);
        }
        table.setTblGrid(tblGrid);
        
        // 创建行和单元格
        for (int i = 0; i < rows; i++) {
            Tr row = factory.createTr();
            for (int j = 0; j < cols; j++) {
                Tc cell = factory.createTc();
                
                // 设置单元格属性
                TcPr tcPr = factory.createTcPr();
                
                // 垂直居中
                CTVerticalJc verticalJc = factory.createCTVerticalJc();
                verticalJc.setVal(STVerticalJc.CENTER);
                tcPr.setVAlign(verticalJc);
                
                cell.setTcPr(tcPr);
                
                // 添加一个空段落，确保单元格不为空
                P p = factory.createP();
                cell.getContent().add(p);
                
                row.getContent().add(cell);
            }
            table.getContent().add(row);
        }
        
        return table;
    }
    
    /**
     * 设置单元格文本
     * @param table 表格
     * @param rowIndex 行索引 (0-based)
     * @param colIndex 列索引 (0-based)
     * @param text 文本内容
     * @param fontSize 字体大小
     * @param isBold 是否加粗
     * @param isHeader 是否为表头
     * @param alignment 对齐方式 (1-左对齐, 2-居中, 3-右对齐)
     */
    public static void setCellText(Tbl table, int rowIndex, int colIndex, String text, int fontSize, boolean isBold, boolean isHeader, int alignment) {
        try {
            // 获取行
            List<Object> rows = table.getContent();
            if (rowIndex >= rows.size()) {
                throw new IndexOutOfBoundsException("行索引超出范围");
            }
            
            Tr row = (Tr) rows.get(rowIndex);
            
            // 获取单元格
            List<Object> cells = row.getContent();
            if (colIndex >= cells.size()) {
                throw new IndexOutOfBoundsException("列索引超出范围");
            }
            
            Tc cell = (Tc) cells.get(colIndex);
            
            // 清空单元格内容
            cell.getContent().clear();
            
            // 创建段落
            P p = createParagraph(text, fontSize, isBold, alignment, null);
            
            // 如果是表头，设置灰色背景
            if (isHeader) {
                ObjectFactory factory = new ObjectFactory();
                PPr ppr = p.getPPr();
                if (ppr == null) {
                    ppr = factory.createPPr();
                    p.setPPr(ppr);
                }
                
                // 表头字体加粗 - 为段落中的第一个运行设置粗体
                if (!p.getContent().isEmpty() && p.getContent().get(0) instanceof R) {
                    R run = (R)p.getContent().get(0);
                    RPr rpr = run.getRPr();
                    if (rpr == null) {
                        rpr = factory.createRPr();
                        run.setRPr(rpr);
                    }
                    BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
                    rpr.setB(b);
                }
                
                // 设置表头单元格背景色
                TcPr tcPr = cell.getTcPr();
                if (tcPr == null) {
                    tcPr = factory.createTcPr();
                    cell.setTcPr(tcPr);
                }
                
                // 设置表头单元格背景色
                CTShd shd = factory.createCTShd();
                shd.setVal(STShd.CLEAR);
                shd.setColor("auto");
                shd.setFill("DDDDDD"); // 灰色背景
                tcPr.setShd(shd);
            }
            
            cell.getContent().add(p);
        } catch (Exception e) {
            log.error("设置单元格文本失败", e);
            throw new RuntimeException("设置单元格文本失败", e);
        }
    }
    
    /**
     * 添加分页符
     * @param document Word文档
     */
    public static void addPageBreak(WordprocessingMLPackage document) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            ObjectFactory factory = new ObjectFactory();
            
            P p = factory.createP();
            R r = factory.createR();
            Br br = factory.createBr();
            br.setType(STBrType.PAGE);
            r.getContent().add(br);
            p.getContent().add(r);
            
            mdp.addObject(p);
        } catch (Exception e) {
            log.error("添加分页符失败", e);
            throw new RuntimeException("添加分页符失败", e);
        }
    }
    
    /**
     * 将BigDecimal格式化为字符串
     * 如果小数部分全是0，则只显示整数部分
     * @param value BigDecimal值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String formatBigDecimal(BigDecimal value, int scale) {
        if (value == null) {
            return "";
        }
        
        BigDecimal scaledValue = value.setScale(scale, BigDecimal.ROUND_HALF_UP);
        
        // 检查小数部分是否全为0
        if (scaledValue.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
            // 小数部分全为0，只显示整数部分
            return scaledValue.toBigInteger().toString();
        } else {
            // 有非零小数部分，显示完整的小数
            return scaledValue.toString();
        }
    }

    /**
     * 将BigDecimal格式化为字符串（得分专用，保持原始精度显示）
     * @param value BigDecimal值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String formatBigDecimalForScore(BigDecimal value, int scale) {
        if (value == null) {
            return "";
        }
        return value.setScale(scale, BigDecimal.ROUND_HALF_UP).toString();
    }
    
    /**
     * 设置单元格背景色
     * @param cell 单元格
     * @param color 颜色代码 (如"DDDDDD"表示灰色)
     */
    public static void setCellBackgroundColor(Tc cell, String color) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 获取或创建单元格属性
            TcPr tcPr = cell.getTcPr();
            if (tcPr == null) {
                tcPr = factory.createTcPr();
                cell.setTcPr(tcPr);
            }
            
            // 设置背景色
            CTShd shd = factory.createCTShd();
            shd.setVal(STShd.CLEAR);
            shd.setColor("auto");
            shd.setFill(color);
            tcPr.setShd(shd);
        } catch (Exception e) {
            log.error("设置单元格背景色失败", e);
            throw new RuntimeException("设置单元格背景色失败", e);
        }
    }


    /**
     * 一次性合并多个区域
     * @param table
     * @param rowIndex
     * @param ranges
     */
    public static void mergeMultipleCellRanges(Tbl table, int rowIndex, int[][] ranges) {
        // ranges格式：{{startCol1, endCol1}, {startCol2, endCol2}, ...}
        // 从右往左处理，避免索引变化问题
        Arrays.sort(ranges, (a, b) -> Integer.compare(b[0], a[0]));

        for (int[] range : ranges) {
            mergeRowCellsSimple(table, rowIndex, range[0], range[1]);
        }
    }
    
    /**
     * 合并表格中指定行的单元格
     *
     * @param table      表格对象
     * @param rowIndex   要合并的行索引
     * @param startCol   开始列索引
     * @param endCol     结束列索引（不包含该列）
     */
    public static void mergeRowCellsSimple(Tbl table, int rowIndex, int startCol, int endCol) {
        try {
            // 检查参数有效性
            if (table == null || rowIndex < 0 || startCol < 0 || endCol <= startCol) {
                log.error("合并单元格参数无效: table={}, rowIndex={}, startCol={}, endCol={}", 
                          table != null ? "not null" : "null", rowIndex, startCol, endCol);
                return;
            }
            
            List<Object> rows = table.getContent();
            if (rows.size() <= rowIndex) {
                log.error("合并单元格行索引超出范围: rowIndex={}, rows.size()={}", rowIndex, rows.size());
                return;
            }
            
            Tr row = (Tr) rows.get(rowIndex);
            List<Object> cells = row.getContent();
            
            if (cells.size() <= startCol) {
                log.error("合并单元格起始列索引超出范围: startCol={}, cells.size()={}", startCol, cells.size());
                return;
            }
            
            // 获取要合并的第一个单元格
            Tc firstCell = null;
            if (cells.get(startCol) instanceof Tc) {
                firstCell = (Tc) cells.get(startCol);
            } else if (cells.get(startCol) instanceof JAXBElement && 
                      ((JAXBElement<?>)cells.get(startCol)).getValue() instanceof Tc) {
                firstCell = (Tc) ((JAXBElement<?>)cells.get(startCol)).getValue();
            }
            
            if (firstCell == null) {
                log.error("未找到起始单元格: startCol={}", startCol);
                return;
            }
            
            // 设置第一个单元格的网格跨度
            TcPr tcPr = firstCell.getTcPr();
            if (tcPr == null) {
                ObjectFactory factory = new ObjectFactory();
                tcPr = factory.createTcPr();
                firstCell.setTcPr(tcPr);
            }
            
            // 计算实际合并的列数
            int actualEndCol = Math.min(endCol, cells.size() - 1);
            int gridSpan = actualEndCol - startCol + 1;
            
            // 使用ObjectFactory创建并设置网格跨度对象
            ObjectFactory factory = new ObjectFactory();
            TcPrInner.GridSpan gridSpanObj = factory.createTcPrInnerGridSpan();
            gridSpanObj.setVal(BigInteger.valueOf(gridSpan));
            tcPr.setGridSpan(gridSpanObj);
            
            // 从后往前删除其他要合并的单元格
            for (int i = actualEndCol; i > startCol; i--) {
                cells.remove(i);
            }
            
        } catch (Exception e) {
            log.error("合并单元格操作失败", e);
        }
    }
    
    /**
     * 添加大标题和图片，并在下方添加横线
     * @param document Word文档
     * @param headerText 左侧文本
     * @param imagePath 右侧图片路径（类路径）
     */
    public static void addHeaderWithImageAndLine(WordprocessingMLPackage document, String headerText, String imagePath) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            ObjectFactory factory = new ObjectFactory();
            
            // 创建一个段落用于标题和图片
            P headerP = factory.createP();
            PPr ppr = factory.createPPr();
            
            // 设置段落的间距
            PPrBase.Spacing spacing = factory.createPPrBaseSpacing();
            spacing.setBefore(BigInteger.ZERO);
            spacing.setAfter(BigInteger.ZERO);
            ppr.setSpacing(spacing);
            
            // 设置段落下边框，确保整行都有下划线
            PPrBase.PBdr pBdr = factory.createPPrBasePBdr();
            CTBorder bottomBorder = factory.createCTBorder();
            bottomBorder.setVal(STBorder.SINGLE);
            bottomBorder.setSz(BigInteger.valueOf(6)); // 下划线粗细
            bottomBorder.setColor("000000"); // 黑色
            pBdr.setBottom(bottomBorder);
            ppr.setPBdr(pBdr);
            
            headerP.setPPr(ppr);
            
            // 添加标题文本（居左）
            R textR = factory.createR();
            RPr textRPr = factory.createRPr();
            
            // 字体设置
            HpsMeasure fontSize = factory.createHpsMeasure();
            fontSize.setVal(BigInteger.valueOf(18)); // 16磅
            textRPr.setSz(fontSize);
            
            // 加粗
            BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
            textRPr.setB(bold);
            
            // 字体
            RFonts fonts = factory.createRFonts();
            fonts.setAscii("SimSun");
            fonts.setEastAsia("SimSun");
            fonts.setHAnsi("SimSun");
            textRPr.setRFonts(fonts);
            
            textR.setRPr(textRPr);
            
            // 添加文本
            Text text = factory.createText();
            text.setValue(headerText);
            text.setSpace("preserve");
            textR.getContent().add(text);
            
            headerP.getContent().add(textR);
            
            // 添加空格，推动图片到右侧
            R spaceR = factory.createR();
            RPr spaceRPr = factory.createRPr();
            
            // 设置与主文本相同的字体大小
            HpsMeasure spaceFontSize = factory.createHpsMeasure();
            spaceFontSize.setVal(BigInteger.valueOf(18)); // 16磅
            spaceRPr.setSz(spaceFontSize);
            
            spaceR.setRPr(spaceRPr);
            
            Text spaceText = factory.createText();
            
            // 使用多个空格来调整位置（根据实际情况可能需要调整数量）
            StringBuilder spaces = new StringBuilder();
            for (int i = 0; i < 56; i++) {
                spaces.append(" ");
            }
            spaceText.setValue(spaces.toString());
            spaceText.setSpace("preserve");
            spaceR.getContent().add(spaceText);
            
            headerP.getContent().add(spaceR);
            
            // 添加图片（居右）
            if (imagePath != null && !imagePath.isEmpty()) {
                try {
                    // 获取图片
                    java.io.InputStream is;
                    if (imagePath.startsWith("/")) {
                        is = WordDocumentUtil.class.getResourceAsStream(imagePath);
                        if (is == null) {
                            is = new java.io.FileInputStream(System.getProperty("user.dir") + imagePath);
                        }
                    } else {
                        is = new java.io.FileInputStream(imagePath);
                    }
                    
                    byte[] bytes = IOUtils.toByteArray(is);
                    is.close();
                    
                    // 创建图片部分
                    org.docx4j.openpackaging.parts.WordprocessingML.BinaryPartAbstractImage imagePart = 
                        org.docx4j.openpackaging.parts.WordprocessingML.BinaryPartAbstractImage.createImagePart(document, bytes);
                    
                    // 设置图片属性
                    int docPrId = 1;
                    int cNvPrId = 2;
                    org.docx4j.dml.wordprocessingDrawing.Inline inline = imagePart.createImageInline("Image", "Alt Text", docPrId, cNvPrId, false);
                    
                    // 设置图片大小
                    inline.getExtent().setCx(600000); // 宽度
                    inline.getExtent().setCy(300000); // 高度
                    
                    // 添加图片到段落
                    org.docx4j.wml.Drawing drawing = factory.createDrawing();
                    drawing.getAnchorOrInline().add(inline);
                    
                    R imgR = factory.createR();
                    imgR.getContent().add(drawing);
                    headerP.getContent().add(imgR);
                } catch (Exception e) {
                    log.error("添加图片失败", e);
                    // 添加空白占位
                    R emptyR = factory.createR();
                    Text emptyText = factory.createText();
                    emptyText.setValue(" ");
                    emptyR.getContent().add(emptyText);
                    headerP.getContent().add(emptyR);
                }
            }
            
            // 添加一个填充Run，确保下划线延伸到整行
            R fillR = factory.createR();
            RPr fillRPr = factory.createRPr();
            fillRPr.setSz(fontSize);
            fillR.setRPr(fillRPr);
            
            Text fillText = factory.createText();
            fillText.setValue("                    ");
            fillText.setSpace("preserve");
            fillR.getContent().add(fillText);
            headerP.getContent().add(fillR);
            
            // 添加段落到文档
            mdp.addObject(headerP);
            
            log.info("标题、图片和下划线添加成功");
        } catch (Exception e) {
            log.error("添加标题和图片失败", e);
            throw new RuntimeException("添加标题和图片失败", e);
        }
    }
    
    /**
     * 添加附表1标题段落
     * @param document Word文档
     * @param text 文本内容，通常为"附表1、隧道机电设施检查结果"
     * @return P 段落对象
     */
    public static P addTableTitleParagraph(WordprocessingMLPackage document, String text) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            
            ObjectFactory factory = new ObjectFactory();
            P p = factory.createP();
            
            // 设置左对齐
            PPr ppr = factory.createPPr();
            Jc jc = factory.createJc();
            jc.setVal(JcEnumeration.LEFT);
            ppr.setJc(jc);
            
            // 设置段落间距 - 不使用Spacing对象简化实现
            /*
            PPr.Spacing spacing = factory.createPPrSpacing();
            spacing.setBefore(BigInteger.valueOf(200)); // 段前
            spacing.setAfter(BigInteger.valueOf(200));  // 段后
            ppr.setSpacing(spacing);
            */
            
            p.setPPr(ppr);
            
            // 创建文本运行
            R r = factory.createR();
            Text t = factory.createText();
            t.setValue(text);
            t.setSpace("preserve");
            r.getContent().add(t);
            
            // 设置字体样式
            RPr rpr = factory.createRPr();
            
            // 设置字体大小
            HpsMeasure sz = factory.createHpsMeasure();
            sz.setVal(BigInteger.valueOf(24)); // 12磅
            rpr.setSz(sz);
            
            // 设置字体加粗
            BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
            b.setVal(true);
            rpr.setB(b);
            
            r.setRPr(rpr);
            p.getContent().add(r);
            
            mdp.addObject(p);
            return p;
        } catch (Exception e) {
            log.error("添加附表标题段落失败", e);
            throw new RuntimeException("添加附表标题段落失败", e);
        }
    }
    
    /**
     * 替换文档中的占位符
     * @param document Word文档对象
     * @param placeholder 占位符文本（不包含${}）
     * @param value 替换值
     */
    public static void replacePlaceholder(WordprocessingMLPackage document, String placeholder, String value) {
        try {
            if (document == null || placeholder == null || value == null) {
                return;
            }
            
            // 获取主文档部分
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            
            // 获取所有段落
            List<Object> docObjects = mainDocumentPart.getContent();
            
            // 遍历处理所有文本元素，查找并替换占位符
            for (Object obj : docObjects) {
                if (obj instanceof P) {
                    P p = (P) obj;
                    List<Object> runs = p.getContent();
                    
                    for (Object run : runs) {
                        if (run instanceof R) {
                            R r = (R) run;
                            List<Object> texts = r.getContent();
                            
                            for (Object textObj : texts) {
                                if (textObj instanceof JAXBElement) {
                                    JAXBElement<?> jaxbElement = (JAXBElement<?>) textObj;
                                    if (jaxbElement.getValue() instanceof Text) {
                                        Text text = (Text) jaxbElement.getValue();
                                        String textValue = text.getValue();
                                        
                                        // 检查文本是否包含占位符
                                        if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                            // 替换占位符
                                            String newValue = textValue.replace("${" + placeholder + "}", value);
                                            text.setValue(newValue);
                                        }
                                    }
                                } else if (textObj instanceof Text) {
                                    Text text = (Text) textObj;
                                    String textValue = text.getValue();
                                    
                                    // 检查文本是否包含占位符
                                    if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                        // 替换占位符
                                        String newValue = textValue.replace("${" + placeholder + "}", value);
                                        text.setValue(newValue);
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 处理表格中的文本
                if (obj instanceof Tbl) {
                    Tbl tbl = (Tbl) obj;
                    List<Object> rows = tbl.getContent();
                    
                    for (Object rowObj : rows) {
                        if (rowObj instanceof Tr) {
                            Tr row = (Tr) rowObj;
                            List<Object> cells = row.getContent();
                            
                            for (Object cellObj : cells) {
                                if (cellObj instanceof Tc) {
                                    Tc cell = (Tc) cellObj;
                                    List<Object> cellContent = cell.getContent();
                                    
                                    for (Object cellContentObj : cellContent) {
                                        if (cellContentObj instanceof P) {
                                            P p = (P) cellContentObj;
                                            List<Object> runs = p.getContent();
                                            
                                            for (Object run : runs) {
                                                if (run instanceof R) {
                                                    R r = (R) run;
                                                    List<Object> texts = r.getContent();
                                                    
                                                    for (Object textObj : texts) {
                                                        if (textObj instanceof JAXBElement) {
                                                            JAXBElement<?> jaxbElement = (JAXBElement<?>) textObj;
                                                            if (jaxbElement.getValue() instanceof Text) {
                                                                Text text = (Text) jaxbElement.getValue();
                                                                String textValue = text.getValue();
                                                                
                                                                // 检查文本是否包含占位符
                                                                if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                                                    // 替换占位符
                                                                    String newValue = textValue.replace("${" + placeholder + "}", value);
                                                                    text.setValue(newValue);
                                                                }
                                                            }
                                                        } else if (textObj instanceof Text) {
                                                            Text text = (Text) textObj;
                                                            String textValue = text.getValue();
                                                            
                                                            // 检查文本是否包含占位符
                                                            if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                                                // 替换占位符
                                                                String newValue = textValue.replace("${" + placeholder + "}", value);
                                                                text.setValue(newValue);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            log.debug("替换占位符[{}]为[{}]成功", placeholder, value);
        } catch (Exception e) {
            log.error("替换占位符[{}]失败", placeholder, e);
        }
    }


    /**
     * 替换Word文档页眉中的占位符
     * @param document Word文档对象
     * @param placeholder 占位符文本（不包含${}）
     * @param value 替换值
     */
    public static void replaceHeaderPlaceholder(WordprocessingMLPackage document, String placeholder, String value) {
        try {
            // 获取页眉部分
            List<org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart> headerParts = 
                document.getParts().getParts().values().stream()
                    .filter(part -> part instanceof org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart)
                    .map(part -> (org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart) part)
                    .collect(Collectors.toList());
            
            // 遍历所有页眉
            for (org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart headerPart : headerParts) {
                // 获取页眉内容
                List<Object> headerContent = headerPart.getContent();
                
                // 遍历处理所有文本元素，查找并替换占位符
                for (Object obj : headerContent) {
                    if (obj instanceof org.docx4j.wml.P) {
                        org.docx4j.wml.P p = (org.docx4j.wml.P) obj;
                        List<Object> runs = p.getContent();
                        
                        for (Object run : runs) {
                            if (run instanceof org.docx4j.wml.R) {
                                org.docx4j.wml.R r = (org.docx4j.wml.R) run;
                                List<Object> texts = r.getContent();
                                
                                for (Object textObj : texts) {
                                    if (textObj instanceof JAXBElement) {
                                        JAXBElement<?> jaxbElement = (JAXBElement<?>) textObj;
                                        if (jaxbElement.getValue() instanceof Text) {
                                            Text text = (Text) jaxbElement.getValue();
                                            String textValue = text.getValue();
                                            
                                            // 检查文本是否包含占位符
                                            if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                                // 替换占位符
                                                String newValue = textValue.replace("${" + placeholder + "}", value);
                                                text.setValue(newValue);
                                                log.debug("页眉中替换占位符[{}]为[{}]成功", placeholder, value);
                                            }
                                        }
                                    } else if (textObj instanceof Text) {
                                        Text text = (Text) textObj;
                                        String textValue = text.getValue();
                                        
                                        // 检查文本是否包含占位符
                                        if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                            // 替换占位符
                                            String newValue = textValue.replace("${" + placeholder + "}", value);
                                            text.setValue(newValue);
                                            log.debug("页眉中替换占位符[{}]为[{}]成功", placeholder, value);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("替换页眉占位符[{}]失败", placeholder, e);
        }
    }
    
    /**
     * 替换Word文档页脚中的占位符
     * @param document Word文档对象
     * @param placeholder 占位符文本（不包含${}）
     * @param value 替换值
     */
    public static void replaceFooterPlaceholder(WordprocessingMLPackage document, String placeholder, String value) {
        try {
            // 获取页脚部分
            List<org.docx4j.openpackaging.parts.WordprocessingML.FooterPart> footerParts = 
                document.getParts().getParts().values().stream()
                    .filter(part -> part instanceof org.docx4j.openpackaging.parts.WordprocessingML.FooterPart)
                    .map(part -> (org.docx4j.openpackaging.parts.WordprocessingML.FooterPart) part)
                    .collect(Collectors.toList());
            
            // 遍历所有页脚
            for (org.docx4j.openpackaging.parts.WordprocessingML.FooterPart footerPart : footerParts) {
                // 获取页脚内容
                List<Object> footerContent = footerPart.getContent();
                
                // 遍历处理所有文本元素，查找并替换占位符
                for (Object obj : footerContent) {
                    if (obj instanceof org.docx4j.wml.P) {
                        org.docx4j.wml.P p = (org.docx4j.wml.P) obj;
                        List<Object> runs = p.getContent();
                        
                        for (Object run : runs) {
                            if (run instanceof org.docx4j.wml.R) {
                                org.docx4j.wml.R r = (org.docx4j.wml.R) run;
                                List<Object> texts = r.getContent();
                                
                                for (Object textObj : texts) {
                                    if (textObj instanceof JAXBElement) {
                                        JAXBElement<?> jaxbElement = (JAXBElement<?>) textObj;
                                        if (jaxbElement.getValue() instanceof Text) {
                                            Text text = (Text) jaxbElement.getValue();
                                            String textValue = text.getValue();
                                            
                                            // 检查文本是否包含占位符
                                            if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                                // 替换占位符
                                                String newValue = textValue.replace("${" + placeholder + "}", value);
                                                text.setValue(newValue);
                                                log.debug("页脚中替换占位符[{}]为[{}]成功", placeholder, value);
                                            }
                                        }
                                    } else if (textObj instanceof Text) {
                                        Text text = (Text) textObj;
                                        String textValue = text.getValue();
                                        
                                        // 检查文本是否包含占位符
                                        if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                            // 替换占位符
                                            String newValue = textValue.replace("${" + placeholder + "}", value);
                                            text.setValue(newValue);
                                            log.debug("页脚中替换占位符[{}]为[{}]成功", placeholder, value);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("替换页脚占位符[{}]失败", placeholder, e);
        }
    }
    
    /**
     * 替换Word文档中所有表格单元格内的占位符
     * @param document Word文档对象
     * @param placeholder 占位符文本（不包含${}）
     * @param value 替换值
     */
    public static void replaceTableCellPlaceholder(WordprocessingMLPackage document, String placeholder, String value) {
        try {
            if (document == null || placeholder == null || value == null) {
                return;
            }
            
            // 获取主文档部分
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            
            // 获取所有表格
            List<Tbl> tables = getAllTablesFromDocument(mainDocumentPart);
            
            // 遍历所有表格
            for (Tbl tbl : tables) {
                processTableForPlaceholder(tbl, placeholder, value);
            }
            
            // 处理页眉和页脚中的表格占位符
            // 处理页眉
            List<org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart> headerParts = 
                document.getParts().getParts().values().stream()
                    .filter(part -> part instanceof org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart)
                    .map(part -> (org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart) part)
                    .collect(Collectors.toList());
            
            for (org.docx4j.openpackaging.parts.WordprocessingML.HeaderPart headerPart : headerParts) {
                List<Tbl> headerTables = new ArrayList<>();
                collectTables(headerPart.getContent(), headerTables);
                
                // 对页眉中的表格应用相同的处理逻辑
                for (Tbl tbl : headerTables) {
                    processTableForPlaceholder(tbl, placeholder, value);
                }
            }
            
            // 处理页脚
            List<org.docx4j.openpackaging.parts.WordprocessingML.FooterPart> footerParts = 
                document.getParts().getParts().values().stream()
                    .filter(part -> part instanceof org.docx4j.openpackaging.parts.WordprocessingML.FooterPart)
                    .map(part -> (org.docx4j.openpackaging.parts.WordprocessingML.FooterPart) part)
                    .collect(Collectors.toList());
            
            for (org.docx4j.openpackaging.parts.WordprocessingML.FooterPart footerPart : footerParts) {
                List<Tbl> footerTables = new ArrayList<>();
                collectTables(footerPart.getContent(), footerTables);
                
                // 对页脚中的表格应用相同的处理逻辑
                for (Tbl tbl : footerTables) {
                    processTableForPlaceholder(tbl, placeholder, value);
                }
            }
            
            log.info("替换表格单元格中占位符[{}]完成", placeholder);
        } catch (Exception e) {
            log.error("替换表格单元格中占位符[{}]失败", placeholder, e);
        }
    }
    
    /**
     * 处理表格中的占位符
     * @param table 表格对象
     * @param placeholder 占位符
     * @param value 替换值
     */
    private static void processTableForPlaceholder(Tbl table, String placeholder, String value) {
        try {
            List<Object> rows = table.getContent();
            
            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();
                    
                    for (Object cellObj : cells) {
                        // 处理单元格可能是JAXBElement的情况
                        Tc cell = null;
                        if (cellObj instanceof Tc) {
                            cell = (Tc) cellObj;
                        } else if (cellObj instanceof JAXBElement && 
                                  ((JAXBElement<?>)cellObj).getValue() instanceof Tc) {
                            cell = (Tc) ((JAXBElement<?>)cellObj).getValue();
                        }
                        
                        if (cell != null) {
                            List<Object> cellContent = cell.getContent();
                            
                            for (Object cellContentObj : cellContent) {
                                // 处理单元格内容可能是JAXBElement的情况
                                P p = null;
                                if (cellContentObj instanceof P) {
                                    p = (P) cellContentObj;
                                } else if (cellContentObj instanceof JAXBElement && 
                                          ((JAXBElement<?>)cellContentObj).getValue() instanceof P) {
                                    p = (P) ((JAXBElement<?>)cellContentObj).getValue();
                                }
                                
                                if (p != null) {
                                    // 获取段落中的所有运行
                                    List<Object> runs = p.getContent();
                                    
                                    // 检查整个段落的文本是否包含占位符
                                    StringBuilder fullText = new StringBuilder();
                                    // 保存第一个运行对象用于复制样式
                                    R firstRun = null;
                                    RPr firstRPr = null;
                                    
                                    for (Object runObj : runs) {
                                        R r = null;
                                        if (runObj instanceof R) {
                                            r = (R) runObj;
                                        } else if (runObj instanceof JAXBElement && 
                                                  ((JAXBElement<?>)runObj).getValue() instanceof R) {
                                            r = (R) ((JAXBElement<?>)runObj).getValue();
                                        }
                                        
                                        if (r != null) {
                                            // 保存第一个运行对象的字体样式
                                            if (firstRun == null) {
                                                firstRun = r;
                                                if (r.getRPr() != null) {
                                                    firstRPr = r.getRPr();
                                                }
                                            }
                                            
                                            for (Object textObj : r.getContent()) {
                                                Text text = null;
                                                if (textObj instanceof Text) {
                                                    text = (Text) textObj;
                                                } else if (textObj instanceof JAXBElement && 
                                                          ((JAXBElement<?>)textObj).getValue() instanceof Text) {
                                                    text = (Text) ((JAXBElement<?>)textObj).getValue();
                                                }
                                                
                                                if (text != null) {
                                                    fullText.append(text.getValue());
                                                }
                                            }
                                        }
                                    }
                                    
                                    // 检查完整文本是否包含占位符
                                    String completeText = fullText.toString();
                                    if (completeText.contains("${" + placeholder + "}")) {
                                        log.info("找到跨多个文本块的占位符: ${{}}", placeholder);
                                        
                                        // 清空原有内容
                                        p.getContent().clear();
                                        
                                        // 创建新的运行
                                        ObjectFactory factory = Context.getWmlObjectFactory();
                                        R newRun = factory.createR();
                                        
                                        // 创建并设置运行属性（字体、大小等）
                                        RPr rpr;
                                        if (firstRPr != null) {
                                            // 复制原有样式
                                            rpr = createRPrFrom(firstRPr);
                                        } else {
                                            // 如果没有样式，设置默认的宋体4号字体
                                            rpr = factory.createRPr();
                                            
                                            // 设置字体为宋体
                                            RFonts rfonts = factory.createRFonts();
                                            rfonts.setEastAsia("宋体");
                                            rfonts.setAscii("Times New Roman");
                                            rfonts.setHAnsi("Times New Roman");
                                            rpr.setRFonts(rfonts);
                                            
                                            // 设置字体大小为4号字体（14磅 = 28半磅）
                                            HpsMeasure size = factory.createHpsMeasure();
                                            size.setVal(BigInteger.valueOf(28));
                                            rpr.setSz(size);
                                            rpr.setSzCs(size);
                                        }
                                        
                                        newRun.setRPr(rpr);
                                        
                                        // 创建新文本
                                        Text newText = factory.createText();
                                        newText.setValue(completeText.replace("${" + placeholder + "}", value));
                                        newText.setSpace("preserve");
                                        
                                        newRun.getContent().add(newText);
                                        p.getContent().add(newRun);
                                        
                                        log.info("成功替换跨多个块的占位符[{}]为[{}]，并保留原有字体样式", placeholder, value);
                                        continue;  // 处理完整段落后继续下一个段落
                                    }
                                    
                                    // 常规处理每个运行
                                    boolean placeholderFound = false;
                                    for (Object runObj : runs) {
                                        R r = null;
                                        if (runObj instanceof R) {
                                            r = (R) runObj;
                                        } else if (runObj instanceof JAXBElement && 
                                                  ((JAXBElement<?>)runObj).getValue() instanceof R) {
                                            r = (R) ((JAXBElement<?>)runObj).getValue();
                                        }
                                        
                                        if (r != null) {
                                            List<Object> texts = r.getContent();
                                            
                                            for (Object textObj : texts) {
                                                Text text = null;
                                                if (textObj instanceof Text) {
                                                    text = (Text) textObj;
                                                } else if (textObj instanceof JAXBElement && 
                                                          ((JAXBElement<?>)textObj).getValue() instanceof Text) {
                                                    text = (Text) ((JAXBElement<?>)textObj).getValue();
                                                }
                                                
                                                if (text != null) {
                                                    String textValue = text.getValue();
                                                    
                                                    // 检查文本是否包含占位符
                                                    if (textValue != null && textValue.contains("${" + placeholder + "}")) {
                                                        // 替换占位符，保持原有字体样式
                                                        String newValue = textValue.replace("${" + placeholder + "}", value);
                                                        text.setValue(newValue);
                                                        placeholderFound = true;
                                                        log.info("表格单元格中替换占位符[{}]为[{}]成功，保留原有字体样式", placeholder, value);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    // 如果在段落中找到了占位符，但没有单个text包含完整占位符，则需要重新处理
                                    if (!placeholderFound && completeText.contains("${" + placeholder + "}")) {
                                        // 清空原有内容
                                        p.getContent().clear();
                                        
                                        // 创建新的运行
                                        ObjectFactory factory = Context.getWmlObjectFactory();
                                        R newRun = factory.createR();
                                        
                                        // 创建并设置运行属性（字体、大小等）
                                        RPr rpr;
                                        if (firstRPr != null) {
                                            // 复制原有样式
                                            rpr = createRPrFrom(firstRPr);
                                        } else {
                                            // 如果没有样式，设置默认的宋体4号字体
                                            rpr = factory.createRPr();
                                            
                                            // 设置字体为宋体
                                            RFonts rfonts = factory.createRFonts();
                                            rfonts.setEastAsia("宋体");
                                            rfonts.setAscii("Times New Roman");
                                            rfonts.setHAnsi("Times New Roman");
                                            rpr.setRFonts(rfonts);
                                            
                                            // 设置字体大小为4号字体（14磅 = 28半磅）
                                            HpsMeasure size = factory.createHpsMeasure();
                                            size.setVal(BigInteger.valueOf(28));
                                            rpr.setSz(size);
                                            rpr.setSzCs(size);
                                        }
                                        
                                        newRun.setRPr(rpr);
                                        
                                        // 创建新文本
                                        Text newText = factory.createText();
                                        newText.setValue(completeText.replace("${" + placeholder + "}", value));
                                        newText.setSpace("preserve");
                                        
                                        newRun.getContent().add(newText);
                                        p.getContent().add(newRun);
                                        
                                        log.info("成功修复分割的占位符[{}]并替换为[{}]，同时保留原有字体样式", placeholder, value);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理表格占位符失败", e);
        }
    }
    
    /**
     * 获取文档中的所有表格
     * @param documentPart 文档部分
     * @return 表格列表
     */
    private static List<Tbl> getAllTablesFromDocument(MainDocumentPart documentPart) {
        List<Tbl> tables = new ArrayList<>();
        
        List<Object> docContent = documentPart.getContent();
        collectTables(docContent, tables);
        
        return tables;
    }
    
    /**
     * 递归收集文档中的所有表格
     * @param docContent 文档内容
     * @param tables 表格列表
     */
    private static void collectTables(List<Object> docContent, List<Tbl> tables) {
        for (Object obj : docContent) {
            if (obj instanceof Tbl) {
                tables.add((Tbl) obj);
            } else if (obj instanceof P) {
                // 段落中可能有表格
                P p = (P) obj;
                for (Object o : p.getContent()) {
                    if (o instanceof JAXBElement && ((JAXBElement<?>) o).getValue() instanceof Tbl) {
                        tables.add((Tbl) ((JAXBElement<?>) o).getValue());
                    }
                }
            } else if (obj instanceof JAXBElement && ((JAXBElement<?>) obj).getValue() instanceof Tbl) {
                tables.add((Tbl) ((JAXBElement<?>) obj).getValue());
            } else if (obj instanceof SdtBlock) {
                // 内容控件中的表格
                SdtBlock sdtBlock = (SdtBlock) obj;
                if (sdtBlock.getSdtContent() != null) {
                    collectTables(sdtBlock.getSdtContent().getContent(), tables);
                }
            }
        }
    }
    
    /**
     * 从现有的RPr创建新的RPr对象，复制所有属性
     * @param sourcePr 源RPr对象
     * @return 复制的新RPr对象
     */
    private static RPr createRPrFrom(RPr sourcePr) {
        ObjectFactory factory = Context.getWmlObjectFactory();
        RPr newRPr = factory.createRPr();
        
        // 复制字体设置
        if (sourcePr.getRFonts() != null) {
            RFonts oldFonts = sourcePr.getRFonts();
            RFonts newFonts = factory.createRFonts();
            
            if (oldFonts.getAscii() != null) newFonts.setAscii(oldFonts.getAscii());
            if (oldFonts.getHAnsi() != null) newFonts.setHAnsi(oldFonts.getHAnsi());
            if (oldFonts.getEastAsia() != null) newFonts.setEastAsia(oldFonts.getEastAsia());
            if (oldFonts.getCs() != null) newFonts.setCs(oldFonts.getCs());
            
            newRPr.setRFonts(newFonts);
        } else {
            // 如果没有字体设置，默认设置为宋体
            RFonts newFonts = factory.createRFonts();
            newFonts.setEastAsia("宋体");
            newFonts.setAscii("Times New Roman");
            newFonts.setHAnsi("Times New Roman");
            newRPr.setRFonts(newFonts);
        }
        
        // 复制字体大小
        if (sourcePr.getSz() != null) {
            HpsMeasure sz = factory.createHpsMeasure();
            sz.setVal(sourcePr.getSz().getVal());
            newRPr.setSz(sz);
        } else {
            // 如果没有字体大小设置，默认设置为4号字体(28半磅)
            HpsMeasure sz = factory.createHpsMeasure();
            sz.setVal(BigInteger.valueOf(28));
            newRPr.setSz(sz);
        }
        
        if (sourcePr.getSzCs() != null) {
            HpsMeasure szCs = factory.createHpsMeasure();
            szCs.setVal(sourcePr.getSzCs().getVal());
            newRPr.setSzCs(szCs);
        } else {
            // 如果没有中文字体大小设置，默认设置为4号字体(28半磅)
            HpsMeasure szCs = factory.createHpsMeasure();
            szCs.setVal(BigInteger.valueOf(28));
            newRPr.setSzCs(szCs);
        }
        
        // 复制粗体设置
        if (sourcePr.getB() != null) {
            BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
            b.setVal(sourcePr.getB().isVal());
            newRPr.setB(b);
        }
        
        // 复制斜体设置
        if (sourcePr.getI() != null) {
            BooleanDefaultTrue i = factory.createBooleanDefaultTrue();
            i.setVal(sourcePr.getI().isVal());
            newRPr.setI(i);
        }
        
        // 复制下划线设置
        if (sourcePr.getU() != null) {
            U u = factory.createU();
            u.setVal(sourcePr.getU().getVal());
            newRPr.setU(u);
        }
        
        // 复制颜色设置
        if (sourcePr.getColor() != null) {
            Color color = factory.createColor();
            color.setVal(sourcePr.getColor().getVal());
            newRPr.setColor(color);
        }
        
        // 复制文本高亮设置
        if (sourcePr.getHighlight() != null) {
            Highlight highlight = factory.createHighlight();
            highlight.setVal(sourcePr.getHighlight().getVal());
            newRPr.setHighlight(highlight);
        }
        
        // 复制文本间距设置
        if (sourcePr.getSpacing() != null) {
            CTSignedTwipsMeasure spacing = factory.createCTSignedTwipsMeasure();
            spacing.setVal(sourcePr.getSpacing().getVal());
            newRPr.setSpacing(spacing);
        }
        
        // 复制文本缩放设置
        if (sourcePr.getW() != null) {
            CTTextScale w = factory.createCTTextScale();
            w.setVal(sourcePr.getW().getVal());
            newRPr.setW(w);
        }
        
        // 复制删除线设置
        if (sourcePr.getStrike() != null) {
            BooleanDefaultTrue strike = factory.createBooleanDefaultTrue();
            strike.setVal(sourcePr.getStrike().isVal());
            newRPr.setStrike(strike);
        }
        
        // 复制双删除线设置
        if (sourcePr.getDstrike() != null) {
            BooleanDefaultTrue dstrike = factory.createBooleanDefaultTrue();
            dstrike.setVal(sourcePr.getDstrike().isVal());
            newRPr.setDstrike(dstrike);
        }
        
        // 复制上标/下标设置
        if (sourcePr.getVertAlign() != null) {
            CTVerticalAlignRun vertAlign = factory.createCTVerticalAlignRun();
            vertAlign.setVal(sourcePr.getVertAlign().getVal());
            newRPr.setVertAlign(vertAlign);
        }
        
        return newRPr;
    }

    /**
     * 删除文档中包含指定占位符的段落
     * @param document Word文档对象
     * @param placeholder 占位符文本（不包含${}）
     */
    public static void deletePlaceholderParagraph(WordprocessingMLPackage document, String placeholder) {
        try {
            if (document == null || placeholder == null) {
                return;
            }
            
            // 获取主文档部分
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = String.valueOf( p);
                    if (paragraphText.contains("${" + placeholder + "}")) {
                        docObjects.remove(i);
                        log.info("成功删除包含占位符${{{}}}的段落", placeholder);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("删除占位符段落失败", e);
        }
    }
    
    /**
     * 新的文本替换方法 - 更可靠地替换Word文档中的占位符，保持原有字体样式
     * @param document Word文档对象
     * @param placeholder 占位符文本（不包含${}）
     * @param value 替换值
     */
    public static void replaceTextPlaceholderV2(WordprocessingMLPackage document, String placeholder, String value) {
        try {
            if (document == null || placeholder == null || value == null) {
                log.warn("替换文本参数为空: document={}, placeholder={}, value={}", 
                        document != null ? "not null" : "null", placeholder, value);
                return;
            }
            
            String targetPlaceholder = "${" + placeholder + "}";
            log.info("开始替换占位符: {} -> {}", targetPlaceholder, value);
            
            // 获取主文档部分
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            
            boolean found = false;
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    
                    // 检查段落是否包含目标占位符
                    if (paragraphContainsPlaceholder(paragraph, targetPlaceholder)) {
                        log.info("找到包含占位符的段落");
                        
                        // 替换段落中的文本，保持原有样式
                        if (replaceParagraphTextWithStyle(paragraph, targetPlaceholder, value)) {
                            found = true;
                            log.info("成功替换占位符 {} 为 {}", targetPlaceholder, value);
                        }
                    }
                }
            }
            
            if (!found) {
                log.warn("未找到占位符: {}", targetPlaceholder);
            }
            
        } catch (Exception e) {
            log.error("替换文本占位符失败: placeholder={}, value={}", placeholder, value, e);
        }
    }
    
    /**
     * 检查段落是否包含指定的占位符
     * @param paragraph 段落对象
     * @param placeholder 占位符
     * @return 是否包含占位符
     */
    private static boolean paragraphContainsPlaceholder(P paragraph, String placeholder) {
        try {
            StringBuilder fullText = new StringBuilder();
            
            // 遍历段落中的所有运行，收集文本
            for (Object runObj : paragraph.getContent()) {
                if (runObj instanceof R) {
                    R run = (R) runObj;
                    for (Object textObj : run.getContent()) {
                        if (textObj instanceof Text) {
                            Text text = (Text) textObj;
                            fullText.append(text.getValue());
                        } else if (textObj instanceof JAXBElement) {
                            JAXBElement<?> jaxbElement = (JAXBElement<?>) textObj;
                            if (jaxbElement.getValue() instanceof Text) {
                                Text text = (Text) jaxbElement.getValue();
                                fullText.append(text.getValue());
                            }
                        }
                    }
                }
            }
            
            return fullText.toString().contains(placeholder);
        } catch (Exception e) {
            log.error("检查段落占位符失败", e);
            return false;
        }
    }
    
    /**
     * 替换段落中的文本，保持占位符原有的字体样式
     * @param paragraph 段落对象
     * @param placeholder 占位符
     * @param value 替换值
     * @return 是否成功替换
     */
    private static boolean replaceParagraphTextWithStyle(P paragraph, String placeholder, String value) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 收集段落中的所有文本和样式信息
            StringBuilder fullText = new StringBuilder();
            List<RunInfo> runInfos = new ArrayList<>();
            
            // 遍历段落中的所有运行，收集文本和样式
            for (Object runObj : paragraph.getContent()) {
                if (runObj instanceof R) {
                    R run = (R) runObj;
                    StringBuilder runText = new StringBuilder();
                    
                    // 收集当前运行的文本
                    for (Object textObj : run.getContent()) {
                        if (textObj instanceof Text) {
                            Text text = (Text) textObj;
                            runText.append(text.getValue());
                        } else if (textObj instanceof JAXBElement) {
                            JAXBElement<?> jaxbElement = (JAXBElement<?>) textObj;
                            if (jaxbElement.getValue() instanceof Text) {
                                Text text = (Text) jaxbElement.getValue();
                                runText.append(text.getValue());
                            }
                        }
                    }
                    
                    // 保存运行信息
                    RunInfo runInfo = new RunInfo();
                    runInfo.text = runText.toString();
                    runInfo.startIndex = fullText.length();
                    runInfo.endIndex = fullText.length() + runText.length();
                    runInfo.rPr = run.getRPr();
                    runInfos.add(runInfo);
                    
                    fullText.append(runText);
                }
            }
            
            String completeText = fullText.toString();
            if (!completeText.contains(placeholder)) {
                return false;
            }
            
            // 找到占位符的位置
            int placeholderStart = completeText.indexOf(placeholder);
            int placeholderEnd = placeholderStart + placeholder.length();
            
            // 找到包含占位符的运行
            RPr placeholderStyle = null;
            for (RunInfo runInfo : runInfos) {
                if (runInfo.startIndex <= placeholderStart && runInfo.endIndex >= placeholderEnd) {
                    placeholderStyle = runInfo.rPr;
                    break;
                }
                // 如果占位符跨越多个运行，使用第一个包含占位符开始部分的运行样式
                if (runInfo.startIndex <= placeholderStart && runInfo.endIndex > placeholderStart) {
                    placeholderStyle = runInfo.rPr;
                    break;
                }
            }
            
            // 清空段落内容
            paragraph.getContent().clear();
            
            // 创建新的运行
            R newRun = factory.createR();
            
            // 设置样式 - 优先使用占位符的样式
            if (placeholderStyle != null) {
                newRun.setRPr(createRPrFrom(placeholderStyle));
                log.debug("使用占位符原有样式");
            } else {
                // 如果没有找到占位符样式，使用第一个运行的样式
                RPr firstStyle = runInfos.isEmpty() ? null : runInfos.get(0).rPr;
                if (firstStyle != null) {
                    newRun.setRPr(createRPrFrom(firstStyle));
                    log.debug("使用第一个运行的样式");
                } else {
                    // 设置默认样式
                    RPr rpr = factory.createRPr();
                    
                    // 设置字体
                    RFonts rfonts = factory.createRFonts();
                    rfonts.setEastAsia("宋体");
                    rfonts.setAscii("Times New Roman");
                    rfonts.setHAnsi("Times New Roman");
                    rpr.setRFonts(rfonts);
                    
                    // 设置字体大小
                    HpsMeasure size = factory.createHpsMeasure();
                    size.setVal(BigInteger.valueOf(24)); // 12磅
                    rpr.setSz(size);
                    rpr.setSzCs(size);
                    
                    newRun.setRPr(rpr);
                    log.debug("使用默认样式");
                }
            }
            
            // 创建新文本
            Text newText = factory.createText();
            newText.setValue(completeText.replace(placeholder, value));
            newText.setSpace("preserve");
            
            newRun.getContent().add(newText);
            paragraph.getContent().add(newRun);
            
            return true;
            
        } catch (Exception e) {
            log.error("替换段落文本失败", e);
            return false;
        }
    }
    
    /**
     * 运行信息类，用于保存文本运行的样式信息
     */
    private static class RunInfo {
        String text;
        int startIndex;
        int endIndex;
        RPr rPr;
    }



    /**
     * 自动生成Word目录，基于文档中的标题样式
     * @param wordMLPackage Word文档对象
     * @param placeholder 目录占位符，如"${autoTOC}"
     */
    public static void generateAutoTableOfContents(WordprocessingMLPackage wordMLPackage, String placeholder) {
        try {
            log.info("开始自动生成目录");

            // 方法1：插入TOC域（推荐）
            insertTOCField(wordMLPackage, placeholder);

        } catch (Exception e) {
            log.error("自动生成目录失败", e);
            // fallback到手动生成
            generateManualTOC(wordMLPackage, placeholder);
        }
    }

    /**
     * 插入TOC域到指定占位符位置
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符
     */
    public static void insertTOCField(WordprocessingMLPackage wordMLPackage, String placeholder) {
        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

                    if (paragraphText.contains(placeholder)) {
                        // 清空段落内容
                        paragraph.getContent().clear();

                        // 创建TOC域
                        createTOCField(paragraph, factory);

                        log.info("成功插入TOC域");
                        return;
                    }
                }
            }

            log.warn("未找到目录占位符: {}", placeholder);
        } catch (Exception e) {
            log.error("插入TOC域失败", e);
            throw e;
        }
    }


    /**
     * 获取段落中的文本内容
     * @param paragraph 段落对象
     * @return 段落文本
     */
    public static String getParagraphText(P paragraph) {
        StringBuilder text = new StringBuilder();
        for (Object content : paragraph.getContent()) {
            if (content instanceof R) {
                R run = (R) content;
                for (Object runContent : run.getContent()) {
                    if (runContent instanceof JAXBElement) {
                        JAXBElement<?> element = (JAXBElement<?>) runContent;
                        if (element.getValue() instanceof Text) {
                            Text textElement = (Text) element.getValue();
                            text.append(textElement.getValue());
                        }
                    } else if (runContent instanceof Text) {
                        Text textElement = (Text) runContent;
                        text.append(textElement.getValue());
                    }
                }
            }
        }
        return text.toString();
    }

    /**
     * 创建TOC域
     * @param paragraph 目标段落
     * @param factory 对象工厂
     */
    public static void createTOCField(P paragraph, ObjectFactory factory) {
        // 设置段落属性
        PPr ppr = paragraph.getPPr();
        if (ppr == null) {
            ppr = factory.createPPr();
            paragraph.setPPr(ppr);
        }

        // 创建域开始
        R runBegin = factory.createR();
        FldChar fldCharBegin = factory.createFldChar();
        fldCharBegin.setFldCharType(STFldCharType.BEGIN);
        runBegin.getContent().add(fldCharBegin);
        paragraph.getContent().add(runBegin);

        // 创建域代码
        R runCode = factory.createR();
        Text instrText = factory.createText();
        // TOC域代码：\o "1-3" 表示包含1-3级标题，\h 表示超链接，\z 表示隐藏页码制表符
        instrText.setValue(" TOC \\o \"1-3\" \\h \\z \\u ");
        instrText.setSpace("preserve");
        runCode.getContent().add(instrText);
        paragraph.getContent().add(runCode);

        // 创建域分隔符
        R runSeparate = factory.createR();
        FldChar fldCharSeparate = factory.createFldChar();
        fldCharSeparate.setFldCharType(STFldCharType.SEPARATE);
        runSeparate.getContent().add(fldCharSeparate);
        paragraph.getContent().add(runSeparate);

        // 添加默认TOC内容（Word会自动替换）
        R runContent = factory.createR();
        Text tocText = factory.createText();
        tocText.setValue("目录将在Word中自动生成，请在Word中右键选择\"更新域\"");
        tocText.setSpace("preserve");

        // 设置目录内容字体
        RPr rpr = factory.createRPr();
        RFonts rFonts = factory.createRFonts();
        rFonts.setEastAsia("宋体");
        rFonts.setAscii("宋体");
        rFonts.setHAnsi("宋体");
        rFonts.setCs("宋体");
        rpr.setRFonts(rFonts);

        HpsMeasure fontSize = factory.createHpsMeasure();
        fontSize.setVal(BigInteger.valueOf(24)); // 小四号
        rpr.setSz(fontSize);
        rpr.setSzCs(fontSize);

        runContent.setRPr(rpr);
        runContent.getContent().add(tocText);
        paragraph.getContent().add(runContent);

        // 创建域结束
        R runEnd = factory.createR();
        FldChar fldCharEnd = factory.createFldChar();
        fldCharEnd.setFldCharType(STFldCharType.END);
        runEnd.getContent().add(fldCharEnd);
        paragraph.getContent().add(runEnd);
    }

    /**
     * 手动生成目录（fallback方案）
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符
     */
    public static void generateManualTOC(WordprocessingMLPackage wordMLPackage, String placeholder) {
        try {
            log.info("开始手动生成目录");

            // 扫描文档中的标题
            List<TocEntry> tocEntries = scanDocumentHeadings(wordMLPackage);

            if (tocEntries.isEmpty()) {
                log.warn("未找到任何标题，无法生成目录");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, placeholder.replace("${", "").replace("}", ""), "未找到标题");
                return;
            }

            // 生成目录内容
            generateTOCContent(wordMLPackage, placeholder, tocEntries);

        } catch (Exception e) {
            log.error("手动生成目录失败", e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, placeholder.replace("${", "").replace("}", ""), "目录生成失败");
        }
    }

    /**
     * 扫描文档中的标题
     * @param wordMLPackage Word文档对象
     * @return 标题列表
     */
    public static  List<TocEntry> scanDocumentHeadings(WordprocessingMLPackage wordMLPackage) {
        List<TocEntry> tocEntries = new ArrayList<>();

        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();

            int pageNum = 1; // 简化的页码计算

            for (Object obj : docObjects) {
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    TocEntry entry = analyzeParagraphForHeading(paragraph, pageNum);
                    if (entry != null) {
                        tocEntries.add(entry);
                        log.info("找到标题: 级别{}, 内容: {}, 页码: {}", entry.getLevel(), entry.getText(), entry.getPageNumber());
                    }
                }

                // 简单的页码递增逻辑（可以改进）
                if (obj instanceof P) {
                    P p = (P) obj;
                    String text = WordDocumentUtil.getParagraphText(p);
                    if (text.contains("分页符") || text.contains("page-break") ||
                            (text.length() > 200)) { // 假设长段落可能导致分页
                        pageNum++;
                    }
                }
            }

        } catch (Exception e) {
            log.error("扫描标题失败", e);
        }

        return tocEntries;
    }

    /**
     * 分析段落是否为标题
     * @param paragraph 段落
     * @param pageNum 页码
     * @return 目录条目，如果不是标题则返回null
     */
    public static TocEntry analyzeParagraphForHeading(P paragraph, int pageNum) {
        try {
            String text = getParagraphText(paragraph).trim();
            if (text.isEmpty()) {
                return null;
            }

            // 方法1：根据样式判断
            PPr ppr = paragraph.getPPr();
            if (ppr != null && ppr.getPStyle() != null) {
                String styleId = ppr.getPStyle().getVal();
                if (styleId != null) {
                    if (styleId.toLowerCase().contains("heading1") || styleId.toLowerCase().contains("标题1")) {
                        return new TocEntry(1, text, pageNum);
                    } else if (styleId.toLowerCase().contains("heading2") || styleId.toLowerCase().contains("标题2")) {
                        return new TocEntry(2, text, pageNum);
                    } else if (styleId.toLowerCase().contains("heading3") || styleId.toLowerCase().contains("标题3")) {
                        return new TocEntry(3, text, pageNum);
                    }
                }
            }

            // 方法2：根据您的目录示例格式进行精确匹配
            // 一级标题：数字 + 空格 + 文本（如：1 项目概况、2 检测目的及依据）
            if (text.matches("^\\d+\\s+.+")) {
                return new TocEntry(1, text, pageNum);
            }

            // 二级标题：数字.数字 + 空格 + 文本（如：2.1 检测目的、3.1 主要检测人员）
            if (text.matches("^\\d+\\.\\d+\\s+.+")) {
                return new TocEntry(2, text, pageNum);
            }

            // 三级标题：数字.数字.数字 + 空格 + 文本（如：3.3.1 检测组织情况、4.6.1 分项设施评定方法）
            if (text.matches("^\\d+\\.\\d+\\.\\d+\\s+.+")) {
                return new TocEntry(3, text, pageNum);
            }

            // 附表标题：附表 + 数字、+ 文本（如：附表 1、供配电设施检查结果）
            if (text.matches("^附表\\s+\\d+、.+")) {
                return new TocEntry(1, text, pageNum);
            }

            // 方法3：根据字体大小和加粗判断（作为备用）
            boolean isBold = isParagraphBold(paragraph);
            int fontSize = getParagraphFontSize(paragraph);

            // 对于没有明确编号但格式像标题的文本
            if (isBold && fontSize >= 28) { // 四号或以上且加粗
                return new TocEntry(1, text, pageNum);
            } else if (isBold && fontSize >= 24) { // 小四号或以上且加粗
                return new TocEntry(2, text, pageNum);
            }

        } catch (Exception e) {
            log.error("分析段落标题失败", e);
        }

        return null;
    }

    /**
     * 检查段落是否加粗
     */
    public static boolean isParagraphBold(P paragraph) {
        for (Object content : paragraph.getContent()) {
            if (content instanceof R) {
                R run = (R) content;
                RPr rpr = run.getRPr();
                if (rpr != null && rpr.getB() != null && rpr.getB().isVal()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取段落字体大小
     */
    public static int getParagraphFontSize(P paragraph) {
        for (Object content : paragraph.getContent()) {
            if (content instanceof R) {
                R run = (R) content;
                RPr rpr = run.getRPr();
                if (rpr != null && rpr.getSz() != null) {
                    return rpr.getSz().getVal().intValue();
                }
            }
        }
        return 24; // 默认小四号
    }

    /**
     * 生成目录内容
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符
     * @param tocEntries 目录条目列表
     */
    public static void generateTOCContent(WordprocessingMLPackage wordMLPackage, String placeholder, List<TocEntry> tocEntries) {
        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String paragraphText = getParagraphText(paragraph);

                    if (paragraphText.contains(placeholder)) {
                        // 清空段落
                        paragraph.getContent().clear();

                        // 设置段落制表位
                        PPr ppr = paragraph.getPPr();
                        if (ppr == null) {
                            ppr = factory.createPPr();
                            paragraph.setPPr(ppr);
                        }

                        Tabs tabs = factory.createTabs();
                        org.docx4j.wml.CTTabStop tabStop = factory.createCTTabStop();
                        tabStop.setVal(org.docx4j.wml.STTabJc.RIGHT);
                        tabStop.setPos(BigInteger.valueOf(9100));
                        tabStop.setLeader(org.docx4j.wml.STTabTlc.DOT);
                        tabs.getTab().add(tabStop);
                        ppr.setTabs(tabs);

                        // 生成目录条目
                        boolean isFirst = true;
                        for (TocEntry entry : tocEntries) {
                            if (!isFirst) {
                                // 添加换行
                                R breakRun = factory.createR();
                                Br br = factory.createBr();
                                breakRun.getContent().add(br);
                                paragraph.getContent().add(breakRun);
                            } else {
                                isFirst = false;
                            }

                            // 添加缩进
                            R indentRun = factory.createR();
                            Text indentText = factory.createText();
                            StringBuilder indentBuilder = new StringBuilder();
                            int indentLevel = Math.max(0, entry.getLevel() - 1);
                            for (int k = 0; k < indentLevel; k++) {
                                indentBuilder.append("    ");
                            }
                            indentText.setValue(indentBuilder.toString());
                            indentText.setSpace("preserve");
                            indentRun.getContent().add(indentText);
                            paragraph.getContent().add(indentRun);

                            // 添加标题文本
                            R textRun = factory.createR();
                            RPr rpr = factory.createRPr();
                            RFonts rFonts = factory.createRFonts();
                            rFonts.setEastAsia("宋体");
                            rFonts.setAscii("宋体");
                            rFonts.setHAnsi("宋体");
                            rFonts.setCs("宋体");
                            rpr.setRFonts(rFonts);

                            HpsMeasure fontSize = factory.createHpsMeasure();
                            fontSize.setVal(BigInteger.valueOf(24));
                            rpr.setSz(fontSize);
                            rpr.setSzCs(fontSize);

                            textRun.setRPr(rpr);
                            Text text = factory.createText();
                            text.setValue(entry.getText());
                            text.setSpace("preserve");
                            textRun.getContent().add(text);
                            paragraph.getContent().add(textRun);

                            // 添加制表符
                            R tabRun = factory.createR();
                            tabRun.setRPr(rpr);
                            Text tabText = factory.createText();
                            tabText.setValue("\t");
                            tabText.setSpace("preserve");
                            tabRun.getContent().add(tabText);
                            paragraph.getContent().add(tabRun);

                            // 添加页码
                            R pageRun = factory.createR();
                            pageRun.setRPr(rpr);
                            Text pageText = factory.createText();
                            pageText.setValue(String.valueOf(entry.getPageNumber()));
                            pageText.setSpace("preserve");
                            pageRun.getContent().add(pageText);
                            paragraph.getContent().add(pageRun);
                        }

                        log.info("手动目录生成完成，共{}个条目", tocEntries.size());
                        return;
                    }
                }
            }

        } catch (Exception e) {
            log.error("生成目录内容失败", e);
        }
    }



    /**
     * 生成路段隧道列表并替换${roadTunnelList}占位符
     * @param wordMLPackage Word文档对象
     * @param roadTunnelList 路段隧道列表
     * @param currentTunnelName 当前隧道名称，如果为null则在前面添加路段标题
     */
    public static void generateRoadTunnelList(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> roadTunnelList, String currentTunnelName) {
        try {
            if (roadTunnelList == null || roadTunnelList.isEmpty()) {
                log.warn("路段隧道列表为空");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "roadTunnelList", "暂无数据");
                return;
            }

            // 获取路段名称（从第一个隧道获取）
            String roadName = roadTunnelList.get(0).getRoadName();

            // 获取主文档部分
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();

            // 查找包含${roadTunnelList}占位符的段落
            List<Object> docObjects = mainDocumentPart.getContent();
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = getParagraphText(p);

                    if (paragraphText.contains("${roadTunnelList}")) {
                        // 移除原占位符段落
                        docObjects.remove(i);

                        // 创建对象工厂
                        ObjectFactory factory = Context.getWmlObjectFactory();

                        int insertIndex = i; // 记录插入位置

                        // 如果currentTunnelName为null，添加路段标题
                        if (currentTunnelName == null) {
                            P titleP = createRoadTitleParagraph(factory, roadName);
                            docObjects.add(insertIndex, titleP);
                            insertIndex++;
                            log.info("添加路段标题: 1 {}隧道机电设施技术状况评定", roadName);
                        }

                        // 为每个隧道创建一个段落
                        for (int j = 0; j < roadTunnelList.size(); j++) {
                            TunnelInfo tunnel = roadTunnelList.get(j);

                            // 创建段落
                            P tunnelP = factory.createP();

                            // 创建运行
                            R run = factory.createR();

                            // 设置运行属性
                            RPr rpr = factory.createRPr();

                            // 设置字体
                            RFonts rfonts = factory.createRFonts();
                            rfonts.setEastAsia("宋体");
                            rfonts.setAscii("Times New Roman");
                            rfonts.setHAnsi("Times New Roman");
                            rpr.setRFonts(rfonts);

                            // 设置字体大小为4号字体（14磅 = 28半磅）
                            HpsMeasure size = factory.createHpsMeasure();
                            size.setVal(BigInteger.valueOf(28));
                            rpr.setSz(size);
                            rpr.setSzCs(size);

                            // 如果是当前隧道，设置加粗
                            if (currentTunnelName != null && tunnel.getTunnelName().equals(currentTunnelName)) {
                                BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
                                bold.setVal(true);
                                rpr.setB(bold);
                                rpr.setBCs(bold);
                            }

                            run.setRPr(rpr);

                            // 创建文本内容
                            Text text = factory.createText();
                            String content;
                            if (currentTunnelName == null) {
                                // 如果没有指定当前隧道，使用2、3、4这样的编号（因为1已经是路段标题）
                                content = (j + 2) + " " + tunnel.getRoadName() + "隧道机电设施技术状况评定（" + tunnel.getTunnelName() + "）";
                            } else {
                                // 如果有指定当前隧道，使用原来的编号方式
                                content = (j + 1) + "、" + tunnel.getRoadName() + "隧道机电设施技术状况评定（" + tunnel.getTunnelName() + "）";
                            }
                            text.setValue(content);
                            text.setSpace("preserve");

                            run.getContent().add(text);
                            tunnelP.getContent().add(run);

                            // 插入段落
                            docObjects.add(insertIndex + j, tunnelP);
                        }

                        // 在隧道列表后添加分页符
                        P pageBreakParagraph = factory.createP();
                        R pageBreakRun = factory.createR();
                        Br pageBreak = factory.createBr();
                        pageBreak.setType(STBrType.PAGE);
                        pageBreakRun.getContent().add(pageBreak);
                        pageBreakParagraph.getContent().add(pageBreakRun);

                        // 将分页符段落插入到隧道列表后面
                        docObjects.add(insertIndex + roadTunnelList.size(), pageBreakParagraph);
                        
                        String logMessage = currentTunnelName == null ? 
                            "成功生成路段隧道列表（含路段标题），共{}个隧道" : 
                            "成功生成隧道列表，共{}个隧道";
                        log.info(logMessage, roadTunnelList.size());
                        break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("生成路段隧道列表失败", e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "roadTunnelList", "生成隧道列表失败");
        }
    }

    /**
     * 创建路段标题段落
     * @param factory 对象工厂
     * @param roadName 路段名称
     * @return 路段标题段落
     */
    private static P createRoadTitleParagraph(ObjectFactory factory, String roadName) {
        // 创建段落
        P titleP = factory.createP();

        // 创建运行
        R run = factory.createR();

        // 设置运行属性
        RPr rpr = factory.createRPr();

        // 设置字体
        RFonts rfonts = factory.createRFonts();
        rfonts.setEastAsia("宋体");
        rfonts.setAscii("Times New Roman");
        rfonts.setHAnsi("Times New Roman");
        rpr.setRFonts(rfonts);

        // 设置字体大小为4号字体（14磅 = 28半磅）
        HpsMeasure size = factory.createHpsMeasure();
        size.setVal(BigInteger.valueOf(28));
        rpr.setSz(size);
        rpr.setSzCs(size);

        // 设置加粗
        BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
        bold.setVal(true);
        rpr.setB(bold);
        rpr.setBCs(bold);

        run.setRPr(rpr);

        // 创建文本内容
        Text text = factory.createText();
        String content = "1 " + roadName + "隧道机电设施技术状况评定";
        text.setValue(content);
        text.setSpace("preserve");

        run.getContent().add(text);
        titleP.getContent().add(run);

        return titleP;
    }


    /**
     * 替换占位符对应的表格
     * @param wordMLPackage
     * @param table
     * @param replacePlaceholder
     */
    public static void findAndGenerateTable(WordprocessingMLPackage wordMLPackage, Tbl table,String replacePlaceholder){
        replacePlaceholder="${"+replacePlaceholder+"}";
        // 获取主文档部分
        org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
        // 查找包含占位符的段落并替换为表格
        List<Object> docObjects = mainDocumentPart.getContent();
        for (int i = 0; i < docObjects.size(); i++) {
            Object obj = docObjects.get(i);
            if (obj instanceof org.docx4j.wml.P) {
                org.docx4j.wml.P paragraph = (org.docx4j.wml.P) obj;
                String paragraphText = String.valueOf(paragraph);
                log.debug("段落文本: [{}]", paragraphText);
                if (paragraphText.contains(replacePlaceholder)) {
                    // 替换占位符段落为表格
                    docObjects.set(i, table);
                    log.info("成功替换"+replacePlaceholder+"占位符为隧道表格");
                    break;
                }
            }
        }
    }


    /**
     * 创建饼图
     * 建议移动到WordDocumentUtil中
     * @param document XWPFDocument对象
     * @param categories 类别列表
     * @param values 数值列表
     * @param chartTitle 图表标题
     * @param seriesTitle 系列标题
     * @param width 图表宽度（EMU单位）
     * @param height 图表高度（EMU单位）
     * @return 创建的图表对象，失败时返回null
     */
    public static XWPFChart createPieChart(XWPFDocument document, List<String> categories, List<Integer> values,
                                     String chartTitle, String seriesTitle, int width, int height) {
        try {
            log.info("准备创建饼图，数据详情 - 类别: {}, 数值: {}", categories, values);

            // 创建图表 - 这会在文档末尾创建
            XWPFChart chart = document.createChart(width, height);
            log.info("图表创建成功，尺寸: {}x{} EMU", width, height);

            // 设置图表标题
            chart.setTitleText(chartTitle);
            chart.setTitleOverlay(false);
            log.info("图表标题设置完成: {}", chartTitle);

            // 创建图例
            XDDFChartLegend legend = chart.getOrAddLegend();
            legend.setPosition(LegendPosition.RIGHT);
            log.info("图例设置完成，位置: RIGHT");

            // 计算总数用于占比计算
            int totalValue = values.stream().mapToInt(Integer::intValue).sum();
            log.info("开始计算各类别占比，总数: {}", totalValue);

            // 创建包含占比和数量的类别标签
            String[] categoriesWithPercentage = new String[categories.size()];
            for (int idx = 0; idx < categories.size(); idx++) {
                double percentage = (values.get(idx) * 100.0) / totalValue;
                categoriesWithPercentage[idx] = String.format("%s (%d个, %.1f%%)",
                        categories.get(idx), values.get(idx), percentage);
                log.info("类别 {}: {} -> {}", idx+1, categories.get(idx), categoriesWithPercentage[idx]);
            }

            Integer[] valuesArray = values.toArray(new Integer[0]);

            log.info("数据源准备 - 增强类别数组: {}, 数值数组: {}",
                    java.util.Arrays.toString(categoriesWithPercentage),
                    java.util.Arrays.toString(valuesArray));

            XDDFDataSource<String> categoriesData = XDDFDataSourcesFactory.fromArray(categoriesWithPercentage);
            XDDFNumericalDataSource<Integer> valuesData = XDDFDataSourcesFactory.fromArray(valuesArray);
            log.info("数据源创建完成");

            // 创建饼图数据
            XDDFChartData data = chart.createData(ChartTypes.PIE, null, null);
            log.info("饼图数据结构创建完成，类型: PIE");

            XDDFChartData.Series series = data.addSeries(categoriesData, valuesData);
            series.setTitle(seriesTitle, null);
            log.info("数据系列添加完成，标题: {}", seriesTitle);

            // 绘制图表
            chart.plot(data);
            log.info("饼图绘制完成 - plot() 方法执行成功");

            // 强制刷新图表
            try {
                if (chart.getCTChart() != null) {
                    log.info("图表内部结构验证成功");
                }
            } catch (Exception verifyEx) {
                log.warn("图表结构验证失败，但继续处理: {}", verifyEx.getMessage());
            }

            return chart;

        } catch (Exception e) {
            log.error("创建饼图时发生异常", e);
            return null;
        }
    }


    /**
     * 创建饼图-3D
     * 建议移动到WordDocumentUtil中
     * @param document XWPFDocument对象
     * @param categories 类别列表
     * @param values 数值列表
     * @param chartTitle 图表标题
     * @param seriesTitle 系列标题
     * @param width 图表宽度（EMU单位）
     * @param height 图表高度（EMU单位）
     * @return 创建的图表对象，失败时返回null
     */
    public static XWPFChart createPieChart3D(XWPFDocument document, List<String> categories, List<Integer> values,
                                     String chartTitle, String seriesTitle, int width, int height) {
        try {
            log.info("准备创建饼图，数据详情 - 类别: {}, 数值: {}", categories, values);

            // 创建图表 - 这会在文档末尾创建
            XWPFChart chart = document.createChart(width, height);
            log.info("图表创建成功，尺寸: {}x{} EMU", width, height);

            // 设置图表标题
            chart.setTitleText(chartTitle);
            chart.setTitleOverlay(false);
            log.info("图表标题设置完成: {}", chartTitle);

            // 创建图例
            XDDFChartLegend legend = chart.getOrAddLegend();
            legend.setPosition(LegendPosition.RIGHT);
            log.info("图例设置完成，位置: RIGHT");

            // 启用并配置3D视图参数
            try {
                if (chart.getCTChart() != null) {
                    org.openxmlformats.schemas.drawingml.x2006.chart.CTView3D view3D =
                            chart.getCTChart().isSetView3D() ? chart.getCTChart().getView3D() : chart.getCTChart().addNewView3D();
                    // 适度的X/Y旋转角度与透视，形成三维立体效果
                    if (!view3D.isSetRotX()) {
                        view3D.addNewRotX();
                    }
                    view3D.getRotX().setVal((byte) 30);

                    if (!view3D.isSetRotY()) {
                        view3D.addNewRotY();
                    }
                    view3D.getRotY().setVal(20);

                    if (!view3D.isSetDepthPercent()) {
                        view3D.addNewDepthPercent();
                    }
                    view3D.getDepthPercent().setVal(100);

                    if (!view3D.isSetRAngAx()) {
                        view3D.addNewRAngAx();
                    }
                    view3D.getRAngAx().setVal(true);

                    if (!view3D.isSetPerspective()) {
                        view3D.addNewPerspective();
                    }
                    view3D.getPerspective().setVal((byte) 30);
                    log.info("3D视图参数设置完成: rotX=30, rotY=20, depth=100, perspective=30");
                }
            } catch (Exception viewEx) {
                log.warn("设置3D视图失败: {}", viewEx.getMessage());
            }

            // 计算总数用于占比计算
            int totalValue = values.stream().mapToInt(Integer::intValue).sum();
            log.info("开始计算各类别占比，总数: {}", totalValue);

            // 创建包含占比和数量的类别标签
            String[] categoriesWithPercentage = new String[categories.size()];
            for (int idx = 0; idx < categories.size(); idx++) {
                double percentage = (values.get(idx) * 100.0) / totalValue;
                categoriesWithPercentage[idx] = String.format("%s (%d个, %.1f%%)",
                        categories.get(idx), values.get(idx), percentage);
                log.info("类别 {}: {} -> {}", idx+1, categories.get(idx), categoriesWithPercentage[idx]);
            }

            Integer[] valuesArray = values.toArray(new Integer[0]);

            log.info("数据源准备 - 增强类别数组: {}, 数值数组: {}",
                    java.util.Arrays.toString(categoriesWithPercentage),
                    java.util.Arrays.toString(valuesArray));

            XDDFDataSource<String> categoriesData = XDDFDataSourcesFactory.fromArray(categoriesWithPercentage);
            XDDFNumericalDataSource<Integer> valuesData = XDDFDataSourcesFactory.fromArray(valuesArray);
            log.info("数据源创建完成");

            // 创建饼图数据
            XDDFChartData data = chart.createData(ChartTypes.PIE3D, null, null);
            log.info("饼图数据结构创建完成，类型: PIE");

            XDDFChartData.Series series = data.addSeries(categoriesData, valuesData);
            series.setTitle(seriesTitle, null);
            log.info("数据系列添加完成，标题: {}", seriesTitle);

            // 绘制图表
            chart.plot(data);
            log.info("饼图绘制完成 - plot() 方法执行成功");

            // 强制刷新图表
            try {
                if (chart.getCTChart() != null) {
                    log.info("图表内部结构验证成功");
                }
            } catch (Exception verifyEx) {
                log.warn("图表结构验证失败，但继续处理: {}", verifyEx.getMessage());
            }

            return chart;

        } catch (Exception e) {
            log.error("创建饼图时发生异常", e);
            return null;
        }
    }

    /**
     * 创建折线图
     * 建议移动到WordDocumentUtil中
     * @param document XWPFDocument对象
     * @param categories 类别列表
     * @param values 数值列表
     * @param chartTitle 图表标题
     * @param seriesTitle 系列标题
     * @param width 图表宽度（EMU单位）
     * @param height 图表高度（EMU单位）
     * @return 创建的图表对象，失败时返回null
     */
    public static XWPFChart createLineChart(XWPFDocument document, List<String> categories, List<Double> values,
                                      String chartTitle, String seriesTitle, int width, int height) {
        try {
            log.info("准备创建折线图，数据详情 - 类别: {}, 数值: {}", categories, values);

            // 创建图表 - 这会在文档末尾创建
            XWPFChart chart = document.createChart(width, height);
            log.info("图表创建成功，尺寸: {}x{} EMU", width, height);

            // 设置图表标题
            chart.setTitleText(chartTitle);
            chart.setTitleOverlay(false);
            log.info("图表标题设置完成: {}", chartTitle);

            // 创建图例
            XDDFChartLegend legend = chart.getOrAddLegend();
            legend.setPosition(LegendPosition.BOTTOM);
            log.info("图例设置完成，位置: BOTTOM");

            // 准备数据
            String[] categoriesArray = categories.toArray(new String[0]);
            Double[] valuesArray = values.toArray(new Double[0]);

            log.info("数据源准备 - 类别数组: {}, 数值数组: {}",
                    java.util.Arrays.toString(categoriesArray),
                    java.util.Arrays.toString(valuesArray));

            XDDFDataSource<String> categoriesData = XDDFDataSourcesFactory.fromArray(categoriesArray);
            XDDFNumericalDataSource<Double> valuesData = XDDFDataSourcesFactory.fromArray(valuesArray);
            log.info("数据源创建完成");

            // 创建分类轴
            XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
            bottomAxis.setTitle("分部设施");

            // 设置分类轴属性，确保数据点不在Y轴上显示
            try {
                // 设置分类轴的位置，让数据点在横轴上正确分布
                bottomAxis.setCrosses(AxisCrosses.AUTO_ZERO);
            } catch (Exception axisEx) {
                log.warn("设置分类轴属性失败: {}", axisEx.getMessage());
            }

            // 创建数值轴
            XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
            leftAxis.setTitle("平均得分");

            // 设置数值轴属性
            try {
                leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
                // 将Y轴设置为在分类之间穿过，避免第一数据点压在Y轴上
                leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
            } catch (Exception valueAxisEx) {
                log.warn("设置数值轴属性失败: {}", valueAxisEx.getMessage());
            }

            // 创建折线图数据
            XDDFChartData data = chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);
            log.info("折线图数据结构创建完成，类型: LINE");

            XDDFChartData.Series series = data.addSeries(categoriesData, valuesData);
            series.setTitle(seriesTitle, null);
            log.info("数据系列添加完成，标题: {}", seriesTitle);

            // 确保数据点显示标记
            if (series instanceof XDDFLineChartData.Series) {
                XDDFLineChartData.Series lineSeries = (XDDFLineChartData.Series) series;
                try {
                    // 设置标记样式，确保数据点可见
                    lineSeries.setMarkerStyle(MarkerStyle.DIAMOND);
                    log.info("折线图数据点标记设置完成");
                } catch (Exception markerEx) {
                    log.warn("设置数据点标记失败: {}", markerEx.getMessage());
                }
            }

            // 绘制图表
            chart.plot(data);
            log.info("折线图绘制完成 - plot() 方法执行成功");

            // 设置折线图样式（不平滑 + 绿色线条）
            try {
                if (data instanceof XDDFLineChartData && series instanceof XDDFLineChartData.Series) {
                    XDDFLineChartData.Series lineSeries = (XDDFLineChartData.Series) series;

                    // 设置不平滑
                    lineSeries.setSmooth(false);

                    // 设置线条颜色为绿色
                    try {
                        // 使用POI的底层API设置线条样式
                        if (chart.getCTChart() != null && chart.getCTChart().getPlotArea() != null) {
                            // 获取图表的绘图区域
                            org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea plotArea = chart.getCTChart().getPlotArea();

                            // 获取折线图
                            if (plotArea.sizeOfLineChartArray() > 0) {
                                org.openxmlformats.schemas.drawingml.x2006.chart.CTLineChart lineChart = plotArea.getLineChartArray(0);

                                // 获取第一个系列
                                if (lineChart.sizeOfSerArray() > 0) {
                                    org.openxmlformats.schemas.drawingml.x2006.chart.CTLineSer ser = lineChart.getSerArray(0);

                                    // 创建绿色填充属性
                                    if (ser.getSpPr() == null) {
                                        ser.addNewSpPr();
                                    }
                                    if (ser.getSpPr().getLn() == null) {
                                        ser.getSpPr().addNewLn();
                                    }
                                    if (ser.getSpPr().getLn().getSolidFill() == null) {
                                        ser.getSpPr().getLn().addNewSolidFill();
                                    }
                                    if (ser.getSpPr().getLn().getSolidFill().getSrgbClr() == null) {
                                        ser.getSpPr().getLn().getSolidFill().addNewSrgbClr();
                                    }

                                    // 设置绿色 (RGB: 0, 128, 0)
                                    ser.getSpPr().getLn().getSolidFill().getSrgbClr().setVal(new byte[]{0, (byte)128, 0});

                                    // 设置线条宽度
                                    ser.getSpPr().getLn().setW(25400); // 1pt = 12700 EMU, 2pt = 25400 EMU

                                    log.info("折线图颜色设置为绿色成功");
                                }
                            }
                        }
                    } catch (Exception colorEx) {
                        log.warn("设置折线图颜色失败: {}", colorEx.getMessage());
                    }

                    log.info("折线图设置完成：不平滑模式");
                }
            } catch (Exception styleEx) {
                log.warn("设置折线图样式失败，使用默认设置: {}", styleEx.getMessage());
            }

            // 强制刷新图表
            try {
                if (chart.getCTChart() != null) {
                    log.info("图表内部结构验证成功");
                }
            } catch (Exception verifyEx) {
                log.warn("图表结构验证失败，但继续处理: {}", verifyEx.getMessage());
            }

            return chart;

        } catch (Exception e) {
            log.error("创建折线图时发生异常", e);
            return null;
        }
    }
} 