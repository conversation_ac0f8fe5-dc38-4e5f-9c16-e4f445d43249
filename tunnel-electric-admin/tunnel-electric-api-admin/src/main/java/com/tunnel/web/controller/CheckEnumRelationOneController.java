package com.tunnel.web.controller;

import com.google.common.collect.Lists;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.CheckEnumRelation;
import com.tunnel.domain.FacilityInfo;
import com.tunnel.domain.TunnelCheck;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.mapper.CheckEnumMapper;
import com.tunnel.mapper.FacilityInfoMapper;
import com.tunnel.mapper.TunnelCheckMapper;
import com.tunnel.mapper.TunnelInfoMapper;
import com.tunnel.service.CheckEnumRelationService;
import com.tunnel.service.TunnelCheckService;
import com.tunnel.web.controller.dto.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分项设备检查设备对应关系Controller
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@RestController
@RequestMapping("/tunnel/electric/relationOne")
@Slf4j
public class CheckEnumRelationOneController extends BaseController {

    @Autowired
    private CheckEnumRelationService checkEnumRelationService;
    @Resource
    private TunnelCheckMapper tunnelCheckMapper;
    @Resource
    private CheckEnumMapper checkEnumMapper;
    @Resource
    private FacilityInfoMapper facilityInfoMapper;
    @Resource
    private TunnelInfoMapper tunnelInfoMapper;
    @Autowired
    private TunnelCheckService tunnelCheckService;

    /**
     * 导出分项设备检查设备对应关系列表
     */
    @Log(title = "导出检查设备列表信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCheckData")
    public void exportCheckData(HttpServletResponse response, CheckEnumRelation checkEnumRelation) {
        // 创建计时器
        org.springframework.util.StopWatch stopWatch = new org.springframework.util.StopWatch("ExportCheckData");
        stopWatch.start("初始化检查");
        
        //隧道ID不能为空
        if (Objects.isNull(checkEnumRelation.getTunnelId())) {
            throw new ServiceException("隧道ID不能为空");
        }
        TunnelInfo tunnelInfo =tunnelInfoMapper.selectTunnelInfoById(checkEnumRelation.getTunnelId());
        if(Objects.isNull(tunnelInfo)){
            throw new ServiceException("隧道不存在");
        }
        stopWatch.stop();
        
        stopWatch.start("查询检查关系数据");
        List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(checkEnumRelation);

        for (CheckEnumRelation enumRelation : checkEnumRelationList) {
            if(StringUtils.isBlank(enumRelation.getName())){
                enumRelation.setName("");
            }
        }
        stopWatch.stop();
        
        stopWatch.start("查询和处理资产数据");
        //查询当前隧道下的所有资产
        List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(checkEnumRelation.getTunnelId());
        for (FacilityInfo facilityInfo : facilityInfoList) {
            if(StringUtils.isBlank(facilityInfo.getName())){
                facilityInfo.setName("");
            }
        }
        stopWatch.stop();
        stopWatch.start("数据分组与预处理");
        // 根据partCode+itemCode进行分组
        Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream().collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));
        // 创建一个映射，用于存储设备信息
        Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();
        // 创建分值查找映射表
        Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>();        // 按编号查找
        Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>();     // 默认分值（不带name的记录）
        // 预处理checkEnumRelationList
        Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream()
            .collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));
        Map<String,BigDecimal> damageRatioMap = new HashMap<>();
        Map<String,List<String>> partItemRemarkMapTemp = new HashMap<>();
        
        stopWatch.stop();
        
        stopWatch.start("初始化评分数据");
        checkEnumRelationService.initCommonScore(checkEnumRelationList, checkEnumRelation,relationGroupMap
                ,facilityGroupMap,deviceInfoMap,codeScoreMap,defaultScoreMap,damageRatioMap,partItemRemarkMapTemp, Lists.newArrayList());


        Map<String, String> partItemRemarkMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : partItemRemarkMapTemp.entrySet()) {
            partItemRemarkMap.put(entry.getKey(), String.join(",", entry.getValue()));
        }

        stopWatch.stop();
//        checkEnumRelationList=checkEnumRelationList.stream().filter(v->Objects.equals("UPS电源.EPS电源",v.getItemName())).collect(Collectors.toList());

        stopWatch.start("按分部分组数据");
        // 按照分部进行分组
        Map<String, List<CheckEnumRelation>> partGroupedData = checkEnumRelationList.stream().collect(Collectors.groupingBy(item -> {
                    String partCode = item.getPartCode();
                    return partCode != null ? partCode : "未分类";
                }));
        stopWatch.stop();
        
        try {
            stopWatch.start("初始化Excel工作簿");
            // 获取当前年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            
            // 设置响应格式
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(currentYear + "年"+tunnelInfo.getRoadName() + tunnelInfo.getTunnelName() + "隧道定期检查报告", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            stopWatch.stop();
            
            stopWatch.start("创建Excel样式");
            // 设置标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleStyle.setFillPattern(FillPatternType.NO_FILL);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setBorderTop(BorderStyle.THIN);
            // 减小单元格内边距
            titleStyle.setIndention((short)0);
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 12);
            titleStyle.setFont(titleFont);
            
            // 设置表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillPattern(FillPatternType.NO_FILL);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            // 减小单元格内边距
            headerStyle.setIndention((short)0);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 10);
            headerStyle.setFont(headerFont);
            headerStyle.setWrapText(true);
            
            // 设置数据单元格样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            // 减小单元格内边距
            dataStyle.setIndention((short)0);
            Font dataFont = workbook.createFont();
            dataFont.setFontHeightInPoints((short) 10.5); // 设置为五号字体(10.5磅)
            dataFont.setFontName("宋体"); // 设置为宋体
            dataStyle.setFont(dataFont);
            dataStyle.setWrapText(true);
            
            // 为主要检查内容列创建特殊样式，确保文本完整显示
            CellStyle contentColumnStyle = workbook.createCellStyle();
            contentColumnStyle.cloneStyleFrom(dataStyle);
            contentColumnStyle.setWrapText(true); // 确保文本换行
            // 使用左对齐并增加微小缩进，更适合多行文本显示
            contentColumnStyle.setAlignment(HorizontalAlignment.LEFT);
            contentColumnStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentColumnStyle.setIndention((short)0); // 不设置缩进，确保文本完全靠左
            // 设置字体
            Font contentFont = workbook.createFont();
            contentFont.setFontHeightInPoints((short) 10.5); // 五号字体
            contentFont.setFontName("宋体");
            contentColumnStyle.setFont(contentFont);
            
            // 设置总计行样式
            CellStyle totalStyle = workbook.createCellStyle();
            totalStyle.setAlignment(HorizontalAlignment.CENTER);
            totalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalStyle.setFillPattern(FillPatternType.NO_FILL);
            totalStyle.setBorderBottom(BorderStyle.THIN);
            totalStyle.setBorderLeft(BorderStyle.THIN);
            totalStyle.setBorderRight(BorderStyle.THIN);
            totalStyle.setBorderTop(BorderStyle.THIN);
            Font totalFont = workbook.createFont();
            totalFont.setBold(true);
            totalStyle.setFont(totalFont);
            totalStyle.setWrapText(true); // 确保总计行自动换行
            
            // 设置分项名称样式（用于合并单元格）
            CellStyle itemNameStyle = workbook.createCellStyle();
            itemNameStyle.setAlignment(HorizontalAlignment.CENTER);
            itemNameStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            itemNameStyle.setBorderBottom(BorderStyle.THIN);
            itemNameStyle.setBorderLeft(BorderStyle.THIN);
            itemNameStyle.setBorderRight(BorderStyle.THIN);
            itemNameStyle.setBorderTop(BorderStyle.THIN);
            itemNameStyle.setWrapText(true); // 确保分项名称自动换行
            
            // 设置结果列样式（加粗显示）
            CellStyle resultStyle = workbook.createCellStyle();
            resultStyle.setAlignment(HorizontalAlignment.CENTER);
            resultStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            resultStyle.setBorderBottom(BorderStyle.THIN);
            resultStyle.setBorderLeft(BorderStyle.THIN);
            resultStyle.setBorderRight(BorderStyle.THIN);
            resultStyle.setBorderTop(BorderStyle.THIN);
            Font resultFont = workbook.createFont();
            resultFont.setBold(true);
            resultStyle.setFont(resultFont);
            resultStyle.setWrapText(true); // 确保结果列自动换行
            
            // 创建预定义的数字格式样式，避免在循环中重复创建样式
            // 数字格式 - 保留3位小数
            CellStyle resultNumberStyle3Decimal = workbook.createCellStyle();
            resultNumberStyle3Decimal.cloneStyleFrom(resultStyle);
            DataFormat resultFormat3Decimal = workbook.createDataFormat();
            resultNumberStyle3Decimal.setDataFormat(resultFormat3Decimal.getFormat("0.000"));
            resultNumberStyle3Decimal.setWrapText(true); // 确保自动换行
            resultNumberStyle3Decimal.setFillPattern(FillPatternType.NO_FILL);
            
            // 数字格式 - 保留2位小数
            CellStyle resultNumberStyle2Decimal = workbook.createCellStyle();
            resultNumberStyle2Decimal.cloneStyleFrom(resultStyle);
            DataFormat resultFormat2Decimal = workbook.createDataFormat();
            resultNumberStyle2Decimal.setDataFormat(resultFormat2Decimal.getFormat("0.00"));
            resultNumberStyle2Decimal.setWrapText(true); // 确保自动换行
            resultNumberStyle2Decimal.setFillPattern(FillPatternType.NO_FILL);
            
            // 总分行样式（红色字体）
            CellStyle totalRowStyle = workbook.createCellStyle();
            totalRowStyle.setAlignment(HorizontalAlignment.CENTER);
            totalRowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalRowStyle.setBorderBottom(BorderStyle.THIN);
            totalRowStyle.setBorderLeft(BorderStyle.THIN);
            totalRowStyle.setBorderRight(BorderStyle.THIN);
            totalRowStyle.setBorderTop(BorderStyle.THIN);
            Font totalRowFont = workbook.createFont();
            totalRowFont.setBold(true);
            totalRowFont.setColor(IndexedColors.RED.getIndex()); // 红色字体
            totalRowStyle.setFont(totalRowFont);
            totalRowStyle.setWrapText(true); // 确保自动换行
            
            // 总分行数字格式
            CellStyle totalRowNumberStyle = workbook.createCellStyle();
            totalRowNumberStyle.cloneStyleFrom(totalRowStyle);
            DataFormat totalRowFormat = workbook.createDataFormat();
            totalRowNumberStyle.setDataFormat(totalRowFormat.getFormat("0.00"));
            totalRowNumberStyle.setWrapText(true); // 确保自动换行
            totalRowNumberStyle.setFillPattern(FillPatternType.NO_FILL);
            
            // 备注行样式
            CellStyle remarkStyle = workbook.createCellStyle();
            remarkStyle.setAlignment(HorizontalAlignment.LEFT);
            remarkStyle.setVerticalAlignment(VerticalAlignment.TOP);
            remarkStyle.setBorderBottom(BorderStyle.THIN);
            remarkStyle.setBorderLeft(BorderStyle.THIN);
            remarkStyle.setBorderRight(BorderStyle.THIN);
            remarkStyle.setBorderTop(BorderStyle.THIN);
            remarkStyle.setWrapText(true); // 启用自动换行
            remarkStyle.setFillPattern(FillPatternType.NO_FILL);
            
            // 创建带自动换行的样式，用于后续循环中
            Map<CellStyle, CellStyle> wrapTextStyleCache = new HashMap<>();
            
            // 为各种基础样式创建对应的自动换行版本
            CellStyle dataStyleWrapText = workbook.createCellStyle();
            dataStyleWrapText.cloneStyleFrom(dataStyle);
            dataStyleWrapText.setWrapText(true);
            dataStyleWrapText.setFillPattern(FillPatternType.NO_FILL);
            
            CellStyle itemNameStyleWrapText = workbook.createCellStyle();
            itemNameStyleWrapText.cloneStyleFrom(itemNameStyle);
            itemNameStyleWrapText.setWrapText(true);
            itemNameStyleWrapText.setFillPattern(FillPatternType.NO_FILL);
            
            CellStyle resultStyleWrapText = workbook.createCellStyle();
            resultStyleWrapText.cloneStyleFrom(resultStyle);
            resultStyleWrapText.setWrapText(true);
            resultStyleWrapText.setFillPattern(FillPatternType.NO_FILL);
            
            stopWatch.stop();
            
            stopWatch.start("按分部创建工作表");
            // 按分部代码排序处理
            List<String> sortedPartCodes = new ArrayList<>(partGroupedData.keySet());
            Collections.sort(sortedPartCodes);
            
            // 为每个分部创建一个工作表
            for (String partCode : sortedPartCodes) {
                if (stopWatch.isRunning()) {
                    stopWatch.stop();
                }
                stopWatch.start("处理分部：" + partCode);
                List<CheckEnumRelation> partData = partGroupedData.get(partCode);
                String partName = partData.get(0).getPartName();
                // 创建工作表
                Sheet sheet = workbook.createSheet(partName);
                
                // 设置打印相关属性，适配A4纸张
                // 设置页面打印方向为纵向
                sheet.getPrintSetup().setLandscape(false);
                // 设置为A4纸张
                sheet.getPrintSetup().setPaperSize(PrintSetup.A4_PAPERSIZE);
                // 设置页面适合打印区域
                sheet.setFitToPage(true);
                // 设置页面边距，按照图片中的要求设置
                // 上：2.85cm，下：2.54cm，左：3.16cm，右：2.28cm
                // Excel中页边距单位为英寸，需要将厘米转换为英寸 (1cm = 0.393701英寸)
                sheet.setMargin(Sheet.LeftMargin, 3.16 * 0.393701);   // 左边距：3.16cm
                sheet.setMargin(Sheet.RightMargin, 2.28 * 0.393701);  // 右边距：2.28cm
                sheet.setMargin(Sheet.TopMargin, 2.85 * 0.393701);    // 上边距：2.85cm
                sheet.setMargin(Sheet.BottomMargin, 2.54 * 0.393701); // 下边距：2.54cm
                sheet.setMargin(Sheet.HeaderMargin, 0.2);
                sheet.setMargin(Sheet.FooterMargin, 0.2);
                // 设置打印区域自动适配宽度为1页，高度自动
                sheet.getPrintSetup().setFitWidth((short)1);
                sheet.getPrintSetup().setFitHeight((short)0);
                
                // 启用自动分页 - 确保每页内容完整
                sheet.setAutobreaks(true);
                
                // 设置不打印网格线
                sheet.setPrintGridlines(false);
                
                // 设置页脚居中显示页码
                sheet.getFooter().setCenter("第&P页，共&N页");
                
                // 水平居中打印
                sheet.setHorizontallyCenter(true);
                
                // 添加页眉（包含智能检测logo和标题文字）
                // 获取当前年份已在文件开头定义，可直接使用
                
                // 当前行索引（从页眉行下一行开始）
                int currentRowIndex = 1;
                
                // 设置页眉单元格样式
                CellStyle headerCellStyle = workbook.createCellStyle();
                headerCellStyle.setAlignment(HorizontalAlignment.LEFT); // 文字靠左显示
                headerCellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM); // 修改为底部对齐，使文字紧贴下面的页眉线
                headerCellStyle.setBorderBottom(BorderStyle.MEDIUM); // 只保留底部边框
                // 去掉其他三边的边框
                headerCellStyle.setBorderLeft(BorderStyle.NONE);
                headerCellStyle.setBorderRight(BorderStyle.NONE);
                headerCellStyle.setBorderTop(BorderStyle.NONE);
                
                // 设置背景色 - 已被注释
                //headerCellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
                headerCellStyle.setFillPattern(FillPatternType.NO_FILL); // 改为无填充
                
                // 设置字体为宋体五号
                Font pageHeaderFont = workbook.createFont();
                pageHeaderFont.setBold(false); // 取消加粗
                pageHeaderFont.setFontHeightInPoints((short) 10.5); // 五号字体约为10.5磅
                pageHeaderFont.setFontName("宋体"); // 设置为宋体
                headerCellStyle.setFont(pageHeaderFont);
                
                // 创建页眉行，设置较高的行高
                Row headerRow = sheet.createRow(0);
                headerRow.setHeightInPoints(30); // 调整行高为30点
                
                // 合并单元格用于页眉，从A到H列，保留I列给图片
                CellRangeAddress headerRange = new CellRangeAddress(0, 0, 0, 7); // 0-7，不包含I列
                sheet.addMergedRegion(headerRange);
                
                // 创建页眉单元格并设置样式
                Cell headerCell = headerRow.createCell(0);
                headerCell.setCellStyle(headerCellStyle);
                
                // 设置页眉文本（使用隧道信息构建标题文本）
                String headerText = currentYear + "年" + tunnelInfo.getRoadName() + tunnelInfo.getTunnelName() + "隧道定期检查报告";
                headerCell.setCellValue(headerText);
                
                // 为I列创建单独的单元格，用于放置图片
                Cell imageCell = headerRow.createCell(8); // I列对应索引8
                
                // 创建I列单元格样式（只有底部边框）
                CellStyle imageCellStyle = workbook.createCellStyle();
                imageCellStyle.setBorderBottom(BorderStyle.MEDIUM);
                imageCellStyle.setBorderLeft(BorderStyle.NONE);
                imageCellStyle.setBorderRight(BorderStyle.NONE);
                imageCellStyle.setBorderTop(BorderStyle.NONE);
                imageCellStyle.setFillPattern(FillPatternType.NO_FILL);
                
                imageCell.setCellStyle(imageCellStyle);
                
                // 确保文字完全靠左，不留任何缩进
                headerCellStyle.setIndention((short)0); // 设置为0去掉任何左侧缩进
                
                // 不再需要应用边框到合并单元格，因为只需要底部边框
                // 只设置底部边框
                RegionUtil.setBorderBottom(BorderStyle.MEDIUM, headerRange, sheet);
                
                // 添加图片到页眉（I列）
                try {
                    // 使用统一的资源加载方式获取智能检测logo图片
                    InputStream is = null;
                    String logoPath = "/public/static/zhinengjiance.png";
                    is = this.getClass().getResourceAsStream(logoPath);
                    
                    // 如果找到了图片资源，添加到Excel中
                    if (is != null) {
                        byte[] bytes = IOUtils.toByteArray(is);
                        int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);
                        is.close();
                        
                        // 创建绘图对象
                        CreationHelper helper = workbook.getCreationHelper();
                        Drawing<?> drawing = sheet.createDrawingPatriarch();
                        ClientAnchor anchor = helper.createClientAnchor();
                        
                        // 设置图片锚点类型为MOVE_AND_RESIZE，以便填充整个单元格
                        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                        
                        // 设置图片位置在I列（索引为8）
                        anchor.setCol1(8); // I列（索引8）
                        anchor.setRow1(0);
                        // 设置偏移量为0，使图片从单元格左上角开始
                        anchor.setDx1(0);
                        anchor.setDy1(0);
                        // 设置图片结束位置，确保只在I列内，并完全填充单元格
                        anchor.setCol2(9);
                        anchor.setRow2(1);
                        // 结束偏移量设为0，确保图片扩展到单元格右下角
                        anchor.setDx2(0);
                        anchor.setDy2(0);
                        
                        // 添加图片到工作表
                        Picture pict = drawing.createPicture(anchor, pictureIdx);
                        // 不再使用resize()方法，而是让图片填充整个锚点区域
                        
                        // 设置I列的宽度稍微宽一点，确保图片有足够的显示空间
                        sheet.setColumnWidth(8, 15 * 256); // 设置I列宽度为15个字符宽度
                    } else {
                        log.warn("找不到智能检测logo图片资源: {}", logoPath);
                    }
                } catch (Exception e) {
                    log.error("添加智能检测logo失败", e);
                }
                
                // 添加新的标题行，位于页眉下方
                int titleRowIndex = 1; // 页眉行的下一行
                Row appendixTitleRow = sheet.createRow(titleRowIndex);
                appendixTitleRow.setHeightInPoints(30); // 设置行高
                
                // 创建标题样式 - 使用不同名称避免冲突
                CellStyle appendixTitleStyle = workbook.createCellStyle();
                appendixTitleStyle.setAlignment(HorizontalAlignment.LEFT); // 左对齐
                appendixTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
                // 不设置边框
                appendixTitleStyle.setBorderBottom(BorderStyle.NONE);
                appendixTitleStyle.setBorderLeft(BorderStyle.NONE);
                appendixTitleStyle.setBorderRight(BorderStyle.NONE);
                appendixTitleStyle.setBorderTop(BorderStyle.NONE);
                appendixTitleStyle.setFillPattern(FillPatternType.NO_FILL); // 无填充色
                
                // 设置黑体三号字体 - 使用不同名称避免冲突
                Font appendixTitleFont = workbook.createFont();
                appendixTitleFont.setBold(true); // 加粗，模拟黑体效果
                appendixTitleFont.setFontHeightInPoints((short) 16); // 三号字体约为16磅
                appendixTitleFont.setFontName("黑体"); // 设置为黑体
                appendixTitleStyle.setFont(appendixTitleFont);
                
                // 创建标题单元格
                Cell appendixTitleCell = appendixTitleRow.createCell(0);
                appendixTitleCell.setCellStyle(appendixTitleStyle);
                
                // 设置标题文本内容
                // 获取当前sheet的索引（从0开始，显示时加1）
                int sheetIndex = workbook.getSheetIndex(sheet) + 1;
                String appendixTitleText = "附表" + sheetIndex + "、" + partName + "检查结果";
                appendixTitleCell.setCellValue(appendixTitleText);
                
                // 合并标题行单元格，占满整行
                CellRangeAddress appendixTitleRange = new CellRangeAddress(titleRowIndex, titleRowIndex, 0, 8); // 包括I列
                sheet.addMergedRegion(appendixTitleRange);
                
                // 当前行索引调整，从标题行的下一行开始
                currentRowIndex = titleRowIndex + 1;
                
                // 跟踪最大列索引，用于标题合并
                int maxColumnIndex = 3; // 至少包含前三列（名称、内容、权重）
                // 记录每个检查内容行的行号和分项名称，用于后续合并单元格
                Map<String, List<Integer>> itemNameRowsMap = new HashMap<>();
                // 用于跟踪已合并区域，避免重复合并
                Set<String> mergedRegions = new HashSet<>();
                // 按照分项代码进行分组
                Map<String, List<CheckEnumRelation>> itemGroupedData = partData.stream().collect(Collectors.groupingBy(item -> item.getItemCode()));
                // 按分项代码排序处理
                List<String> sortedItemCodes = new ArrayList<>(itemGroupedData.keySet());
                Collections.sort(sortedItemCodes);
                // 记录每个分项的起始和结束行，用于后面设置分页符
                Map<String, int[]> itemRowRanges = new HashMap<>();
                int lastItemEndRow = 0;
                
                for (String itemCode : sortedItemCodes) {
                    List<CheckEnumRelation> itemData = itemGroupedData.get(itemCode);
                    String itemName = itemData.get(0).getItemName();
                    log.info("处理分部[{}:{}]分项[{}:{}]", partCode, partName, itemCode, itemName);
                    
                    if (stopWatch.isRunning()) {
                        stopWatch.stop();
                    }
                    stopWatch.start("处理分项：" + itemName);
                    
                    // 记录当前分项的起始行
                    int itemStartRow = currentRowIndex;
                    
                    if (stopWatch.isRunning()) {
                        stopWatch.stop();
                    }
                    stopWatch.start("收集设备位置和编号");
                    
                    // 获取该分项下所有设备位置和编号的组合
                    Map<String, Set<String>> locationDevicesMap = new HashMap<>();
                    Map<String, String> locationNameMap = new HashMap<>();
                    Map<String, String> deviceNameMap = new HashMap<>();
                    // 首先收集所有位置和对应的设备编号
                    for (CheckEnumRelation relation : itemData) {
                        if (relation.getCode() != null && relation.getLocationName() != null) {
                            String locationCode = relation.getLocation() != null ? relation.getLocation() : "";
                            String deviceCode = relation.getCode();
                            String deviceKey = locationCode + "_" + deviceCode;
                            locationNameMap.put(locationCode, relation.getLocationName());
                            locationDevicesMap.computeIfAbsent(locationCode, k -> new HashSet<>()).add(deviceCode);
                            // 保存设备名称
                            if (relation.getName() != null) {
                                deviceNameMap.put(deviceKey, relation.getName());
                            }
                        }
                    }

                    // 按位置排序
                    List<String> sortedLocations = new ArrayList<>(locationDevicesMap.keySet());
                    Collections.sort(sortedLocations);

                    // 收集所有设备键（位置+编号）
                    List<DeviceInfo> allDevices = new ArrayList<>();
                    for (String locationCode : sortedLocations) {
                        Set<String> deviceCodes = locationDevicesMap.get(locationCode);
                        List<String> sortedDeviceCodes = new ArrayList<>(deviceCodes);

                        // 对设备编号排序
                        Collections.sort(sortedDeviceCodes, (a, b) -> {
                            try {
                                int numA = Integer.parseInt(a.replaceAll("[^0-9]", ""));
                                int numB = Integer.parseInt(b.replaceAll("[^0-9]", ""));
                                return Integer.compare(numA, numB);
                            } catch (NumberFormatException e) {
                                return a.compareTo(b);
                            }
                        });
                        for (String deviceCode : sortedDeviceCodes) {
                            String deviceKey = locationCode + "_" + deviceCode;
                            String deviceName = deviceNameMap.getOrDefault(deviceKey, "");
                            // 创建设备信息对象存储位置、编号和显示文本
                            DeviceInfo deviceInfo = new DeviceInfo();
                            deviceInfo.setLocationCode(locationCode);
                            deviceInfo.setDeviceCode(deviceCode);
                            deviceInfo.setLocationName(locationNameMap.getOrDefault(locationCode, ""));
                            deviceInfo.setDisplayName(deviceInfo.getLocationName() + " " + deviceCode + (StringUtils.isBlank(deviceName) ? "" : " " + deviceName));
                            allDevices.add(deviceInfo);
                        }
                    }
                    if (stopWatch.isRunning()) {
                        stopWatch.stop();
                    }
                    stopWatch.start("按检查内容分组");
                    
                    // 按照检查内容分组
                    Map<String, List<CheckEnumRelation>> contentGroupedData = itemData.stream().collect(Collectors.groupingBy(item -> item.getCheckContent()));
                    // 保留原始顺序的检查内容列表，而不是字母排序
                    List<String> contentOrder = new ArrayList<>();
                    // 使用LinkedHashSet去重，保持原始顺序
                    Set<String> processedContents = new LinkedHashSet<>();
                    // 从原始列表中提取检查内容，保持原始顺序
                    for (CheckEnumRelation relation : itemData) {
                        String checkContent = relation.getCheckContent();
                        if (processedContents.add(checkContent)) { // 如果未处理过，则添加到列表
                            contentOrder.add(checkContent);
                        }
                    }
                    if (stopWatch.isRunning()) {
                        stopWatch.stop();
                    }
                    // 每个模块最多显示多少个设备列
                    int maxDevicesPerModule = 5;
                    // 固定结果列的位置 - 基础列数(3) + 最大设备列数(5)
                    int resultColumnIndex = 3 + maxDevicesPerModule;
                    // 计算需要多少个模块
                    int totalDevices = allDevices.size();
                    int moduleCount = (int) Math.ceil((double) totalDevices / maxDevicesPerModule);
                    // 分项总加权得分和总权重 - 所有模块共用
                    BigDecimal totalWeightedScore = BigDecimal.ZERO;
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    // 表头行变量声明
                    Row partItemHeaderTitleRow = null;
                    Row partItemHeaderRow = null;
                    if (!stopWatch.isRunning()) {
                        stopWatch.start("创建Excel表格");
                    }
                    
                    // 为每个模块处理数据
                    for (int moduleIndex = 0; moduleIndex < moduleCount; moduleIndex++) {
                        // 当前模块的设备起始和结束索引
                        int startIdx = moduleIndex * maxDevicesPerModule;
                        int endIdx = Math.min(startIdx + maxDevicesPerModule, totalDevices);
                        // 获取当前模块的设备列表
                        List<DeviceInfo> moduleDevices = allDevices.subList(startIdx, endIdx);
                        
                        // 创建表头标题行 - 用于"测试点单项检测指标评分"
                        partItemHeaderTitleRow = sheet.createRow(currentRowIndex++);
                        partItemHeaderTitleRow.setHeightInPoints(24); // 设置表头标题行高度
                        
                        // 创建A-C列的占位单元格
                        for (int i = 0; i < 3; i++) {
                            Cell emptyCell = partItemHeaderTitleRow.createCell(i);
                            emptyCell.setCellValue("");
                            emptyCell.setCellStyle(headerStyle);
                        }
                        
                        // 设置"测试点单项检测指标评分"标题
                        Cell testPointTitleCell = partItemHeaderTitleRow.createCell(3);
                        testPointTitleCell.setCellValue("测试点单项检测指标评分");
                        testPointTitleCell.setCellStyle(headerStyle);
                        
                        // 创建额外的空白占位单元格，确保有足够的列用于合并
                        for (int i = 4; i <= 7; i++) {
                            Cell emptyCell = partItemHeaderTitleRow.createCell(i);
                            emptyCell.setCellValue("");
                            emptyCell.setCellStyle(headerStyle);
                        }
                        
                        // 创建最后一列的占位单元格
                        Cell lastEmptyCell = partItemHeaderTitleRow.createCell(resultColumnIndex);
                        lastEmptyCell.setCellValue("");
                        lastEmptyCell.setCellStyle(headerStyle);
                        
                        // 合并D-H列作为"测试点单项检测指标评分"
                        CellRangeAddress testPointRegion = new CellRangeAddress(
                            partItemHeaderTitleRow.getRowNum(), partItemHeaderTitleRow.getRowNum(), 3, 7);
                        sheet.addMergedRegion(testPointRegion);
                        // 为合并的区域应用边框
                        RegionUtil.setBorderBottom(BorderStyle.THIN, testPointRegion, sheet);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, testPointRegion, sheet);
                        RegionUtil.setBorderRight(BorderStyle.THIN, testPointRegion, sheet);
                        RegionUtil.setBorderTop(BorderStyle.THIN, testPointRegion, sheet);
                        
                        // 创建表头行 - 对所有模块都需要创建
                        partItemHeaderRow = sheet.createRow(currentRowIndex++);
                        partItemHeaderRow.setHeightInPoints(32); // 调整表头行高
                        
                        // 分项设施检查设备表头 - 对所有模块都需要创建
                        Cell partNameHeaderCell = partItemHeaderRow.createCell(0);
                        partNameHeaderCell.setCellValue("分项设施名称");
                        partNameHeaderCell.setCellStyle(headerStyle);

                        // 合并A列的两行单元格（分项设施名称列）
                        CellRangeAddress partNameRegion = new CellRangeAddress(
                            partItemHeaderTitleRow.getRowNum(), partItemHeaderRow.getRowNum(), 0, 0);
                        sheet.addMergedRegion(partNameRegion);
                        // 为合并的区域应用边框
                        RegionUtil.setBorderBottom(BorderStyle.THIN, partNameRegion, sheet);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, partNameRegion, sheet);
                        RegionUtil.setBorderRight(BorderStyle.THIN, partNameRegion, sheet);
                        RegionUtil.setBorderTop(BorderStyle.THIN, partNameRegion, sheet);
                        
                        // 确保合并后的单元格显示内容
                        Cell mergedPartNameCell = partItemHeaderTitleRow.getCell(0);
                        if (mergedPartNameCell == null) {
                            mergedPartNameCell = partItemHeaderTitleRow.createCell(0);
                            mergedPartNameCell.setCellStyle(headerStyle);
                        }
                        mergedPartNameCell.setCellValue("分项设施名称");

                        // 主要检查内容列
                        Cell mainCheckHeaderCell = partItemHeaderRow.createCell(1);
                        mainCheckHeaderCell.setCellValue("主要检查内容");
                        mainCheckHeaderCell.setCellStyle(headerStyle);

                        // 合并B列的两行单元格（主要检查内容列）
                        CellRangeAddress mainCheckRegion = new CellRangeAddress(
                            partItemHeaderTitleRow.getRowNum(), partItemHeaderRow.getRowNum(), 1, 1);
                        sheet.addMergedRegion(mainCheckRegion);
                        // 为合并的区域应用边框
                        RegionUtil.setBorderBottom(BorderStyle.THIN, mainCheckRegion, sheet);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, mainCheckRegion, sheet);
                        RegionUtil.setBorderRight(BorderStyle.THIN, mainCheckRegion, sheet);
                        RegionUtil.setBorderTop(BorderStyle.THIN, mainCheckRegion, sheet);
                        
                        // 确保合并后的单元格显示内容
                        Cell mergedMainCheckCell = partItemHeaderTitleRow.getCell(1);
                        if (mergedMainCheckCell == null) {
                            mergedMainCheckCell = partItemHeaderTitleRow.createCell(1);
                            mergedMainCheckCell.setCellStyle(headerStyle);
                        }
                        mergedMainCheckCell.setCellValue("主要检查内容");

                        // 权重列
                        Cell weightHeaderCell = partItemHeaderRow.createCell(2);
                        weightHeaderCell.setCellValue("权重");
                        weightHeaderCell.setCellStyle(headerStyle);
                        
                        // 合并C列的两行单元格（权重列）
                        CellRangeAddress weightRegion = new CellRangeAddress(
                            partItemHeaderTitleRow.getRowNum(), partItemHeaderRow.getRowNum(), 2, 2);
                        sheet.addMergedRegion(weightRegion);
                        // 为合并的区域应用边框
                        RegionUtil.setBorderBottom(BorderStyle.THIN, weightRegion, sheet);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, weightRegion, sheet);
                        RegionUtil.setBorderRight(BorderStyle.THIN, weightRegion, sheet);
                        RegionUtil.setBorderTop(BorderStyle.THIN, weightRegion, sheet);
                        
                        // 确保合并后的单元格显示内容
                        Cell mergedWeightCell = partItemHeaderTitleRow.getCell(2);
                        if (mergedWeightCell == null) {
                            mergedWeightCell = partItemHeaderTitleRow.createCell(2);
                            mergedWeightCell.setCellStyle(headerStyle);
                        }
                        mergedWeightCell.setCellValue("权重");

                        // 设置初始列宽
                        sheet.setColumnWidth(0, 10 * 256); // 分项设施名称列宽
                        sheet.setColumnWidth(1, 25 * 256); // 主要检查内容列宽
                        sheet.setColumnWidth(2, 6 * 256);  // 权重列宽

                        // 创建设备表头
                        int currentColumn = 3;
                        // 保留原有设备表头显示
                        for (DeviceInfo device : moduleDevices) {
                            Cell deviceHeaderCell = partItemHeaderRow.createCell(currentColumn++);
                            // 使用优化后的设备表头显示
                            String optimizedDisplayName = getOptimizedDisplayName(device);
                            deviceHeaderCell.setCellValue(optimizedDisplayName);
                            deviceHeaderCell.setCellStyle(headerStyle);
                        }

                        // 添加空列，保证结果列位置固定
                        while (currentColumn < resultColumnIndex) {
                            Cell emptyHeaderCell = partItemHeaderRow.createCell(currentColumn++);
                            emptyHeaderCell.setCellValue("/");
                            emptyHeaderCell.setCellStyle(headerStyle);
                        }

                        // 添加结果列 - 固定位置
                        Cell scoreResultHeaderCell = partItemHeaderRow.createCell(resultColumnIndex);
                        scoreResultHeaderCell.setCellValue("单项检测指标得分结果");
                        scoreResultHeaderCell.setCellStyle(headerStyle);
                        
                        // 合并结果列的两行单元格
                        CellRangeAddress resultRegion = new CellRangeAddress(
                            partItemHeaderTitleRow.getRowNum(), partItemHeaderRow.getRowNum(), resultColumnIndex, resultColumnIndex);
                        sheet.addMergedRegion(resultRegion);
                        // 为合并的区域应用边框
                        RegionUtil.setBorderBottom(BorderStyle.THIN, resultRegion, sheet);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, resultRegion, sheet);
                        RegionUtil.setBorderRight(BorderStyle.THIN, resultRegion, sheet);
                        RegionUtil.setBorderTop(BorderStyle.THIN, resultRegion, sheet);
                        
                        // 确保合并后的单元格显示内容
                        Cell mergedResultCell = partItemHeaderTitleRow.getCell(resultColumnIndex);
                        if (mergedResultCell == null) {
                            mergedResultCell = partItemHeaderTitleRow.createCell(resultColumnIndex);
                            mergedResultCell.setCellStyle(headerStyle);
                        }
                        mergedResultCell.setCellValue("单项检测指标得分结果");

                        // 创建内容行
                        for (String content : contentOrder) {
                            List<CheckEnumRelation> contentData = contentGroupedData.get(content);
                            if (contentData.isEmpty()) continue;
                            // 获取权重
                            BigDecimal weight = contentData.get(0).getWeight();
                            if (weight == null) weight = BigDecimal.ONE;
                            // 仅在第一个模块时累加权重
                            if (moduleIndex == 0) {
                                totalWeight = totalWeight.add(weight);
                            }
                            // 创建内容行
                            Row contentRow = sheet.createRow(currentRowIndex++);
                            // 给内容行设置非常高的固定行高，确保任何文本内容都能完整显示
                            contentRow.setHeightInPoints(40); // 设置足够高的行高
                            // 分项名称
                            Cell partNameCell = contentRow.createCell(0);
                            partNameCell.setCellValue(itemName);
                            partNameCell.setCellStyle(itemNameStyle);

                            // 将当前行添加到分项名称的行列表中，用于后面的合并
                            itemNameRowsMap.computeIfAbsent(itemName, k -> new ArrayList<>()).add(contentRow.getRowNum());

                            // 检查内容
                            Cell checkContentCell = contentRow.createCell(1);
                            // 预处理内容文本，确保每隔一定字符数自动换行
                            String formattedContent = formatTextWithLineBreaks(content, 15); // 每15个字符左右自动换行
                            checkContentCell.setCellValue(formattedContent);
                            checkContentCell.setCellStyle(contentColumnStyle); // 使用特殊样式

                            // 特别设置B列的内容行为自适应行高
                            contentRow.setHeight((short)-1); // 设置为-1表示自动调整行高

                            // 权重
                            Cell weightCell = contentRow.createCell(2);
                            weightCell.setCellValue(weight.doubleValue());
                            weightCell.setCellStyle(dataStyle);

                            // 统计当前行总分
                            BigDecimal rowTotalScore = BigDecimal.ZERO;
                            int validDeviceCount = 0;

                            // 填充设备得分
                            currentColumn = 3;
                            for (DeviceInfo device : moduleDevices) {
                                Cell scoreCell = contentRow.createCell(currentColumn++);
                                // 查找对应的得分记录
                                BigDecimal score = null;
                                for (CheckEnumRelation relation : contentData) {
                                    if (device.getDeviceCode().equals(relation.getCode()) &&
                                        device.getLocationCode().equals(relation.getLocation())) {
                                        score = relation.getScore();
                                        break;
                                    }
                                }
                                // 显示得分
                                if (score != null) {
                                    if (score.compareTo(BigDecimal.ZERO) < 0) {
                                        scoreCell.setCellValue("/");
                                    } else {
                                        scoreCell.setCellValue(score.doubleValue());
                                        rowTotalScore = rowTotalScore.add(score);
                                        validDeviceCount++;
                                    }
                                } else {
                                    scoreCell.setCellValue("/");
                                }
                                scoreCell.setCellStyle(dataStyle);
                            }
                            // 添加空列，保证结果列位置固定
                            while (currentColumn < resultColumnIndex) {
                                Cell emptyCell = contentRow.createCell(currentColumn++);
                                emptyCell.setCellValue("/");
                                emptyCell.setCellStyle(dataStyle);
                            }

                            // 计算加权得分结果
                            BigDecimal rowAverage = BigDecimal.ZERO;
                            if (validDeviceCount > 0) {
                                rowAverage = rowTotalScore.divide(new BigDecimal(validDeviceCount), 2, RoundingMode.HALF_EVEN);
                            }
                            BigDecimal weightedScore = rowAverage.multiply(weight);

                            // 仅在第一个模块时累加加权得分
                            if (moduleIndex == 0) {
                                totalWeightedScore = totalWeightedScore.add(weightedScore);
                            }

                            // 得分结果单元格 - 固定位置
                            Cell resultScoreCell = contentRow.createCell(resultColumnIndex);
                            // 预先设置自动换行样式，确保即使后面未设置样式也能自动换行
                            resultScoreCell.setCellStyle(resultNumberStyle3Decimal);

                            // 创建Excel公式，而不是直接设置值
                            // 检查是否有有效设备
                            if (validDeviceCount > 0) {
                                // 查找相同内容的所有行（所有模块）
                                List<Integer> sameContentRows = new ArrayList<>();

                                // 当前行checkContent
                                String currentCheckContent = content;

                                // 遍历工作表中的所有行，查找相同checkContent的行
                                for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                                    Row row = sheet.getRow(i);
                                    if (row != null) {
                                        Cell contentCell = row.getCell(1); // checkContent位于第二列
                                        if (contentCell != null && contentCell.getCellType() == CellType.STRING) {
                                            String cellContent = contentCell.getStringCellValue();
                                            // 找到相同内容的行
                                            if (currentCheckContent.equals(cellContent.replaceAll("\\n",""))) {
                                                sameContentRows.add(i);
                                            }
                                        }
                                    }
                                }

                                // 判断当前行是否是最后一个模块中相同内容的行
                                boolean isLastModuleForContent = moduleIndex == moduleCount - 1;

                                // 如果不是最后一个模块，则不显示分数
                                if (!isLastModuleForContent) {
                                    resultScoreCell.setCellValue("/");
                                    resultScoreCell.setCellStyle(dataStyleWrapText); // 使用带自动换行的样式
                                } else {
                                    // 构建计算平均分的公式部分
                                    StringBuilder avgFormula = new StringBuilder("AVERAGE(");
                                    boolean hasValidScores = false;

                                    // 收集有效单元格引用
                                    List<String> validCellRefs = new ArrayList<>();

                                    // 收集所有模块中相同内容行的有效设备得分
                                        for (Integer rowIndex : sameContentRows) {
                                            Row row = sheet.getRow(rowIndex);

                                            // 确保行属于当前分项，通过检查第一列的值
                                            Cell checkPartNameCell = row.getCell(0);
                                            if (checkPartNameCell == null || !itemName.equals(checkPartNameCell.getStringCellValue())) {
                                                continue; // 跳过不属于当前分项的行
                                            }

                                            // 处理该行的D-H列（索引3-7）
                                            for (int col = 3; col < 8; col++) { // 根据用户要求，固定检查D-H列
                                                Cell cell = row.getCell(col);
                                            if (cell != null) {
                                                String cellValue = "";
                                                // 获取单元格值，无论什么类型
                                                if (cell.getCellType() == CellType.NUMERIC) {
                                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                                } else if (cell.getCellType() == CellType.STRING) {
                                                    cellValue = cell.getStringCellValue();
                                                } else if (cell.getCellType() == CellType.FORMULA) {
                                                    try {
                                                        cellValue = String.valueOf(cell.getNumericCellValue());
                                                    } catch (Exception e) {
                                                        cellValue = cell.getStringCellValue();
                                                    }
                                                }
                                                
                                                // 根据单元格内容判断是否为有效数值
                                                if (!Objects.equals(cellValue, "/")) {
                                                    // 生成单元格引用 (A1格式)
                                                    String cellRef = CellReference.convertNumToColString(col) + (rowIndex + 1);
                                                    validCellRefs.add(cellRef);
                                                    hasValidScores = true;
                                                }
                                                }
                                            }
                                        }

                                    // 修改判断逻辑：只要在所有相关模块中有有效分数就计算结果
                                        if (hasValidScores) {
                                            // 处理超过255个参数的情况
                                            if (validCellRefs.size() <= 255) {
                                                // 正常情况下，少于255个参数时使用单个AVERAGE函数
                                                avgFormula.append(String.join(",", validCellRefs));
                                                avgFormula.append(")");

                                                // 获取权重单元格引用
                                                String weightCellRef = CellReference.convertNumToColString(2) +
                                                                    (contentRow.getRowNum() + 1);

                                            // 构建最终公式：平均值*权重 并四舍五入到3位小数
                                                String formula = "ROUNDBANK(" + avgFormula.toString() + "*" + weightCellRef + ", 3)";

                                                // 设置单元格公式
                                                resultScoreCell.setCellFormula(formula);
                                                // 确保应用带自动换行的样式
                                                resultScoreCell.setCellStyle(resultNumberStyle3Decimal);
                                            } else {
                                                // 当参数超过255个时，拆分为多个SUM函数，然后整体SUM，最后求平均值
                                                int batchCount = (int) Math.ceil(validCellRefs.size() / 250.0); // 每批最多250个，留余量
                                                StringBuilder sumOfSumsFormula = new StringBuilder("(");

                                                for (int batch = 0; batch < batchCount; batch++) {
                                                    int batchStartIdx = batch * 250;
                                                    int batchEndIdx = Math.min(batchStartIdx + 250, validCellRefs.size());

                                                    // 创建当前批次的SUM函数
                                                    if (batch > 0) {
                                                        sumOfSumsFormula.append("+");
                                                    }

                                                    sumOfSumsFormula.append("SUM(");
                                                    List<String> batchRefs = validCellRefs.subList(batchStartIdx, batchEndIdx);
                                                    sumOfSumsFormula.append(String.join(",", batchRefs));
                                                    sumOfSumsFormula.append(")");
                                                }

                                                sumOfSumsFormula.append(")");

                                                // 获取权重单元格引用
                                                String weightCellRef = CellReference.convertNumToColString(2) +
                                                                    (contentRow.getRowNum() + 1);

                                            // 构建最终公式：(总和/有效单元格数量)*权重 并四舍五入到3位小数
                                                String formula = "ROUNDBANK((" + sumOfSumsFormula.toString() + "/" + validCellRefs.size() + ")*" + weightCellRef + ", 3)";

                                                // 设置单元格公式
                                                resultScoreCell.setCellFormula(formula);
                                                // 确保应用带自动换行的样式
                                                resultScoreCell.setCellStyle(resultNumberStyle3Decimal);
                                            }
                                    } else {
                                        // 如果所有模块都没有有效得分，设置为权重值(而不是0或"/")
                                        // 获取行的权重值
                                        BigDecimal weightValue = weight; // 直接使用当前内容的权重值
                                        resultScoreCell.setCellValue(weightValue.doubleValue());
                                        resultScoreCell.setCellStyle(resultNumberStyle3Decimal);
                                        log.info("分项[{}]检查内容[{}]在所有模块中都没有有效得分，填充权重值: {}", itemName, content, weightValue);
                                    }
                                }
                            } else {
                                // 如果没有有效设备，但是在最后一个模块中，也应该根据所有模块的数据计算得分
                                // 判断当前行是否是最后一个模块中相同内容的行
                                boolean isLastModuleForContent = moduleIndex == moduleCount - 1;
                                
                                if (isLastModuleForContent) {
                                    // 构建计算平均分的公式部分
                                    StringBuilder avgFormula = new StringBuilder("AVERAGE(");
                                    boolean hasValidScores = false;
                                    
                                    // 收集有效单元格引用
                                    List<String> validCellRefs = new ArrayList<>();
                                    
                                    // 查找相同内容的所有行
                                    List<Integer> allSameContentRows = new ArrayList<>();
                                    for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                                        Row row = sheet.getRow(i);
                                        if (row != null) {
                                            Cell contentCell = row.getCell(1); // checkContent位于第二列
                                            if (contentCell != null && contentCell.getCellType() == CellType.STRING) {
                                                String cellContent = contentCell.getStringCellValue();
                                                // 找到相同内容且属于同一分项的行
                                                if (content.equals(cellContent.replaceAll("\\n",""))) {
                                                    Cell nameCell = row.getCell(0);
                                                    if (nameCell != null && nameCell.getCellType() == CellType.STRING 
                                                            && itemName.equals(nameCell.getStringCellValue())) {
                                                        allSameContentRows.add(i);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    // 收集所有模块中相同内容行的有效设备得分
                                    for (Integer rowIndex : allSameContentRows) {
                                        Row row = sheet.getRow(rowIndex);
                                        
                                        // 处理该行的D-H列（索引3-7）
                                        for (int col = 3; col < 8; col++) {
                                            Cell cell = row.getCell(col);
                                            if (cell != null) {
                                                String cellValue = "";
                                                // 获取单元格值，无论什么类型
                                                if (cell.getCellType() == CellType.NUMERIC) {
                                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                                } else if (cell.getCellType() == CellType.STRING) {
                                                    cellValue = cell.getStringCellValue();
                                                } else if (cell.getCellType() == CellType.FORMULA) {
                                                    try {
                                                        cellValue = String.valueOf(cell.getNumericCellValue());
                                                    } catch (Exception e) {
                                                        cellValue = cell.getStringCellValue();
                                                    }
                                                }
                                                
                                                // 根据单元格内容判断是否为有效数值
                                                if (!Objects.equals(cellValue, "/")) {
                                                    // 生成单元格引用 (A1格式)
                                                    String cellRef = CellReference.convertNumToColString(col) + (rowIndex + 1);
                                                    validCellRefs.add(cellRef);
                                                    hasValidScores = true;
                                                }
                                            }
                                        }
                                    }
                                    
                                    if (hasValidScores) {
                                        // 构建与上面相同的计算公式
                                        if (validCellRefs.size() <= 255) {
                                            avgFormula.append(String.join(",", validCellRefs));
                                            avgFormula.append(")");
                                            
                                            String weightCellRef = CellReference.convertNumToColString(2) +
                                                                (contentRow.getRowNum() + 1);
                                            
                                            String formula = "ROUNDBANK(" + avgFormula.toString() + "*" + weightCellRef + ", 3)";
                                            resultScoreCell.setCellFormula(formula);
                                            resultScoreCell.setCellStyle(resultNumberStyle3Decimal);
                                        } else {
                                            // 参数过多的处理逻辑，拆分为多个SUM函数，然后整体SUM，最后求平均值
                                            int batchCount = (int) Math.ceil(validCellRefs.size() / 250.0);
                                            StringBuilder sumOfSumsFormula = new StringBuilder("(");
                                            
                                            for (int batch = 0; batch < batchCount; batch++) {
                                                int batchStartIdx = batch * 250;
                                                int batchEndIdx = Math.min(batchStartIdx + 250, validCellRefs.size());
                                                
                                                if (batch > 0) {
                                                    sumOfSumsFormula.append("+");
                                                }
                                                
                                                sumOfSumsFormula.append("SUM(");
                                                List<String> batchRefs = validCellRefs.subList(batchStartIdx, batchEndIdx);
                                                sumOfSumsFormula.append(String.join(",", batchRefs));
                                                sumOfSumsFormula.append(")");
                                            }
                                            
                                            sumOfSumsFormula.append(")");
                                            
                                            String weightCellRef = CellReference.convertNumToColString(2) +
                                                                (contentRow.getRowNum() + 1);
                                            
                                            String formula = "ROUNDBANK((" + sumOfSumsFormula.toString() + "/" + validCellRefs.size() + ")*" + weightCellRef + ", 3)";
                                            resultScoreCell.setCellFormula(formula);
                                            resultScoreCell.setCellStyle(resultNumberStyle3Decimal);
                                }
                            } else {
                                        // 如果所有模块都没有有效得分，设置为权重值(而不是0或"/")
                                        // 获取行的权重值 - 直接使用已经获取的weight变量
                                        BigDecimal weightValue = weight; // 使用之前获取的权重
                                        resultScoreCell.setCellValue(weightValue.doubleValue());
                                        resultScoreCell.setCellStyle(resultNumberStyle3Decimal);
                                        log.info("分项[{}]检查内容[{}]在所有模块中都没有有效得分，填充权重值: {}", itemName, content, weightValue);
                                    }
                                } else {
                                    // 非最后一个模块，设置为"/"
                                resultScoreCell.setCellValue("/");
                                    resultScoreCell.setCellStyle(dataStyleWrapText);
                                }
                            }
                        }
                        // 在最后一个模块时添加总分行
                        if (moduleIndex == moduleCount - 1) {
                            if (stopWatch.isRunning()) {
                                stopWatch.stop();
                            }
                            stopWatch.start("生成总分行和备注");
                            // 添加总分行
                            Row totalRow = sheet.createRow(currentRowIndex++);
                            totalRow.setHeightInPoints(22); // 调整总分行高度

                            // 创建总分标签单元格，加入分项名称
                            Cell totalLabelCell = totalRow.createCell(0);
                            totalLabelCell.setCellValue(itemName + "检测项目得分");
                            totalLabelCell.setCellStyle(totalRowStyle);

                            // 填充中间的空白单元格，为了合并
                            for (int i = 1; i < resultColumnIndex; i++) {
                                Cell emptyCell = totalRow.createCell(i);
                                emptyCell.setCellValue("/");
                                emptyCell.setCellStyle(totalRowStyle);
                            }
                            // 计算总得分
                            // 创建并添加总分值单元格 - 固定位置
                            Cell finalScoreCell = totalRow.createCell(resultColumnIndex);
                            // 预先设置自动换行样式，确保即使后面未设置样式也能自动换行
                            finalScoreCell.setCellStyle(totalRowNumberStyle);
                            if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
                                // 使用Excel公式计算总分
                                // 收集同一分项下所有内容行的索引，同时过滤掉I列值为"/"的行
                                List<Integer> allContentRowIndices = new ArrayList<>();
                                List<Integer> validScoreRowIndices = new ArrayList<>();

                                // 遍历工作表中的所有行，查找相同分项名称的行
                                for (int i = 1; i < sheet.getLastRowNum(); i++) {
                                    Row row = sheet.getRow(i);
                                    if (row != null) {
                                        Cell firstCell = row.getCell(0);
                                        if (firstCell != null) {
                                            String cellValue = firstCell.getStringCellValue();
                                            // 检查是否是同一个分项的内容行（通过检查第一列的值是否为itemName）
                                            if (itemName.equals(cellValue)) {
                                                // 检查第二列（主要检查内容）是否有值，以确保这是一个内容行而不是表头或总分行
                                                Cell secondCell = row.getCell(1);
                                                if (secondCell != null && !secondCell.getStringCellValue().isEmpty()) {
                                                    // 检查是否为最后一个模块的数据
                                                    Cell resultCell = row.getCell(resultColumnIndex);
                                                    allContentRowIndices.add(i);

                                                    // 检查I列的值是否为"/"
                                                    boolean isSlash = false;
                                                    if (resultCell != null && resultCell.getCellType() == CellType.STRING &&
                                                        "/".equals(resultCell.getStringCellValue())) {
                                                        isSlash = true;
                                                    }

                                                    // 只有I列不是"/"的行才用于计算总分
                                                    if (!isSlash) {
                                                        validScoreRowIndices.add(i);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                // 使用Excel公式计算总分
                                StringBuilder scoreSumPart = new StringBuilder();
                                StringBuilder weightSumPart = new StringBuilder();

                                // 获取列引用字母
                                String resultColumnLetter = CellReference.convertNumToColString(resultColumnIndex);
                                String weightColumnLetter = CellReference.convertNumToColString(2);

                                // 检查是否有有效的分数行
                                if (validScoreRowIndices.isEmpty()) {
                                    // 如果没有任何有效的分数行，总分也显示为"/"
                                    finalScoreCell.setCellValue("/");
                                    finalScoreCell.setCellStyle(dataStyleWrapText); // 使用带自动换行的样式
                                } else {
                                    // 构建公式使用有效的行索引
                                    for (int i = 0; i < validScoreRowIndices.size(); i++) {
                                        int rowIdx = validScoreRowIndices.get(i);

                                        if (i > 0) {
                                            scoreSumPart.append(",");
                                            weightSumPart.append(",");
                                        }

                                        scoreSumPart.append(resultColumnLetter).append(rowIdx + 1);
                                        weightSumPart.append(weightColumnLetter).append(rowIdx + 1);
                                    }

                                    String formula = "ROUNDBANK(SUM(" + scoreSumPart.toString() + ")/SUM(" + weightSumPart.toString() + ")*100,2)";
                                    finalScoreCell.setCellFormula(formula);
                                    finalScoreCell.setCellStyle(totalRowNumberStyle);
                                }
                            } else {
                                // 如果没有有效权重，设置为0
                                finalScoreCell.setCellValue("/");
                                finalScoreCell.setCellStyle(dataStyleWrapText); // 使用带自动换行的样式
                            }
                            // 合并总分行的单元格
                            String totalRowRegionKey = (currentRowIndex - 1) + ":" + (currentRowIndex - 1) + ":0:" + (resultColumnIndex - 1);
                            if (!mergedRegions.contains(totalRowRegionKey)) {
                                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex - 1, currentRowIndex - 1, 0, resultColumnIndex - 1));
                                mergedRegions.add(totalRowRegionKey);
                            }
                            // 添加备注行
                            int remarkStartRow = currentRowIndex;
                            Row remarkRow = sheet.createRow(currentRowIndex++);
                            remarkRow.setHeightInPoints(18); // 调整备注行高度

                            // 创建一个更合理高度的备注区域
                            for (int r = 0; r < 2; r++) { // 从3行减至2行
                                Row additionalRow = sheet.createRow(currentRowIndex++);
                                additionalRow.setHeightInPoints(18); // 统一设置较小行高
                            }
                            // 使用预定义的备注样式
                            Cell remarkLabelCell = remarkRow.createCell(0);
                            remarkLabelCell.setCellValue("备注：");
                            remarkLabelCell.setCellStyle(remarkStyle);

                            // 检查是否有任何扣分项
                            // 创建备注内容
                            StringBuilder remarkContent;
                            String remarkKey =tunnelInfo.getId()+ "_" + partCode + "_" + itemCode;
                            if (!partItemRemarkMap.containsKey(remarkKey)) {
                                // 如果没有扣分，显示"此栏空白"
                                remarkContent = new StringBuilder("此栏空白");
                            } else {
                                // 有扣分，创建标准备注
                                remarkContent = new StringBuilder("备注：");
                                remarkContent.append(partItemRemarkMap.get(remarkKey));
                            }
                            // 如果damageRatioMap包含该分项的设备损坏比，添加到备注末尾
                            if (damageRatioMap.containsKey(itemName)) {
                                BigDecimal damageRatio = damageRatioMap.get(itemName);
                                BigDecimal goodRatio = BigDecimal.valueOf(100).multiply(damageRatio);
                                // 确保有空格分隔
                                if (remarkContent.length() > 0 && !remarkContent.toString().equals("此栏空白")) {
                                    remarkContent.append("，");
                                } else if (remarkContent.toString().equals("此栏空白")) {
                                    remarkContent = new StringBuilder();
                                }
                                // 添加设备完好率
                                remarkContent.append(itemName).append("完好率为").append(goodRatio.setScale(0, RoundingMode.HALF_UP)).append("%");
                            }

                            // 设置备注内容
                            Cell remarkContentCell = remarkRow.createCell(0);
                            remarkContentCell.setCellValue(remarkContent.toString());
                            remarkContentCell.setCellStyle(remarkStyle);

                            // 填充第一行的其他单元格，确保包含I列(索引8)
                            int mergeEndColumn = Math.max(resultColumnIndex - 1, 8); // 确保至少合并到I列(索引8)
                            for (int i = 1; i <= mergeEndColumn; i++) {
                                Cell emptyCell = remarkRow.createCell(i);
                                emptyCell.setCellValue("");
                                emptyCell.setCellStyle(remarkStyle);
                            }

                            // 为后续4行添加带边框的空单元格
                            for (int rowIdx = remarkStartRow + 1; rowIdx < remarkStartRow + 4; rowIdx++) {
                                Row additionalRow = sheet.getRow(rowIdx);
                                if (additionalRow == null) {
                                    additionalRow = sheet.createRow(rowIdx);
                                }

                                // 为每行的所有列添加带边框的空单元格
                                for (int colIdx = 0; colIdx <= mergeEndColumn; colIdx++) {
                                    Cell emptyCell = additionalRow.createCell(colIdx);
                                    emptyCell.setCellValue("");
                                    emptyCell.setCellStyle(remarkStyle); // 使用相同的边框样式
                                }
                            }

                            // 创建一个单一的合并区域
                            String remarkRegionKey = remarkStartRow + ":" + (remarkStartRow + 2) + ":0:" + mergeEndColumn; // 从4行减至3行
                            if (!mergedRegions.contains(remarkRegionKey)) {
                                sheet.addMergedRegion(new CellRangeAddress(remarkStartRow, remarkStartRow + 2, 0, mergeEndColumn)); // 从4行减至3行
                                mergedRegions.add(remarkRegionKey);
                            }

                            // 记录当前分项的结束行位置（包括备注区）
                            itemRowRanges.put(itemName, new int[]{itemStartRow, currentRowIndex - 1});
                            lastItemEndRow = currentRowIndex - 1;
                            if (stopWatch.isRunning()) {
                                stopWatch.stop();
                            }
                        }
                        
                        if (!stopWatch.isRunning()) {
                            stopWatch.start("处理分页和表头设置");
                        }
                        // 处理分页：确保相同分项的内容尽量放在同一页
                        for (Map.Entry<String, int[]> entry : itemRowRanges.entrySet()) {
                            String currentItemName = entry.getKey();
                            int[] rowRange = entry.getValue();
                            int startRow = rowRange[0];
                            int endRow = rowRange[1];
                            
                            // 估算此分项的行数
                            int rowCount = endRow - startRow + 1;
                            
                            // 获取上一个处理过的分项的结束行
                            int previousEndRow = -1;
                            for (Map.Entry<String, int[]> prevEntry : itemRowRanges.entrySet()) {
                                if (!prevEntry.getKey().equals(currentItemName) && prevEntry.getValue()[1] < startRow 
                                    && prevEntry.getValue()[1] > previousEndRow) {
                                    previousEndRow = prevEntry.getValue()[1];
                                }
                            }
                            
                            // 如果前面有分项，添加分页符，确保每个分项从新页开始
                            if (previousEndRow != -1) {
                                sheet.setRowBreak(previousEndRow);
                                log.info("在分项[{}]前添加分页符，位置：第{}行", currentItemName, previousEndRow + 1);
                            }
                        }
                        
                        // 设置表头在每页重复显示
                        // 确保表头行包含"测试点单项检测指标评分"行和表头行
                        int headerEndRow = 0;
                        for (Row row : sheet) {
                            Cell cell = row.getCell(0);
                            if (cell != null && cell.getCellType() == CellType.STRING && 
                                "分项设施名称".equals(cell.getStringCellValue())) {
                                headerEndRow = row.getRowNum();
                                break;
                            }
                        }
                        
                        // 设置表头重复行（从第一行到表头行）
                        if (headerEndRow > 0) {
                            sheet.setRepeatingRows(new CellRangeAddress(0, headerEndRow, 0, 8));
                            log.info("设置表头重复显示，行范围: 0-{}", headerEndRow);
                        } else {
                            // 如果未找到表头行，使用前两行作为表头
                            sheet.setRepeatingRows(new CellRangeAddress(0, Math.min(2, sheet.getLastRowNum()), 0, 8));
                            log.info("未找到明确的表头行，使用前两行作为重复表头");
                        }
                        
                        // 更新最大列索引
                        maxColumnIndex = Math.max(maxColumnIndex, resultColumnIndex);
                    }

                    // 添加行分页符，确保分项完整显示并避免表头重复问题
                    for (Map.Entry<String, int[]> entry : itemRowRanges.entrySet()) {
                        String currentItemName = entry.getKey();
                        int[] rowRange = entry.getValue();
                        int startRow = rowRange[0];
                        int endRow = rowRange[1];
                        
                        // 估算此分项的行数
                        int rowCount = endRow - startRow + 1;
                        
                        // 获取上一个处理过的分项的结束行
                        int previousEndRow = -1;
                        for (Map.Entry<String, int[]> prevEntry : itemRowRanges.entrySet()) {
                            if (!prevEntry.getKey().equals(currentItemName) && prevEntry.getValue()[1] < startRow 
                                && prevEntry.getValue()[1] > previousEndRow) {
                                previousEndRow = prevEntry.getValue()[1];
                            }
                        }
                        
                        // 如果前面有分项，且当前分项需要新页显示，添加分页符
                        if (previousEndRow != -1 && rowCount > 10) {
                            sheet.setRowBreak(previousEndRow);
                            log.info("在分项[{}]前添加分页符，位置：第{}行", currentItemName, previousEndRow + 1);
                        }
                    }
                    
                    // 在此处设置仅主标题行(第0行)重复显示在每页上
                    // 这样可以避免分项表头重复显示的问题
                    sheet.setRepeatingRows(new CellRangeAddress(0, 0, 0, 8)); // 固定使用8列，确保涵盖所有列
                    
                    // 移除此处的打印区域设置，改为在添加签名行后设置
                    // String printArea = "$A$1:$" + CellReference.convertNumToColString(maxColumnIndex) + "$" + (currentRowIndex);
                    // workbook.setPrintArea(workbook.getSheetIndex(sheet.getSheetName()), printArea);
                    
                    // 自动调整列宽，应用更合适的宽度比例满足A4纸张
                    // 增加总宽度使表格填满A4纸张
                    int totalWidth = 100 * 256; // 总宽度
                    int colsCount = 9; // 总列数（A-I列）
                    
                    // 优化列宽比例
                    // 分项设施名称(A)、主要检查内容(B)、权重(C)、设备列(D-H)、结果列(I)
                    // 增加主要检查内容列(B)的宽度比例
                    int[] colWidthRatio = {11, 20, 5, 20, 20, 20, 20, 20, 15}; // 更新列宽比例，增加B列和I列的比例
                    int totalRatio = Arrays.stream(colWidthRatio).sum();
                    
                    // 根据比例设置各列宽度
                    for (int i = 0; i < colWidthRatio.length && i <= maxColumnIndex; i++) {
                        int colWidth = (int)(totalWidth * colWidthRatio[i] / totalRatio);
                        // 对小于最小列宽的进行调整
                        colWidth = Math.max(colWidth, 6 * 256); // 增加最小列宽
                        sheet.setColumnWidth(i, colWidth);
                    }

                    // 设置"主要检查内容"列宽度
                    sheet.setColumnWidth(1, (int)(totalWidth * colWidthRatio[1] / totalRatio));

                    if (stopWatch.isRunning()) {
                        stopWatch.stop();
                    }
                    
                    stopWatch.start("执行单元格合并");
                    // 执行全表分项合并
                    for (Map.Entry<String, List<Integer>> entry : itemNameRowsMap.entrySet()) {
                        String mergeItemName = entry.getKey();
                        List<Integer> rowIndices = entry.getValue();

                        if (rowIndices.size() <= 1) {
                            continue; // 只有一行的情况不需要合并
                        }

                        // 排序行索引
                        Collections.sort(rowIndices);

                        // 查找连续的行段
                        int startRow = rowIndices.get(0);
                        int prevRow = startRow;

                        for (int i = 1; i < rowIndices.size(); i++) {
                            int currentRow = rowIndices.get(i);

                            // 如果不连续，则合并前面的段并重新开始
                            if (currentRow > prevRow + 1) {
                                // 合并之前的连续段
                                if (startRow < prevRow) {
                                    try {
                                        // 检查该区域是否已经合并过
                                        String regionKey = startRow + ":" + prevRow + ":0:0";
                                        if (!mergedRegions.contains(regionKey)) {
                                            sheet.addMergedRegion(new CellRangeAddress(startRow, prevRow, 0, 0));
                                            mergedRegions.add(regionKey);
                                            log.info("成功合并分项名称单元格: {} ({}:{})", mergeItemName, startRow, prevRow);
                                        }
                                    } catch (Exception e) {
                                        log.warn("合并分项名称单元格失败: {} ({}:{}) - {}", mergeItemName, startRow, prevRow, e.getMessage());
                                    }
                                }

                                // 重新开始新的段
                                startRow = currentRow;
                            }

                            prevRow = currentRow;
                        }

                        // 处理最后一段
                        if (startRow < prevRow) {
                            try {
                                // 检查该区域是否已经合并过
                                String regionKey = startRow + ":" + prevRow + ":0:0";
                                if (!mergedRegions.contains(regionKey)) {
                                    sheet.addMergedRegion(new CellRangeAddress(startRow, prevRow, 0, 0));
                                    mergedRegions.add(regionKey);
                                    log.info("成功合并分项名称单元格: {} ({}:{})", mergeItemName, startRow, prevRow);
                                }
                            } catch (Exception e) {
                                log.warn("合并分项名称单元格失败: {} ({}:{}) - {}", mergeItemName, startRow, prevRow, e.getMessage());
                            }
                        }
                    }

                    if (stopWatch.isRunning()) {
                        stopWatch.stop();
                    }
                    
                    stopWatch.start("调整行高和列宽");
                    // 调用优化的行高列宽调整方法
//                    optimizeRowHeightAndCellStyles(sheet, workbook, partItemHeaderRow, maxColumnIndex,
//                        wrapTextStyleCache, dataStyle, dataStyleWrapText, itemNameStyle, itemNameStyleWrapText,
//                        resultStyle, resultStyleWrapText);
                }

                if (stopWatch.isRunning()) {
                    stopWatch.stop();
                }
                
                stopWatch.start("添加签名行");
                // 添加签名行
                int signatureRowIdx = currentRowIndex; // 不空行，直接紧接着内容
                Row signatureRow = sheet.createRow(signatureRowIdx);

                // 设置签名行样式
                CellStyle signatureStyle = workbook.createCellStyle();
                signatureStyle.setAlignment(HorizontalAlignment.CENTER);
                signatureStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                signatureStyle.setBorderBottom(BorderStyle.THIN);
                signatureStyle.setBorderLeft(BorderStyle.THIN);
                signatureStyle.setBorderRight(BorderStyle.THIN);
                signatureStyle.setBorderTop(BorderStyle.THIN);
                // 减小单元格内边距
                signatureStyle.setIndention((short)0);
                
                // 为签名行设置稍大字体
                Font signatureFont = workbook.createFont();
                signatureFont.setFontHeightInPoints((short) 11);
                signatureFont.setBold(true);
                signatureStyle.setFont(signatureFont);

                // 创建空白样式（只有边框）
                CellStyle blankStyle = workbook.createCellStyle();
                blankStyle.cloneStyleFrom(signatureStyle);

                // 添加签名列：A列"检测"，C列"记录"，E列"复核"，G列"日期"，其他为空格
                String[] signatureLabels = {"检测", "记录", "复核", "日期"};
                int[] signatureCols = {0, 2, 4, 6}; // A, C, E, G列

                // 创建所有单元格并添加边框，但不填充斜杠
                for (int i = 0; i < 9; i++) { // A到I列
                    Cell cell = signatureRow.createCell(i);
                    cell.setCellStyle(blankStyle); // 默认用空白样式
                    // 不再设置默认值为"/"
                    cell.setCellValue(""); // 设置为空字符串
                }

                // 填充签名标签
                for (int i = 0; i < signatureLabels.length; i++) {
                    Cell signatureCell = signatureRow.getCell(signatureCols[i]);
                    signatureCell.setCellValue(signatureLabels[i]);
                    signatureCell.setCellStyle(signatureStyle);
                }

                // 合并H和I列
                if (maxColumnIndex >= 8) {
                    CellRangeAddress hiRegion = new CellRangeAddress(signatureRowIdx, signatureRowIdx, 7, 8);
                    sheet.addMergedRegion(hiRegion);
                    applyBorderToMergedRegion(sheet, hiRegion, workbook);
                }

                // 设置签名行高度
                signatureRow.setHeightInPoints(30); // 增加签名行高度，使之更醒目
                
                // 更新当前行索引，包含签名行
                currentRowIndex++;
                
                // 设置打印区域，确保签名行包含在打印区域内
                String printArea = "$A$1:$" + CellReference.convertNumToColString(maxColumnIndex) + "$" + currentRowIndex;
                workbook.setPrintArea(workbook.getSheetIndex(sheet.getSheetName()), printArea);
                
                if (stopWatch.isRunning()) {
                    stopWatch.stop();
                }
                
                stopWatch.start("应用字体样式");
                // 对内容单元格应用宋体五号字体
                for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                    Row row = sheet.getRow(i);
                    if (row != null) {
                        // 跳过表头行（前3行）
                        if (row.getRowNum() < 3) {
                            continue;
                        }
                        
                        // 为每个内容单元格设置宋体五号字体
                        for (int j = 0; j <= row.getLastCellNum(); j++) {
                            Cell cell = row.getCell(j);
                            if (cell != null) {
                                CellStyle style = cell.getCellStyle();
                                
                                // 创建新样式并复制原有属性
                                CellStyle newStyle = workbook.createCellStyle();
                                newStyle.cloneStyleFrom(style);
                                
                                // 创建宋体五号字体
                                Font songtiFont = workbook.createFont();
                                songtiFont.setFontName("宋体");
                                songtiFont.setFontHeightInPoints((short) 10.5); // 五号字体(10.5磅)
                                
                                // 应用字体
                                newStyle.setFont(songtiFont);
                                
                                // 应用新样式
                                cell.setCellStyle(newStyle);
                            }
                        }
                    }
                }
                
                if (stopWatch.isRunning()) {
                    stopWatch.stop();
                }
                
                stopWatch.start("添加表格外框");
                // 为表格添加外框（双横线）
                int lastRowNum = sheet.getLastRowNum();
                if (lastRowNum > 2) { // 确保有实际内容行
                    addTableBorder(sheet, workbook, 2, lastRowNum, 0, 8);
                }
                
                // 写入响应
            }

            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            
            stopWatch.start("写入响应");
            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }

            log.info("检查报告导出完成");
            log.info("导出性能统计:\n{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("导出检查报告异常", e);
            throw new ServiceException("导出检查报告失败: " + e.getMessage());
        }
    }

    // 在DeviceInfo类之前添加一个工具方法用于格式化设备编号
    /**
     * 格式化设备编号为#格式
     * 将"001"、"002"这种转为"1#"、"2#"
     */
    private String formatDeviceCode(String code) {
        if (code == null || code.isEmpty()) {
            return code;
        }
        
        // 如果已经是#格式，直接返回
        if (code.contains("#")) {
            return code;
        }
        
        // 提取数字部分
        String numericPart = code.replaceAll("[^0-9]", "");
        try {
            // 移除前导零并添加#
            int number = Integer.parseInt(numericPart);
            return number + "#";
        } catch (NumberFormatException e) {
            // 格式化失败，返回原始编码
            return code;
        }
    }

    /**
     * 优化设备表头显示，特别是对"上行洞室"或"下行洞室"的位置
     * @param device 设备信息
     * @return 优化后的表头显示名称
     */
    private String getOptimizedDisplayName(DeviceInfo device) {
        String location = device.getLocationName();
        String deviceCode = device.getDeviceCode();
        String displayName = device.getDisplayName();
        String locationCode = device.getLocationCode();
        
        // 格式化设备编号
        String formattedCode = formatDeviceCode(deviceCode);
        
        // 检查是否为"上行洞室"或"下行洞室"
        if (location != null && (location.contains("上行洞室") || location.contains("下行洞室"))) {
            // 使用locationCode（如果有）
            if (locationCode != null && !locationCode.isEmpty()) {
                // 格式化locationCode - 只取最后两位数字
                String formattedLocationCode = "";
                String numericPart = locationCode.replaceAll("[^0-9]", "");
                
                try {
                    // 根据数字长度选择如何处理
                    if (numericPart.length() > 2) {
                        // 对于三位数及以上，只取最后两位
                        String lastTwoDigits = numericPart.substring(numericPart.length() - 2);
                        // 移除前导零并将结果作为位置编号
                        int number = Integer.parseInt(lastTwoDigits);
                        formattedLocationCode = String.valueOf(number);
        } else {
                        // 对于两位数及以下，直接移除前导零
                        int number = Integer.parseInt(numericPart);
                        formattedLocationCode = String.valueOf(number);
                    }
                } catch (NumberFormatException e) {
                    log.warn("格式化位置编码异常: {}", numericPart, e);
                    formattedLocationCode = numericPart; // 保持原样
                }
                
                // 提取设备名称部分（进线柜、出线柜等）
                String deviceName = "";
                if (displayName != null) {
                    // 找到设备编号之后的部分，即设备名称
                    int idx = displayName.indexOf(deviceCode);
                    if (idx >= 0 && idx + deviceCode.length() < displayName.length()) {
                        deviceName = displayName.substring(idx + deviceCode.length()).trim();
                    }
                }
                
                // 组合新的显示名称
                String prefix = location.contains("上行洞室") ? "上行" : "下行";
                return prefix + formattedLocationCode + "号洞室" + formattedCode + deviceName;
            }
        }else if(location.contains("轴流风机房")){
            // 提取设备名称部分（进线柜、出线柜等）
            String deviceName = "";
            if (displayName != null) {
                // 找到设备编号之后的部分，即设备名称
                int idx = displayName.indexOf(deviceCode);
                if (idx >= 0 && idx + deviceCode.length() < displayName.length()) {
                    deviceName = displayName.substring(idx + deviceCode.length()).trim();
                }
            }
            return Integer.valueOf(locationCode.substring(locationCode.length()-1,locationCode.length())) + "号轴流风机房"+ formattedCode + deviceName;
        }
        
        // 如果不是特殊位置或没有获取到位置编号，则返回原始的格式化显示名
        return displayName.replace(deviceCode, formattedCode);
    }

    /**
     * 为合并区域的单元格添加边框
     * @param sheet 工作表
     * @param region 合并区域
     * @param workbook 工作簿
     */
    private void applyBorderToMergedRegion(Sheet sheet, CellRangeAddress region, Workbook workbook) {
        BorderStyle borderStyle = BorderStyle.THIN;
        // 创建边框样式
        RegionUtil.setBorderBottom(borderStyle, region, sheet);
        RegionUtil.setBorderLeft(borderStyle, region, sheet);
        RegionUtil.setBorderRight(borderStyle, region, sheet);
        RegionUtil.setBorderTop(borderStyle, region, sheet);
        
        // 为合并区域内的每个单元格应用边框样式
        for (int rowNum = region.getFirstRow(); rowNum <= region.getLastRow(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                row = sheet.createRow(rowNum);
            }
            
            for (int colNum = region.getFirstColumn(); colNum <= region.getLastColumn(); colNum++) {
                Cell cell = row.getCell(colNum);
                if (cell == null) {
                    cell = row.createCell(colNum);
                }
                
                CellStyle cellStyle = cell.getCellStyle();
                if (cellStyle == null) {
                    cellStyle = workbook.createCellStyle();
                }
                
                cellStyle.setBorderBottom(borderStyle);
                cellStyle.setBorderLeft(borderStyle);
                cellStyle.setBorderRight(borderStyle);
                cellStyle.setBorderTop(borderStyle);
                
                cell.setCellStyle(cellStyle);
            }
        }
    }

    /**
     * 格式化文本，在适当位置添加换行符
     * @param text 原始文本
     * @param maxLineLength 每行最大字符数
     * @return 格式化后的文本
     */
    private String formatTextWithLineBreaks(String text, int maxLineLength) {
        if (text == null || text.isEmpty() || text.length() <= maxLineLength) {
            return text;
        }
        
        // 如果文本已经包含换行符，不做处理
        if (text.contains("\n")) {
            return text;
        }
        
        // 更智能的中文文本换行处理
        StringBuilder result = new StringBuilder();
        int length = text.length();
        int startPos = 0;
        int idealLineLength = maxLineLength; // 理想行长度
        
        while (startPos < length) {
            // 计算可能的结束位置
            int endPos = Math.min(startPos + idealLineLength, length);
            
            // 如果不是最后的文本段，尝试在更合适的位置断行
            if (endPos < length) {
                // 优先在标点符号处断行
                int breakPos = findBreakPosition(text, startPos, endPos);
                
                // 如果找到合适的断行位置，使用它
                if (breakPos > startPos && breakPos < endPos) {
                    endPos = breakPos;
                } 
                // 对于中文，也可以在任意字符处断行，不需要额外处理
            }
            
            // 添加当前行文本
            result.append(text.substring(startPos, endPos));
            startPos = endPos;
            
            // 如果还有更多文本，添加换行
            if (startPos < length) {
                result.append("\n");
            }
        }
        
        return result.toString();
    }
    
    /**
     * 查找合适的断行位置
     * @param text 文本
     * @param start 起始位置
     * @param end 结束位置
     * @return 适合断行的位置
     */
    private int findBreakPosition(String text, int start, int end) {
        // 优先在这些标点后断行，扩展中文标点符号
        char[] breakChars = {',', '.', '。', ':', ';', '!', '?', ' ', '，', '、'};
        
        // 从后往前搜索，优先在靠近end的位置断行
        for (int i = end - 1; i > start; i--) {
            char c = text.charAt(i);
            
            // 标点符号后断行
            for (char breakChar : breakChars) {
                if (c == breakChar) {
                    return i + 1; // 在标点后断行
                }
            }
            
            // 句末标点符号前断行
            if (i < end - 1) {
                char nextChar = text.charAt(i + 1);
                // 检查是否是中文句末标点
                if (nextChar == '。' || nextChar == '！' || nextChar == '？' || 
                    nextChar == '：' || nextChar == '；' || nextChar == '…') {
                    return i + 1; // 在句末标点前断行
                }
            }
        }
        
        // 如果没找到合适的断行标点，尝试按照语义断行
        int middle = (start + end) / 2;
        int candidate = middle;
        // 在中间位置附近寻找合适的断句点
        for (int i = middle; i > start + (end - start) / 3; i--) {
            if (i < text.length() - 1) {
                char c = text.charAt(i);
                // 尽量在词语间断开，中文通常没有明显的分隔符
                if (c == ' ' || c == '　') { // 空格和全角空格
                    candidate = i + 1;
                    break;
                }
            }
        }
        
        // 如果找到了合适的位置，返回它；否则返回end
        return candidate != middle ? candidate : end;
    }

    /**
     * 优化的行高和单元格样式调整方法
     * 通过批量处理和样式缓存提高性能
     * 
     * @param sheet 工作表
     * @param workbook 工作簿
     * @param partItemHeaderRow 表头行（用于跳过）
     * @param maxColumnIndex 最大列索引
     * @param wrapTextStyleCache 换行样式缓存
     * @param dataStyle 数据样式
     * @param dataStyleWrapText 数据换行样式
     * @param itemNameStyle 项目名称样式
     * @param itemNameStyleWrapText 项目名称换行样式
     * @param resultStyle 结果样式
     * @param resultStyleWrapText 结果换行样式
     */
    private void optimizeRowHeightAndCellStyles(Sheet sheet, Workbook workbook, Row partItemHeaderRow, 
            int maxColumnIndex, Map<CellStyle, CellStyle> wrapTextStyleCache,
            CellStyle dataStyle, CellStyle dataStyleWrapText,
            CellStyle itemNameStyle, CellStyle itemNameStyleWrapText,
            CellStyle resultStyle, CellStyle resultStyleWrapText) {
        
        // 预先收集需要处理的行，避免在循环中重复计算
        List<Row> rowsToProcess = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        
        for (int i = 0; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row != null && !row.equals(partItemHeaderRow) && row.getRowNum() != 0) {
                rowsToProcess.add(row);
            }
        }
        
        // 批量处理行，减少方法调用开销
        for (Row row : rowsToProcess) {
            // 批量处理单元格样式设置
            processCellStyles(row, maxColumnIndex, wrapTextStyleCache, workbook,
                dataStyle, dataStyleWrapText, itemNameStyle, itemNameStyleWrapText,
                resultStyle, resultStyleWrapText);
            
            // 优化的行高设置
            optimizeRowHeight(row);
        }
    }
    
    /**
     * 批量处理单元格样式设置
     */
    private void processCellStyles(Row row, int maxColumnIndex, Map<CellStyle, CellStyle> wrapTextStyleCache,
            Workbook workbook, CellStyle dataStyle, CellStyle dataStyleWrapText,
            CellStyle itemNameStyle, CellStyle itemNameStyleWrapText,
            CellStyle resultStyle, CellStyle resultStyleWrapText) {
        
        // 预先获取行的所有单元格，避免重复调用
        List<Cell> cellsToProcess = new ArrayList<>();
        for (int j = 0; j <= maxColumnIndex; j++) {
            Cell cell = row.getCell(j);
            if (cell != null) {
                cellsToProcess.add(cell);
            }
        }
        
        // 批量处理单元格样式
        for (Cell cell : cellsToProcess) {
            int columnIndex = cell.getColumnIndex();
            CellStyle originalStyle = cell.getCellStyle();
            
            // 特别处理I列（索引为8）
            if (columnIndex == 8) {
                CellStyle cachedStyle = wrapTextStyleCache.get(originalStyle);
                if (cachedStyle == null) {
                    cachedStyle = workbook.createCellStyle();
                    cachedStyle.cloneStyleFrom(originalStyle);
                    cachedStyle.setWrapText(true);
                    cachedStyle.setFillPattern(FillPatternType.NO_FILL);
                    wrapTextStyleCache.put(originalStyle, cachedStyle);
                }
                cell.setCellStyle(cachedStyle);
                continue;
            }
            
            // 使用预定义样式映射，避免重复的equals比较
            CellStyle newStyle = null;
            if (originalStyle == dataStyle || originalStyle.equals(dataStyle)) {
                newStyle = dataStyleWrapText;
            } else if (originalStyle == itemNameStyle || originalStyle.equals(itemNameStyle)) {
                newStyle = itemNameStyleWrapText;
            } else if (originalStyle == resultStyle || originalStyle.equals(resultStyle)) {
                newStyle = resultStyleWrapText;
            } else {
                // 使用缓存处理其他样式
                newStyle = wrapTextStyleCache.get(originalStyle);
                if (newStyle == null) {
                    newStyle = workbook.createCellStyle();
                    newStyle.cloneStyleFrom(originalStyle);
                    newStyle.setWrapText(true);
                    newStyle.setFillPattern(FillPatternType.NO_FILL);
                    wrapTextStyleCache.put(originalStyle, newStyle);
                }
            }
            
            if (newStyle != null) {
                cell.setCellStyle(newStyle);
            }
        }
    }
    
    /**
     * 优化的行高设置
     */
    private void optimizeRowHeight(Row row) {
        short currentHeight = row.getHeight();
        
        // 如果行高为0或负值，设置为自适应
        if (currentHeight <= 0) {
            row.setHeight((short)-1);
            return;
        }
        
        // 如果已经设置为自适应，跳过
        if (currentHeight == (short)-1) {
            return;
        }
        
        // 检查B列（主要检查内容列）的内容
        Cell contentCell = row.getCell(1);
        if (contentCell != null) {
            // 有内容的行设置为自适应高度
            row.setHeight((short)-1);
        } else {
            // 检查是否有长内容（优化版本，避免重复字符串操作）
            boolean hasLongContent = checkForLongContent(contentCell);
            
            if (hasLongContent) {
                row.setHeight((short)(40 * 20)); // 40点高度
            } else {
                row.setHeight((short)(30 * 20)); // 30点高度
            }
        }
    }
    
    /**
     * 检查是否包含长内容（优化版本）
     */
    private boolean checkForLongContent(Cell contentCell) {
        if (contentCell == null || contentCell.getCellType() != CellType.STRING) {
            return false;
        }
        
        String cellContent = contentCell.getStringCellValue();
        if (cellContent == null) {
            return false;
        }
        
        // 快速检查：长度大于10或包含换行符
        return cellContent.length() > 10 || cellContent.indexOf('\n') != -1;
    }

    /**
     * 高效地为表格添加外框（双横线）
     * 只处理边界单元格，避免遍历整个表格区域
     * 
     * @param sheet 工作表
     * @param workbook 工作簿
     * @param startRow 起始行
     * @param endRow 结束行
     * @param startCol 起始列
     * @param endCol 结束列
     */
    private void addTableBorder(Sheet sheet, Workbook workbook, int startRow, int endRow, int startCol, int endCol) {
        // 使用样式缓存来避免重复创建相同的样式
        Map<String, CellStyle> borderStyleCache = new HashMap<>();
        
        // 高效边框设置函数
        java.util.function.BiConsumer<Cell, String> setBorderStyle = (cell, styleKey) -> {
            if (cell == null) return;
            
            CellStyle originalStyle = cell.getCellStyle();
            String cacheKey = originalStyle.getIndex() + "_" + styleKey;
            
            CellStyle newStyle = borderStyleCache.get(cacheKey);
            if (newStyle == null) {
                newStyle = workbook.createCellStyle();
                newStyle.cloneStyleFrom(originalStyle);
                
                // 根据样式键设置边框
                switch (styleKey) {
                    case "top":
                        newStyle.setBorderTop(BorderStyle.DOUBLE);
                        break;
                    case "bottom":
                        newStyle.setBorderBottom(BorderStyle.DOUBLE);
                        break;
                    case "left":
                        newStyle.setBorderLeft(BorderStyle.DOUBLE);
                        break;
                    case "right":
                        newStyle.setBorderRight(BorderStyle.DOUBLE);
                        break;
                    case "topLeft":
                        newStyle.setBorderTop(BorderStyle.DOUBLE);
                        newStyle.setBorderLeft(BorderStyle.DOUBLE);
                        break;
                    case "topRight":
                        newStyle.setBorderTop(BorderStyle.DOUBLE);
                        newStyle.setBorderRight(BorderStyle.DOUBLE);
                        break;
                    case "bottomLeft":
                        newStyle.setBorderBottom(BorderStyle.DOUBLE);
                        newStyle.setBorderLeft(BorderStyle.DOUBLE);
                        break;
                    case "bottomRight":
                        newStyle.setBorderBottom(BorderStyle.DOUBLE);
                        newStyle.setBorderRight(BorderStyle.DOUBLE);
                        break;
                }
                
                borderStyleCache.put(cacheKey, newStyle);
            }
            
            cell.setCellStyle(newStyle);
        };

        // 设置顶部边框 - 只处理第一行
        Row topRow = sheet.getRow(startRow);
        if (topRow != null) {
            for (int col = startCol; col <= endCol; col++) {
                Cell cell = topRow.getCell(col);
                if (col == startCol) {
                    setBorderStyle.accept(cell, "topLeft");
                } else if (col == endCol) {
                    setBorderStyle.accept(cell, "topRight");
                } else {
                    setBorderStyle.accept(cell, "top");
                }
            }
        }

        // 设置底部边框 - 只处理最后一行
        Row bottomRow = sheet.getRow(endRow);
        if (bottomRow != null) {
            for (int col = startCol; col <= endCol; col++) {
                Cell cell = bottomRow.getCell(col);
                if (col == startCol) {
                    setBorderStyle.accept(cell, "bottomLeft");
                } else if (col == endCol) {
                    setBorderStyle.accept(cell, "bottomRight");
                } else {
                    setBorderStyle.accept(cell, "bottom");
                }
            }
        }

        // 设置左右边框 - 只处理左右两列的中间行
        for (int row = startRow + 1; row < endRow; row++) {
            Row currentRow = sheet.getRow(row);
            if (currentRow != null) {
                // 左边框
                Cell leftCell = currentRow.getCell(startCol);
                setBorderStyle.accept(leftCell, "left");
                
                // 右边框
                Cell rightCell = currentRow.getCell(endCol);
                setBorderStyle.accept(rightCell, "right");
            }
        }
    }

}
