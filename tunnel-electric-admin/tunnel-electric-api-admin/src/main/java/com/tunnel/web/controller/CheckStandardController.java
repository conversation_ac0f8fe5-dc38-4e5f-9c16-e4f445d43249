package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.CheckStandard;
import com.tunnel.service.CheckStandardService;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.core.page.TableDataInfo;

/**
 * 规范列表Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/electric/checkStandard")
public class CheckStandardController extends BaseController {
    @Autowired
    private CheckStandardService checkStandardService;

    /**
     * 查询规范列表列表
     */
    //    @PreAuthorize("@ss.hasPermi('electric:checkStandard:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckStandard checkStandard) {
        startPage();
        List<CheckStandard> list = checkStandardService.selectCheckStandardList(checkStandard);
        return getDataTable(list);
    }

    /**
     * 导出规范列表列表
     */
    //    @PreAuthorize("@ss.hasPermi('electric:checkStandard:export')")
    @Log(title = "规范列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckStandard checkStandard) {
        List<CheckStandard> list = checkStandardService.selectCheckStandardList(checkStandard);
        ExcelUtil<CheckStandard> util = new ExcelUtil<CheckStandard>(CheckStandard.class);
        util.exportExcel(response, list, "规范列表数据");
    }

    /**
     * 获取规范列表详细信息
     */
    //    @PreAuthorize("@ss.hasPermi('electric:checkStandard:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkStandardService.selectCheckStandardById(id));
    }

    /**
     * 新增规范列表
     */
    //    @PreAuthorize("@ss.hasPermi('electric:checkStandard:add')")
    @Log(title = "规范列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckStandard checkStandard) {
        return toAjax(checkStandardService.insertCheckStandard(checkStandard));
    }

    /**
     * 修改规范列表
     */
    //    @PreAuthorize("@ss.hasPermi('electric:checkStandard:edit')")
    @Log(title = "规范列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckStandard checkStandard) {
        return toAjax(checkStandardService.updateCheckStandard(checkStandard));
    }

    /**
     * 删除规范列表
     */
    //    @PreAuthorize("@ss.hasPermi('electric:checkStandard:remove')")
    @Log(title = "规范列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkStandardService.deleteCheckStandardByIds(ids));
    }
} 