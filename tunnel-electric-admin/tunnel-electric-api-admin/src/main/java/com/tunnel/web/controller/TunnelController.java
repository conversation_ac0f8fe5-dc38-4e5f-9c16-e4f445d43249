package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Tunnel;
import com.tunnel.service.TunnelService;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 隧道总Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/tunnel/electric/tunnel")
public class TunnelController extends BaseController {
    @Autowired
    private TunnelService tunnelService;


    @ApiOperation(value = "批量导入新增")
    @RequestMapping(value = "/batchImport", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImport(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(tunnelService.batchImport(file));
    }

    /**
     * 查询隧道总列表
     */
    @PreAuthorize("@ss.hasPermi('system:tunnel:list')")
    @GetMapping("/list")
    public TableDataInfo list(Tunnel tunnel) {
        startPage();
        List<Tunnel> list = tunnelService.selectTunnelList(tunnel);
        return getDataTable(list);
    }

    /**
     * 导出隧道总列表
     */
    @PreAuthorize("@ss.hasPermi('system:tunnel:export')")
    @Log(title = "隧道总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Tunnel tunnel) {
        List<Tunnel> list = tunnelService.selectTunnelList(tunnel);
        ExcelUtil<Tunnel> util = new ExcelUtil<Tunnel>(Tunnel.class);
        util.exportExcel(response, list, "隧道总数据");
    }

    /**
     * 获取隧道总详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tunnel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tunnelService.selectTunnelById(id));
    }

    /**
     * 新增隧道总
     */
    @PreAuthorize("@ss.hasPermi('system:tunnel:add')")
    @Log(title = "隧道总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Tunnel tunnel) {
        return toAjax(tunnelService.insertTunnel(tunnel));
    }

    /**
     * 修改隧道总
     */
    @PreAuthorize("@ss.hasPermi('system:tunnel:edit')")
    @Log(title = "隧道总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Tunnel tunnel) {
        return toAjax(tunnelService.updateTunnel(tunnel));
    }

    /**
     * 删除隧道总
     */
    @PreAuthorize("@ss.hasPermi('system:tunnel:remove')")
    @Log(title = "隧道总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tunnelService.deleteTunnelByIds(ids));
    }

    /**
     * 查询隧道总列表
     */
    @PostMapping("/selectAllTotalTunnel")
    public AjaxResult selectAllTotalTunnel() {
        List<Tunnel> list = tunnelService.selectAllTotalTunnel();
        return AjaxResult.success(list);
    }
}
