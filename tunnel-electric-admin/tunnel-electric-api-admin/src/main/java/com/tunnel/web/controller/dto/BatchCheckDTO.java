package com.tunnel.web.controller.dto;

import com.tunnel.domain.TunnelCheck;
import lombok.Data;

import java.util.List;

@Data
public class BatchCheckDTO {
    /**
     * 缺陷照片1
     */
//    @Excel(name = "缺陷照片1")
    private String picUrl1;

    /**
     * 缺陷照片2
     */
//    @Excel(name = "缺陷照片2")
    private String picUrl2;

    /**
     * 缺陷照片3
     */
//    @Excel(name = "缺陷照片3")
    private String picUrl3;

    private List<TunnelCheck> checkList;
}
