package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckEnum;
import com.tunnel.service.CheckEnumService;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 检测项目Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/tunnel/electric/enum")
public class CheckEnumController extends BaseController {
    @Autowired
    private CheckEnumService checkEnumService;

    /**
     * 查询检测项目列表
     */
    @PreAuthorize("@ss.hasPermi('system:enum:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckEnum checkEnum) {
        startPage();
        List<CheckEnum> list = checkEnumService.selectCheckEnumList(checkEnum);
        return getDataTable(list);
    }

    /**
     * 导出检测项目列表
     */
    @PreAuthorize("@ss.hasPermi('system:enum:export')")
    @Log(title = "检测项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumService.selectCheckEnumList(checkEnum);
        ExcelUtil<CheckEnum> util = new ExcelUtil<CheckEnum>(CheckEnum.class);
        util.exportExcel(response, list, "检测项目数据");
    }

    /**
     * 获取检测项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:enum:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkEnumService.selectCheckEnumById(id));
    }

    /**
     * 新增检测项目
     */
    @PreAuthorize("@ss.hasPermi('system:enum:add')")
    @Log(title = "检测项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckEnum checkEnum) {
        return toAjax(checkEnumService.insertCheckEnum(checkEnum));
    }

    /**
     * 修改检测项目
     */
    @PreAuthorize("@ss.hasPermi('system:enum:edit')")
    @Log(title = "检测项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckEnum checkEnum) {
        return toAjax(checkEnumService.updateCheckEnum(checkEnum));
    }

    /**
     * 删除检测项目
     */
    @PreAuthorize("@ss.hasPermi('system:enum:remove')")
    @Log(title = "检测项目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkEnumService.deleteCheckEnumByIds(ids));
    }


    @ApiOperation(value = "批量导入新增")
    @RequestMapping(value = "/batchImport", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImport(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(checkEnumService.batchImport(file));
    }
}
