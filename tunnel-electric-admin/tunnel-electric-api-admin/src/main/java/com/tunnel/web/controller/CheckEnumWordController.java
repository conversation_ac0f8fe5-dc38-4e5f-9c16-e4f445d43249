package com.tunnel.web.controller;

import com.google.common.collect.Lists;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.word.WordDocumentUtil;
import com.tunnel.domain.CheckEnumRelation;
import com.tunnel.domain.FacilityInfo;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.mapper.CheckEnumMapper;
import com.tunnel.mapper.FacilityInfoMapper;
import com.tunnel.mapper.TunnelCheckMapper;
import com.tunnel.mapper.TunnelInfoMapper;
import com.tunnel.service.CheckEnumRelationService;
import com.tunnel.service.TunnelCheckService;
import com.tunnel.web.controller.dto.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;
import org.docx4j.wml.STBrType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBElement;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检测项目Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/tunnel/electric/word")
@Slf4j
public class CheckEnumWordController extends BaseController {
    @Autowired
    private CheckEnumRelationService checkEnumRelationService;
    @Resource
    private TunnelCheckMapper tunnelCheckMapper;
    @Resource
    private CheckEnumMapper checkEnumMapper;
    @Resource
    private FacilityInfoMapper facilityInfoMapper;
    @Resource
    private TunnelInfoMapper tunnelInfoMapper;
    @Autowired
    private TunnelCheckService tunnelCheckService;

    /**
     * 导出设施检查Word版
     */
    @Log(title = "分项设备检查设备对应关系Word版", businessType = BusinessType.EXPORT)
    @PostMapping("/exportFacilityDetailWord")
    public void exportFacilityDetailWord(HttpServletResponse response, CheckEnumRelation checkEnumRelation) {
        try {

            // 创建Word文档
            // 1. 读取模板文件
            InputStream is = this.getClass().getResourceAsStream("/public/word/check_word_template.docx");
            if (is == null) {
                log.error("找不到报告模板文件");
                throw new ServiceException("找不到报告模板文件");
            }

            // 2. 加载Word文档
            WordprocessingMLPackage document = WordprocessingMLPackage.load(is);
            //隧道ID不能为空
            if (Objects.isNull(checkEnumRelation.getTunnelId())) {
                throw new ServiceException("隧道ID不能为空");
            }

            TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(checkEnumRelation.getTunnelId());
            if (Objects.isNull(tunnelInfo)) {
                throw new ServiceException("隧道不存在");
            }
            // 获取当前年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            String pageTitle = currentYear + "年" + tunnelInfo.getRoadName() + tunnelInfo.getTunnelName() + "机电设施定期检查报告";
            initCheckDetailWord(checkEnumRelation,document,tunnelInfo,true);
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(pageTitle, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");

            // 输出文档
            document.save(response.getOutputStream());

        } catch (Exception e) {
            log.error("导出Word格式报告失败", e);
            throw new ServiceException("导出Word格式报告失败: " + e.getMessage());
        }
    }

    public void initCheckDetailWord(CheckEnumRelation checkEnumRelation,WordprocessingMLPackage document,TunnelInfo tunnelInfo,boolean needTitle){
        List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(checkEnumRelation);
//        checkEnumRelationList=checkEnumRelationList.stream().filter(v->Objects.equals("隧道灯具",v.getItemName())).collect(Collectors.toList());
        for (CheckEnumRelation enumRelation : checkEnumRelationList) {
            if (StringUtils.isBlank(enumRelation.getName())) {
                enumRelation.setName("");
            }
        }

        //查询当前隧道下的所有资产
        List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(checkEnumRelation.getTunnelId());
        for (FacilityInfo facilityInfo : facilityInfoList) {
            if (StringUtils.isBlank(facilityInfo.getName())) {
                facilityInfo.setName("");
            }
        }

        // 根据partCode+itemCode进行分组
        Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream()
                .collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));

        // 创建一个映射，用于存储设备信息
        Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();

        // 创建分值查找映射表
        Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>();        // 按编号查找
        Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>();     // 默认分值（不带name的记录）

        // 预处理checkEnumRelationList
        Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream()
                .collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));

        Map<String, BigDecimal> damageRatioMap = new HashMap<>();
        Map<String, List<String>> partItemRemarkMapTemp = new HashMap<>();

        checkEnumRelationService.initCommonScore(checkEnumRelationList, checkEnumRelation, relationGroupMap, facilityGroupMap,
                deviceInfoMap, codeScoreMap, defaultScoreMap, damageRatioMap, partItemRemarkMapTemp, Lists.newArrayList());

        Map<String, String> partItemRemarkMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : partItemRemarkMapTemp.entrySet()) {
            partItemRemarkMap.put(entry.getKey(), String.join(",", entry.getValue()));
        }

        // 按照分部进行分组
        Map<String, List<CheckEnumRelation>> partGroupedData = checkEnumRelationList.stream()
                .collect(Collectors.groupingBy(item -> {
                    String partCode = item.getPartCode();
                    return partCode != null ? partCode : "未分类";
                }));

        // 获取当前年份
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        // 创建标题文本
        String pageTitle = currentYear + "年" + tunnelInfo.getRoadName() + tunnelInfo.getTunnelName() + "机电设施定期检查报告";
        // 4. 替换页眉中的变量
        WordDocumentUtil.replaceHeaderPlaceholder(document, "pageTitle", pageTitle);
        // 第一页不需要添加标题，因为每个分部会添加自己的标题
        if(needTitle){
            // 不再添加通用的附表1标题，改为在每个分部开头添加对应的附表标题
        }
        // 按分部代码排序处理
        List<String> sortedPartCodes = new ArrayList<>(partGroupedData.keySet());
        Collections.sort(sortedPartCodes);

        // 创建分部序号映射
        Map<String, Integer> partSequenceMap = new HashMap<>();
        int sequenceCounter = 1;
        for (String partCode : sortedPartCodes) {
            partSequenceMap.put(partCode, sequenceCounter++);
        }

        // 为每个分部创建内容
        for (String partCode : sortedPartCodes) {
            List<CheckEnumRelation> partData = partGroupedData.get(partCode);
            if (partData.isEmpty()) continue;

            String partName = partData.get(0).getPartName();
            if (partName == null || partName.isEmpty()) {
                partName = "分部_" + partCode;
            }
            
            // 获取当前分部的序号
            int partSequence = partSequenceMap.get(partCode);
            
            // 从第二个分部开始，在每个分部标题前添加分页符
            if (partSequence > 1) {
                addPageBreak(document);
                log.info("✓ 为分部 [{}] 添加分页符", partSequence);
            }
            
            // 添加分部对应的附表标题（黑体15.5字号）- 每个分部只添加一次
            String partTitle = "附表 " + partSequence + "、" + partName + "检查结果";
            addCustomTableTitle(document, partTitle, partSequence == 1);
            log.info("✓ 添加分部标题 [{}]: {}", partSequence, partTitle);

            // 按照分项代码进行分组
            Map<String, List<CheckEnumRelation>> itemGroupedData = partData.stream()
                    .collect(Collectors.groupingBy(item -> {
                        String itemCode = item.getItemCode();
                        return itemCode != null ? itemCode : "未分类";
                    }));

            // 按分项代码排序处理
            List<String> sortedItemCodes = new ArrayList<>(itemGroupedData.keySet());
            Collections.sort(sortedItemCodes);

            for (String itemCode : sortedItemCodes) {
                List<CheckEnumRelation> itemData = itemGroupedData.get(itemCode);
                if (itemData.isEmpty()) continue;

                String itemName = itemData.get(0).getItemName();
                log.info("处理分部[{}:{}]分项[{}:{}]", partCode, partName, itemCode, itemName);

                // 获取该分项下所有设备位置和编号的组合
                Map<String, Set<String>> locationDevicesMap = new HashMap<>();
                Map<String, String> locationNameMap = new HashMap<>();
                Map<String, String> deviceNameMap = new HashMap<>();

                // 首先收集所有位置和对应的设备编号
                for (CheckEnumRelation relation : itemData) {
                    if (relation.getCode() != null && relation.getLocationName() != null) {
                        String locationCode = relation.getLocation() != null ? relation.getLocation() : "";
                        String deviceCode = relation.getCode();
                        String deviceKey = locationCode + "_" + deviceCode;

                        locationNameMap.put(locationCode, relation.getLocationName());
                        locationDevicesMap.computeIfAbsent(locationCode, k -> new HashSet<>()).add(deviceCode);

                        // 保存设备名称
                        if (relation.getName() != null) {
                            deviceNameMap.put(deviceKey, relation.getName());
                        }
                    }
                }

                // 按位置排序
                List<String> sortedLocations = new ArrayList<>(locationDevicesMap.keySet());
                Collections.sort(sortedLocations);

                // 收集所有设备键（位置+编号）
                List<DeviceInfo> allDevices = new ArrayList<>();
                for (String locationCode : sortedLocations) {
                    Set<String> deviceCodes = locationDevicesMap.get(locationCode);
                    List<String> sortedDeviceCodes = new ArrayList<>(deviceCodes);

                    // 对设备编号排序
                    Collections.sort(sortedDeviceCodes, (a, b) -> {
                        try {
                            int numA = Integer.parseInt(a.replaceAll("[^0-9]", ""));
                            int numB = Integer.parseInt(b.replaceAll("[^0-9]", ""));
                            return Integer.compare(numA, numB);
                        } catch (NumberFormatException e) {
                            return a.compareTo(b);
                        }
                    });

                    for (String deviceCode : sortedDeviceCodes) {
                        String deviceKey = locationCode + "_" + deviceCode;
                        String deviceName = deviceNameMap.getOrDefault(deviceKey, "");

                        // 创建设备信息对象存储位置、编号和显示文本
                        DeviceInfo deviceInfo = new DeviceInfo();
                        deviceInfo.setLocationCode(locationCode);
                        deviceInfo.setDeviceCode(deviceCode);
                        deviceInfo.setLocationName(locationNameMap.getOrDefault(locationCode, ""));
                        deviceInfo.setDisplayName(deviceInfo.getLocationName() + " " + deviceCode + (StringUtils.isBlank(deviceName) ? "" : " " + deviceName));
                        allDevices.add(deviceInfo);
                    }
                }

                // 按照检查内容分组
                Map<String, List<CheckEnumRelation>> contentGroupedData = itemData.stream()
                        .collect(Collectors.groupingBy(item -> item.getCheckContent() != null ? item.getCheckContent() : "未分类"));

                // 保留原始顺序的检查内容列表，而不是字母排序
                List<String> contentOrder = new ArrayList<>();
                // 使用LinkedHashSet去重，保持原始顺序
                Set<String> processedContents = new LinkedHashSet<>();

                // 从原始列表中提取检查内容，保持原始顺序
                for (CheckEnumRelation relation : itemData) {
                    String checkContent = relation.getCheckContent() != null ? relation.getCheckContent() : "未分类";
                    if (processedContents.add(checkContent)) { // 如果未处理过，则添加到列表
                        contentOrder.add(checkContent);
                    }
                }

                // 每个模块最多显示多少个设备列
                int maxDevicesPerModule = 5;

                // 固定结果列的位置 - 基础列数(3) + 最大设备列数(5)
                int resultColumnIndex = 3 + maxDevicesPerModule;

                // 计算需要多少个模块
                int totalDevices = allDevices.size();
                int moduleCount = (int) Math.ceil((double) totalDevices / maxDevicesPerModule);

                // 分项总加权得分和总权重 - 所有模块共用
                BigDecimal totalWeightedScore = BigDecimal.ZERO;
                BigDecimal totalWeight = BigDecimal.ZERO;

                // 为每个模块处理数据
                for (int moduleIndex = 0; moduleIndex < moduleCount; moduleIndex++) {
                    // 当前模块的设备起始和结束索引
                    int startIdx = moduleIndex * maxDevicesPerModule;
                    int endIdx = Math.min(startIdx + maxDevicesPerModule, totalDevices);

                    // 获取当前模块的设备列表
                    List<DeviceInfo> moduleDevices = allDevices.subList(startIdx, endIdx);

                    // 创建表格 - 行数等于检查内容数量+1（表头）+1(新增一行表头)
                    int rowCount = contentOrder.size() + 2;
                    Tbl table = WordDocumentUtil.addTable(document, rowCount, resultColumnIndex + 1);

                    // 移除主表格的外框线，仅保留内框线
                    removeTableOuterBorders(table);

                    // 设置主表格列宽（固定宽度）
                    setMainTableFixedColumnWidths(table, resultColumnIndex + 1);
                    
                    // 设置表格字体为宋体小五号
                    setTableFont(table);
                    
                    // 设置表格间距为0，移除段落分隔
                    setTableSpacing(table);

                    try {
                        // 设置第一行第4-8列为"测试点单项检测指标评分"并合并
                        // 首先设置第一行所有单元格为空
                        for (int i = 4; i < resultColumnIndex; i++) {
                            WordDocumentUtil.setCellText(table, 0, i, "", 9, true, true, 2);
                        }

                        // 设置第二行表头（常规表头）- 使用false参数取消背景色
                        WordDocumentUtil.setCellText(table, 1, 0, "分项设施名称", 9, true, false, 2);
                        WordDocumentUtil.setCellText(table, 1, 1, "主要检查内容", 9, true, false, 2);
                        WordDocumentUtil.setCellText(table, 1, 2, "权重", 9, true, false, 2);
                        // 设置设备表头到第二行
                        int deviceColumn = 3;
                        for (DeviceInfo device : moduleDevices) {
                            // 确保不超出表格列数
                            if (deviceColumn < resultColumnIndex) {
                                String optimizedDisplayName = getOptimizedDisplayName(device);
                                WordDocumentUtil.setCellText(table, 1, deviceColumn++, optimizedDisplayName, 9, true, false, 2);
                            }
                        }

                        // 添加空列，保证结果列位置固定
                        while (deviceColumn < resultColumnIndex) {
                            WordDocumentUtil.setCellText(table, 1, deviceColumn++, formatTableCellText("/"), 9, true, false, 2);
                        }

                        // 设置结果列
                        WordDocumentUtil.setCellText(table, 0, resultColumnIndex, "单项检测指标得分结果", 9, true, false, 2);
                        WordDocumentUtil.setCellText(table, 1, resultColumnIndex, "单项检测指标得分结果", 9, true, false, 2);

                        // 在第一行中间(第4列)设置文本并合并4-8列 - 使用false参数取消背景色
                        WordDocumentUtil.setCellText(table, 0, 3, "测试点单项检测指标评分", 10, true, false, 2);
                        // 重要：复制第二行的内容到第一行，以便合并后显示内容
                        // 第1-3列和结果列会显示第二行内容
                        WordDocumentUtil.setCellText(table, 0, 0, "分项设施名称", 9, true, false, 2);
                        WordDocumentUtil.setCellText(table, 0, 1, "主要检查内容", 9, true, false, 2);
                        WordDocumentUtil.setCellText(table, 0, 2, "权重", 9, true, false, 2);

                    } catch (Exception e) {
                        log.error("设置表格表头失败", e);
                    }

                    // 垂直合并第1列、第2列、第3列和结果列的第1行和第2行
                    try {
                        mergeVerticalCells(table, 0, 0, 1); // 合并第1列(分项设施名称)
                        log.info("成功合并第1列");
                    } catch (Exception e) {
                        log.error("合并第1列失败", e);
                    }

                    try {
                        mergeVerticalCells(table, 1, 0, 1); // 合并第2列(主要检查内容)
                        log.info("成功合并第2列");
                    } catch (Exception e) {
                        log.error("合并第2列失败", e);
                    }

                    try {
                        mergeVerticalCells(table, 2, 0, 1); // 合并第3列(权重)
                        log.info("成功合并第3列");
                    } catch (Exception e) {
                        log.error("合并第3列失败", e);
                    }

                    try {
                        mergeVerticalCells(table, resultColumnIndex, 0, 1); // 合并结果列
                        log.info("成功合并结果列");
                    } catch (Exception e) {
                        log.error("合并结果列失败", e);
                    }


                    // 合并第一行的设备列（从第4列到结果列前一列）
                    if (resultColumnIndex > 4) {
                        WordDocumentUtil.mergeRowCellsSimple(table, 0, 3, resultColumnIndex - 1);
                    }
                    
                    // 移除主表格第1、7、8列的右边框线（索引分别为）
                    int[] columnsToRemoveRightBorder = {0,1,2,3,5,6,7};
                    removeSpecificColumnRightBorders(table, columnsToRemoveRightBorder,2);

                    int[] columnsToRemoveLeftBorder = {5};
                    removeSpecificColumnRightBorders(table, columnsToRemoveLeftBorder,1);

                    // 填充数据行 - 从第三行开始
                    int rowIndex = 2;
                    int itemNameStartRow = 2; // 记录当前分项名称开始的行，固定从第3行开始
                    for (String content : contentOrder) {
                        List<CheckEnumRelation> contentData = contentGroupedData.get(content);
                        if (contentData.isEmpty()) continue;

                        // 获取权重
                        BigDecimal weight = contentData.get(0).getWeight();
                        if (weight == null) weight = BigDecimal.ONE;

                        // 仅在第一个模块时累加权重
                        if (moduleIndex == 0) {
                            totalWeight = totalWeight.add(weight);
                        }

                        // 设置分项名称（只在第一行设置，后面的行留空等待合并）
                        if (rowIndex == itemNameStartRow) {
                            // 第一行数据设置分项名称
                            WordDocumentUtil.setCellText(table, rowIndex, 0, itemName, 9, false, false, 2);
                        } else {
                            // 后续行留空，等待合并
                            WordDocumentUtil.setCellText(table, rowIndex, 0, formatTableCellText("", false), 9, false, false, 2);
                        }
                        
                        WordDocumentUtil.setCellText(table, rowIndex, 1, content, 9, false, false, 1);
                        WordDocumentUtil.setCellText(table, rowIndex, 2, WordDocumentUtil.formatBigDecimal(weight, 1), 9, false, false, 2);

                        // 统计当前模块的设备得分（用于显示）
                        BigDecimal currentModuleTotalScore = BigDecimal.ZERO;
                        int currentModuleValidDeviceCount = 0;

                        // 填充当前模块的设备得分
                        int dataColumn = 3;
                        for (DeviceInfo device : moduleDevices) {
                            // 查找对应的得分记录
                            BigDecimal score = null;
                            for (CheckEnumRelation relation : contentData) {
                                if (device.getDeviceCode().equals(relation.getCode()) &&
                                        device.getLocationCode().equals(relation.getLocation())) {
                                    score = relation.getScore();
                                    break;
                                }
                            }

                            // 显示得分
                            if (score != null) {
                                if (score.compareTo(BigDecimal.ZERO) < 0) {
                                    WordDocumentUtil.setCellText(table, rowIndex, dataColumn, formatTableCellText("/"), 9, false, false, 2);
                                } else {
                                    WordDocumentUtil.setCellText(table, rowIndex, dataColumn, WordDocumentUtil.formatBigDecimal(score, 3), 9, false, false, 2);
                                    currentModuleTotalScore = currentModuleTotalScore.add(score);
                                    currentModuleValidDeviceCount++;
                                }
                            } else {
                                WordDocumentUtil.setCellText(table, rowIndex, dataColumn, formatTableCellText("/"), 9, false, false, 2);
                            }
                            dataColumn++;
                        }

                        // 计算整个分项下相同检查内容的所有设备得分（跨所有模块）
                        BigDecimal allDevicesTotalScore = BigDecimal.ZERO;
                        int allDevicesValidCount = 0;
                        boolean allValuesAreMissing = true;

                        // 遍历该分项下的所有设备，收集相同检查内容的所有得分
                        for (DeviceInfo device : allDevices) {
                            for (CheckEnumRelation relation : contentData) {
                                if (device.getDeviceCode().equals(relation.getCode()) &&
                                        device.getLocationCode().equals(relation.getLocation())) {
                                    // 如果至少有一个分值不是负值（不是"/"），标记为false
                                    if (relation.getScore() != null && relation.getScore().compareTo(BigDecimal.ZERO) >= 0) {
                                        allValuesAreMissing = false;
                                        allDevicesTotalScore = allDevicesTotalScore.add(relation.getScore());
                                        allDevicesValidCount++;
                                    }
                                break;
                                }
                            }
                        }

                        // 计算加权得分结果（基于所有设备的平均分）
                        BigDecimal weightedScore = null;

                        if (allValuesAreMissing) {
                            // 如果所有设备分值都是"/"，则直接使用权重值作为结果
                            weightedScore = weight;
                            log.info("分项[{}]检查内容[{}]所有设备分值均为'/'，使用权重值{}作为结果", itemName, content, WordDocumentUtil.formatBigDecimal(weight, 3));
                        } else {
                            // 正常计算加权分数（基于所有设备的平均分）
                            BigDecimal allDevicesAverage = BigDecimal.ZERO;
                            if (allDevicesValidCount > 0) {
                                // 计算加权得分 = 所有设备平均分 * 权重
                                weightedScore = weight.multiply(allDevicesTotalScore).divide(new BigDecimal(allDevicesValidCount), 3, RoundingMode.HALF_EVEN);
                            }
                            log.info("分项[{}]检查内容[{}]计算结果：所有设备总分={}, 有效设备数={}, 平均分={}, 权重={}, 加权得分={}",
                                    itemName, content, WordDocumentUtil.formatBigDecimal(allDevicesTotalScore, 3), allDevicesValidCount, 
                                    WordDocumentUtil.formatBigDecimal(allDevicesAverage, 3), WordDocumentUtil.formatBigDecimal(weight, 3), WordDocumentUtil.formatBigDecimal(weightedScore, 3));
                        }

                        // 仅在第一个模块时累加加权得分
                        if (moduleIndex == 0) {
                            totalWeightedScore = totalWeightedScore.add(weightedScore);
                        }

                        // 得分结果单元格 - 固定位置
                        // 只在最后一个模块显示计算结果，其他模块显示"/"
                        if (moduleIndex == moduleCount - 1) {
                        WordDocumentUtil.setCellText(table, rowIndex, resultColumnIndex, WordDocumentUtil.formatBigDecimalForScore(weightedScore, 3), 9, true, false, 2);
                        } else {
                            WordDocumentUtil.setCellText(table, rowIndex, resultColumnIndex, formatTableCellText("/"), 9, true, false, 2);
                        }

                        rowIndex++;
                    }

                    // 合并第一列相同分项的单元格（每个模块的每个表格都需要执行）
                    // 只要有多行数据就需要合并第一列
                    if (contentOrder.size() > 1) {
                        try {
                            int endRow = itemNameStartRow + contentOrder.size() - 1;
                            // 确保endRow不超出表格行数
                            int actualTableRows = rowIndex; // rowIndex此时指向下一行，即当前表格的总行数
                            if (endRow >= actualTableRows) {
                                endRow = actualTableRows - 1;
                            }
                            
                            if (endRow > itemNameStartRow) {
                                mergeVerticalCells(table, 0, itemNameStartRow, endRow);
                                log.info("成功合并分项[{}]的第一列单元格（模块{}），行范围: {}-{}，检查内容数: {}", 
                                        itemName, moduleIndex + 1, itemNameStartRow, endRow, contentOrder.size());
                            } else {
                                log.info("分项[{}]（模块{}）只有一行数据，无需合并", itemName, moduleIndex + 1);
                            }
                        } catch (Exception e) {
                            log.error("合并分项[{}]第一列单元格失败（模块{}）", itemName, moduleIndex + 1, e);
                        }
                    } else {
                        log.info("分项[{}]（模块{}）只有一个检查内容，无需合并第一列", itemName, moduleIndex + 1);
                    }

                    // 只在每个分项的最后一个模块添加总计表格和备注表格
                    if (moduleIndex == moduleCount - 1) {
                        // 计算最终分项得分
                        BigDecimal itemScore = BigDecimal.ZERO;
                        if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal rawScore = totalWeightedScore.divide(totalWeight, 4, RoundingMode.HALF_EVEN);
                            itemScore = rawScore.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_EVEN);
                        }

                        // 1. 先添加分项标题行表格（占用整行）
                        Tbl titleTable = WordDocumentUtil.addTable(document, 1, resultColumnIndex + 1);
                        
                        // 移除标题表格的外框线，仅保留内框线
                        removeTableOuterBorders(titleTable);
                        
                        setTitleTableFixedColumnWidths(titleTable, resultColumnIndex + 1);
                        setTableFont(titleTable);
                        setTableSpacing(titleTable);

                        String summaryText = itemName + "检测项目得分";
                        WordDocumentUtil.setCellText(titleTable, 0, 0, summaryText, 9, true, false, 2);

                        // 根据设备数量决定是否在标题行显示分项得分
                        if (allDevices.size() >= 2) {
                            // 设备数量>=2时，设置其他列为空，合并整行，不显示分项得分
                            for (int i = 1; i <= resultColumnIndex; i++) {
                                WordDocumentUtil.setCellText(titleTable, 0, i, "", 9, false, false, 2);
                            }
                            
                            // 合并整行
                            if (resultColumnIndex > 0) {
                                WordDocumentUtil.mergeRowCellsSimple(titleTable, 0, 0, resultColumnIndex);
                            }
                        } else {
                            // 设备数量<2时，设置其他列为空，除了最后一列显示分项得分
                            for (int i = 1; i < resultColumnIndex; i++) {
                                WordDocumentUtil.setCellText(titleTable, 0, i, "", 9, false, false, 2);
                            }
                            
                            // 最后一列显示分项得分
                            WordDocumentUtil.setCellText(titleTable, 0, resultColumnIndex, WordDocumentUtil.formatBigDecimalForScore(itemScore, 2), 9, true, false, 2);

                            // 合并除最后一列之外的其他列
                            if (resultColumnIndex > 1) {
                                WordDocumentUtil.mergeRowCellsSimple(titleTable, 0, 0, resultColumnIndex - 1);
                            }
                        }

                        // 2. 只有当设备数量大于等于2时才添加详细计算表格
                        if (allDevices.size() >= 2) {
                            createDetailedCalculationTable(document, contentOrder, contentGroupedData, 
                                                         allDevices, totalWeight, totalWeightedScore, itemName, itemScore);
                            log.info("分项[{}]设备数量={}，已创建详细计算表格", itemName, allDevices.size());
                        } else {
                            log.info("分项[{}]设备数量={}（小于2），跳过创建详细计算表格", itemName, allDevices.size());
                        }

                        // 3. 最后添加备注表格（放在分项的最后一行）
                        createRemarkTable(document, itemName, partCode, itemCode, damageRatioMap, partItemRemarkMap, tunnelInfo);

                        // 取消分项间的分页符，让表格内容正常连续展示
                        // 不再为不同分项添加分页符
                    }
                }
            }
        }
        
        // 最后遍历所有表格单元格，将空单元格填充为"-"
        fillEmptyTableCells(document);
    }







    /**
     * 设置表格列宽（按指定比例）
     * @param table 表格对象
     * @param totalColumns 总列数
     */
    private void setEqualColumnWidths(Tbl table, int totalColumns) {
        try {
            TblGrid tblGrid = table.getTblGrid();
            List<TblGridCol> gridCols = tblGrid.getGridCol();

            // 定义列宽比例：1.05  2.5   1  1.9   1.9   1.9   1.9   1.9  1.45
            double[] columnWidthRatios = {1.05, 3, 1.0, 1.9, 1.9, 1.9, 1.9, 1.9, 0.45};
            
            // 计算总比例
            double totalRatio = 0;
            for (int i = 0; i < Math.min(totalColumns, columnWidthRatios.length); i++) {
                totalRatio += columnWidthRatios[i];
            }
            
            // 总宽度（twips单位，约相当于A4纸宽度）
            double totalWidth = 10800; // 约19cm
            
            // 设置每列宽度
            for (int i = 0; i < totalColumns && i < columnWidthRatios.length; i++) {
                double columnWidth = (columnWidthRatios[i] / totalRatio) * totalWidth;
                gridCols.get(i).setW(BigInteger.valueOf((long) columnWidth));
            }
            
            log.info("成功设置表格列宽，总列数: {}", totalColumns);
        } catch (Exception e) {
            log.error("设置表格列宽失败", e);
        }
    }

    /**
     * 设置主表格列宽（固定宽度，不会因内容变化而浮动）
     * @param table 表格对象
     * @param totalColumns 总列数
     */
    private void setMainTableFixedColumnWidths(Tbl table, int totalColumns) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 确保表格布局为固定布局
            TblPr tblPr = table.getTblPr();
            if (tblPr == null) {
                tblPr = factory.createTblPr();
                table.setTblPr(tblPr);
            }
            
            // 强制设置为固定布局
            CTTblLayoutType layout = factory.createCTTblLayoutType();
            layout.setType(STTblLayoutType.FIXED);
            tblPr.setTblLayout(layout);
            
            // 设置表格网格
            TblGrid tblGrid = table.getTblGrid();
            List<TblGridCol> gridCols = tblGrid.getGridCol();

            // 定义列宽比例：分项设施名称  主要检查内容  权重  设备列1-5  结果列
            double[] columnWidthRatios = {2.0, 3.0, 1.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.45};
            
            // 计算总比例
            double totalRatio = 0;
            for (int i = 0; i < Math.min(totalColumns, columnWidthRatios.length); i++) {
                totalRatio += columnWidthRatios[i];
            }
            
            // 总宽度（twips单位）
            double totalWidth = 10800; // 约19cm
            
            // 设置每列宽度 - 严格按比例分配
            for (int i = 0; i < totalColumns && i < columnWidthRatios.length; i++) {
                double columnWidth = (columnWidthRatios[i] / totalRatio) * totalWidth;
                gridCols.get(i).setW(BigInteger.valueOf((long) columnWidth));
            }
            
            // 为每个单元格设置固定宽度，确保不会浮动
            setFixedCellWidths(table, totalColumns, columnWidthRatios, totalWidth, totalRatio);
            
            log.info("成功设置主表格固定列宽，总列数: {}", totalColumns);
        } catch (Exception e) {
            log.error("设置主表格列宽失败", e);
        }
    }

    /**
     * 设置标题表格列宽（固定宽度，与主表格保持一致）
     * @param table 表格对象
     * @param totalColumns 总列数
     */
    private void setTitleTableFixedColumnWidths(Tbl table, int totalColumns) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 确保表格布局为固定布局
            TblPr tblPr = table.getTblPr();
            if (tblPr == null) {
                tblPr = factory.createTblPr();
                table.setTblPr(tblPr);
            }
            
            // 强制设置为固定布局
            CTTblLayoutType layout = factory.createCTTblLayoutType();
            layout.setType(STTblLayoutType.FIXED);
            tblPr.setTblLayout(layout);
            
            // 设置表格网格
            TblGrid tblGrid = table.getTblGrid();
            List<TblGridCol> gridCols = tblGrid.getGridCol();

            // 使用与主表格相同的列宽比例
            double[] columnWidthRatios = {2, 3.0, 1.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.45};
            
            // 计算总比例
            double totalRatio = 0;
            for (int i = 0; i < Math.min(totalColumns, columnWidthRatios.length); i++) {
                totalRatio += columnWidthRatios[i];
            }
            
            // 总宽度（与主表格一致）
            double totalWidth = 10800; // 约19cm
            
            // 设置每列宽度 - 与主表格完全一致
            for (int i = 0; i < totalColumns && i < columnWidthRatios.length; i++) {
                double columnWidth = (columnWidthRatios[i] / totalRatio) * totalWidth;
                gridCols.get(i).setW(BigInteger.valueOf((long) columnWidth));
            }
            
            // 为每个单元格设置固定宽度
            setFixedCellWidths(table, totalColumns, columnWidthRatios, totalWidth, totalRatio);
            
            log.info("成功设置标题表格固定列宽，总列数: {}", totalColumns);
        } catch (Exception e) {
            log.error("设置标题表格列宽失败", e);
        }
    }

    /**
     * 为表格中的每个单元格设置固定宽度
     * @param table 表格对象
     * @param totalColumns 总列数
     * @param columnWidthRatios 列宽比例数组
     * @param totalWidth 总宽度
     * @param totalRatio 总比例
     */
    private void setFixedCellWidths(Tbl table, int totalColumns, double[] columnWidthRatios, double totalWidth, double totalRatio) {
        try {
            ObjectFactory factory = new ObjectFactory();
            List<Object> rows = table.getContent();
            
            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();
                    
                    for (int colIndex = 0; colIndex < cells.size() && colIndex < totalColumns && colIndex < columnWidthRatios.length; colIndex++) {
                        Object cellObj = cells.get(colIndex);
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            
                            // 获取或创建单元格属性
                            TcPr tcPr = cell.getTcPr();
                            if (tcPr == null) {
                                tcPr = factory.createTcPr();
                                cell.setTcPr(tcPr);
                            }
                            
                            // 设置单元格宽度
                            TblWidth cellWidth = factory.createTblWidth();
                            cellWidth.setType("dxa"); // 使用绝对单位
                            double columnWidth = (columnWidthRatios[colIndex] / totalRatio) * totalWidth;
                            cellWidth.setW(BigInteger.valueOf((long) columnWidth));
                            tcPr.setTcW(cellWidth);
                        }
                    }
                }
            }
            
            log.debug("成功为所有单元格设置固定宽度");
        } catch (Exception e) {
            log.error("设置单元格固定宽度失败", e);
        }
    }




    /**
     * 优化设备表头显示，特别是对"上行洞室"或"下行洞室"的位置
     * @param device 设备信息
     * @return 优化后的表头显示名称
     */
    private String getOptimizedDisplayName(DeviceInfo device) {
        String location = device.getLocationName();
        String deviceCode = device.getDeviceCode();
        String displayName = device.getDisplayName();
        String locationCode = device.getLocationCode();

        // 格式化设备编号
        String formattedCode = formatDeviceCode(deviceCode);

        // 检查是否为"上行洞室"或"下行洞室"
        if (location != null && (location.contains("上行洞室") || location.contains("下行洞室"))) {
            // 使用locationCode（如果有）
            if (locationCode != null && !locationCode.isEmpty()) {
                // 格式化locationCode - 只取最后两位数字
                String formattedLocationCode = "";
                String numericPart = locationCode.replaceAll("[^0-9]", "");

                try {
                    // 根据数字长度选择如何处理
                    if (numericPart.length() > 2) {
                        // 对于三位数及以上，只取最后两位
                        String lastTwoDigits = numericPart.substring(numericPart.length() - 2);
                        // 移除前导零并将结果作为位置编号
                        int number = Integer.parseInt(lastTwoDigits);
                        formattedLocationCode = String.valueOf(number);
                    } else {
                        // 对于两位数及以下，直接移除前导零
                        int number = Integer.parseInt(numericPart);
                        formattedLocationCode = String.valueOf(number);
                    }
                } catch (NumberFormatException e) {
                    log.warn("格式化位置编码异常: {}", numericPart, e);
                    formattedLocationCode = numericPart; // 保持原样
                }

                // 提取设备名称部分（进线柜、出线柜等）
                String deviceName = "";
                if (displayName != null) {
                    // 找到设备编号之后的部分，即设备名称
                    int idx = displayName.indexOf(deviceCode);
                    if (idx >= 0 && idx + deviceCode.length() < displayName.length()) {
                        deviceName = displayName.substring(idx + deviceCode.length()).trim();
                    }
                }

                // 组合新的显示名称
                String prefix = location.contains("上行洞室") ? "上行" : "下行";
                return prefix + formattedLocationCode + "号洞室" + formattedCode + deviceName;
            }
        }else if(location.contains("轴流风机房")){
            // 提取设备名称部分（进线柜、出线柜等）
            String deviceName = "";
            if (displayName != null) {
                // 找到设备编号之后的部分，即设备名称
                int idx = displayName.indexOf(deviceCode);
                if (idx >= 0 && idx + deviceCode.length() < displayName.length()) {
                    deviceName = displayName.substring(idx + deviceCode.length()).trim();
                }
            }
            return Integer.valueOf(locationCode.substring(locationCode.length()-1,locationCode.length())) + "号轴流风机房"+ formattedCode + deviceName;
        }

        // 如果不是特殊位置或没有获取到位置编号，则返回原始的格式化显示名
        return displayName.replace(deviceCode, formattedCode);
    }
    // 在DeviceInfo类之前添加一个工具方法用于格式化设备编号
    /**
     * 格式化设备编号为#格式
     * 将"001"、"002"这种转为"1#"、"2#"
     */
    private String formatDeviceCode(String code) {
        if (code == null || code.isEmpty()) {
            return code;
        }

        // 如果已经是#格式，直接返回
        if (code.contains("#")) {
            return code;
        }

        // 提取数字部分
        String numericPart = code.replaceAll("[^0-9]", "");
        try {
            // 移除前导零并添加#
            int number = Integer.parseInt(numericPart);
            return number + "#";
        } catch (NumberFormatException e) {
            // 格式化失败，返回原始编码
            return code;
        }
    }

    /**
     * 垂直合并表格单元格（合并列）
     * @param table 表格对象
     * @param colIndex 要合并的列索引
     * @param startRow 开始行索引
     * @param endRow 结束行索引
     */
    private void mergeVerticalCells(Tbl table, int colIndex, int startRow, int endRow) {
        try {
            if (table == null || colIndex < 0 || startRow < 0 || endRow <= startRow) {
                log.error("垂直合并单元格参数无效: colIndex={}, startRow={}, endRow={}", colIndex, startRow, endRow);
                return;
            }
            
            List<Object> rows = table.getContent();
            if (rows.size() <= endRow) {
                log.warn("垂直合并单元格行索引超出范围，调整endRow: 原endRow={}, rows.size()={}, 调整后endRow={}", 
                        endRow, rows.size(), rows.size() - 1);
                endRow = rows.size() - 1;
                if (endRow <= startRow) {
                    log.error("调整后endRow仍然无效，放弃合并: startRow={}, endRow={}", startRow, endRow);
                    return;
                }
            }
            
            // 获取第一个单元格
            Object rowObj = rows.get(startRow);
            if (!(rowObj instanceof Tr)) {
                log.error("无法获取起始行，行类型不是Tr");
                return;
            }
            
            Tr firstRow = (Tr) rowObj;
            List<Object> cells = firstRow.getContent();
            
            if (cells.size() <= colIndex) {
                log.error("垂直合并单元格列索引超出范围: colIndex={}, cells.size()={}", colIndex, cells.size());
                return;
            }
            
            // 获取第一个单元格，处理可能的JAXBElement包装
            Tc firstCell = null;
            Object cellObj = cells.get(colIndex);
            if (cellObj instanceof Tc) {
                firstCell = (Tc) cellObj;
            } else if (cellObj instanceof JAXBElement) {
                JAXBElement<?> jaxbElement = (JAXBElement<?>) cellObj;
                if (jaxbElement.getValue() instanceof Tc) {
                    firstCell = (Tc) jaxbElement.getValue();
                }
            }
            
            if (firstCell == null) {
                log.error("无法获取要合并的起始单元格，类型是: {}", cellObj.getClass().getName());
                return;
            }
            
            // 设置垂直合并属性
            TcPr tcPr = firstCell.getTcPr();
            if (tcPr == null) {
                ObjectFactory factory = new ObjectFactory();
                tcPr = factory.createTcPr();
                firstCell.setTcPr(tcPr);
            }
            
            // 设置为"开始"垂直合并
            ObjectFactory factory = new ObjectFactory();
            TcPrInner.VMerge vMerge = factory.createTcPrInnerVMerge();
            vMerge.setVal("restart");
            tcPr.setVMerge(vMerge);
            
            // 处理后续单元格
            for (int i = startRow + 1; i <= endRow; i++) {
                Object nextRowObj = rows.get(i);
                if (!(nextRowObj instanceof Tr)) {
                    continue;
                }
                
                Tr row = (Tr) nextRowObj;
                List<Object> rowCells = row.getContent();
                
                if (rowCells.size() <= colIndex) {
                    continue;
                }
                
                // 获取单元格，处理可能的JAXBElement包装
                Tc cell = null;
                Object cellObject = rowCells.get(colIndex);
                if (cellObject instanceof Tc) {
                    cell = (Tc) cellObject;
                } else if (cellObject instanceof JAXBElement) {
                    JAXBElement<?> jaxbElement = (JAXBElement<?>) cellObject;
                    if (jaxbElement.getValue() instanceof Tc) {
                        cell = (Tc) jaxbElement.getValue();
                    }
                }
                
                if (cell == null) {
                    log.warn("无法获取要合并的单元格，行: {}, 列: {}, 类型: {}", i, colIndex, 
                             cellObject != null ? cellObject.getClass().getName() : "null");
                    continue;
                }
                
                TcPr cellPr = cell.getTcPr();
                if (cellPr == null) {
                    cellPr = factory.createTcPr();
                    cell.setTcPr(cellPr);
                }
                
                // 设置为"继续"垂直合并
                TcPrInner.VMerge cellVMerge = factory.createTcPrInnerVMerge();
                // 不设置val，默认为"continue"
                cellPr.setVMerge(cellVMerge);
                
                // 清空内容，确保不会显示重复内容
                cell.getContent().clear();
                P p = factory.createP();
                cell.getContent().add(p);
            }
            
            log.info("垂直合并单元格成功: colIndex={}, startRow={}, endRow={}", colIndex, startRow, endRow);
        } catch (Exception e) {
            log.error("垂直合并单元格失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 设置表格行的高度
     * @param table 表格对象
     * @param rowIndex 行索引
     * @param height 高度值（单位：twip）
     */
    private void setTableRowHeight(Tbl table, int rowIndex, int height) {
        try {
            List<Object> rows = table.getContent();
            if (rows.size() <= rowIndex) {
                log.error("设置行高失败：行索引超出范围 rowIndex={}, rows.size()={}", rowIndex, rows.size());
                return;
            }
            
            Object rowObj = rows.get(rowIndex);
            if (!(rowObj instanceof Tr)) {
                log.error("设置行高失败：行对象类型不是Tr");
                return;
            }
            
            Tr row = (Tr) rowObj;
            ObjectFactory factory = new ObjectFactory();
            TrPr trPr = row.getTrPr();
            if (trPr == null) {
                trPr = factory.createTrPr();
                row.setTrPr(trPr);
            }
            
            // 创建行高设置
            CTHeight trHeight = new CTHeight();
            trHeight.setVal(BigInteger.valueOf(height));
            trHeight.setHRule(STHeightRule.AT_LEAST); // 设置为至少这个高度
            
            // 创建TrHeight元素并设置值
            JAXBElement<CTHeight> trHeightWrapped = 
                factory.createCTTrPrBaseTrHeight(trHeight);
            
            // 应用设置
            trPr.getCnfStyleOrDivIdOrGridBefore().add(trHeightWrapped);
            
            log.info("成功设置表格行高: rowIndex={}, height={}", rowIndex, height);
        } catch (Exception e) {
            log.error("设置表格行高失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加分页符
     * @param document Word文档
     */
    private void addPageBreak(WordprocessingMLPackage document) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            ObjectFactory factory = new ObjectFactory();
            
            // 创建段落
            P p = factory.createP();
            
            // 创建运行
            R r = factory.createR();
            
            // 创建分页符
            Br br = factory.createBr();
            br.setType(STBrType.PAGE);
            
            r.getContent().add(br);
            p.getContent().add(r);
            
            mdp.addObject(p);
            
            log.info("成功添加分页符");
        } catch (Exception e) {
            log.error("添加分页符失败", e);
            throw new RuntimeException("添加分页符失败", e);
        }
    }

    /**
     * 添加自定义格式的表格标题（黑体15.5字号，一级标题）
     * @param document Word文档
     * @param text 标题文本内容
     * @param isFirstTitle 是否是第一个标题
     * @return P 段落对象
     */
    private P addCustomTableTitle(WordprocessingMLPackage document, String text, boolean isFirstTitle) {
        try {
            MainDocumentPart mdp = document.getMainDocumentPart();
            ObjectFactory factory = new ObjectFactory();
            
            P p;
            if (isFirstTitle) {
                // 对于第一个标题，使用文档的最后一个段落（通常是空行）
                List<Object> docObjects = mdp.getContent();
                P lastParagraph = null;
                
                // 从后往前查找最后一个段落
                for (int i = docObjects.size() - 1; i >= 0; i--) {
                    Object obj = docObjects.get(i);
                    if (obj instanceof P) {
                        lastParagraph = (P) obj;
                        break;
                    }
                }
                
                if (lastParagraph != null) {
                    // 使用最后一个段落（空行）
                    p = lastParagraph;
                    // 清空现有内容
                    p.getContent().clear();
                    log.info("使用文档最后一个段落作为第一个附表标题");
                } else {
                    // 没有找到段落，创建新的
                    p = factory.createP();
                    log.info("未找到现有段落，创建新的第一个标题段落");
                }
            } else {
                // 非第一个标题，正常创建新段落
                p = factory.createP();
            }
            
            // 设置段落属性
            PPr ppr = factory.createPPr();
            
            // 设置左对齐
            Jc jc = factory.createJc();
            jc.setVal(JcEnumeration.LEFT);
            ppr.setJc(jc);
            
            // 设置大纲级别为一级标题（0表示一级标题）- 这是在导航视图中显示的关键
            PPrBase.OutlineLvl outlineLvl = factory.createPPrBaseOutlineLvl();
            outlineLvl.setVal(BigInteger.ZERO); // 0 = 一级标题
            ppr.setOutlineLvl(outlineLvl);
            
            // 不设置预定义的Heading1样式，避免影响全局样式
            // PPrBase.PStyle pStyle = factory.createPPrBasePStyle();
            // pStyle.setVal("Heading1"); // 注释掉这行，避免影响模板样式
            // ppr.setPStyle(pStyle);
            
            p.setPPr(ppr);
            
            // 创建文本运行
            R r = factory.createR();
            Text t = factory.createText();
            t.setValue(text);
            t.setSpace("preserve");
            r.getContent().add(t);
            
            // 直接在Run级别设置字体样式，不依赖段落样式
            RPr rpr = factory.createRPr();
            
            // 设置字体为黑体
            RFonts rfonts = factory.createRFonts();
            rfonts.setEastAsia("黑体");
            rfonts.setHAnsi("黑体");
            rpr.setRFonts(rfonts);
            
            // 设置字体大小为15.5磅（15.5pt = 31半磅）
            HpsMeasure sz = factory.createHpsMeasure();
            sz.setVal(BigInteger.valueOf(31)); // 15.5磅 * 2 = 31半磅
            rpr.setSz(sz);
            rpr.setSzCs(sz);
            
            // 设置字体加粗
            BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
            b.setVal(true);
            rpr.setB(b);
            
            // 设置字体颜色为黑色
            Color color = factory.createColor();
            color.setVal("000000"); // 黑色
            rpr.setColor(color);
            
            r.setRPr(rpr);
            p.getContent().add(r);
            
            // 只有非第一个标题或者第一个标题但没有使用现有段落时才添加到文档
            if (!isFirstTitle || !mdp.getContent().contains(p)) {
                mdp.addObject(p);
            }
            log.info("成功添加一级标题（不使用预定义样式）: {}", text);
            return p;
        } catch (Exception e) {
            log.error("添加一级标题失败", e);
            throw new RuntimeException("添加一级标题失败", e);
        }
    }

    /**
     * 设置表格字体为宋体小五号
     * @param table 表格对象
     */
    private void setTableFont(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = new ObjectFactory();
            
            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();
                    
                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            setTableCellFont(cell, factory);
                        }
                    }
                }
            }
            
            log.debug("成功设置表格字体为宋体小五号");
        } catch (Exception e) {
            log.error("设置表格字体失败", e);
        }
    }

    /**
     * 设置表格单元格字体为宋体小五号
     * @param cell 单元格对象
     * @param factory 对象工厂
     */
    private void setTableCellFont(Tc cell, ObjectFactory factory) {
        try {
            List<Object> cellContent = cell.getContent();
            
            for (Object content : cellContent) {
                if (content instanceof P) {
                    P paragraph = (P) content;
                    List<Object> paragraphContent = paragraph.getContent();
                    
                    for (Object pContent : paragraphContent) {
                        if (pContent instanceof R) {
                            R run = (R) pContent;
                            
                            // 获取或创建运行属性
                            RPr rpr = run.getRPr();
                            if (rpr == null) {
                                rpr = factory.createRPr();
                                run.setRPr(rpr);
                            }
                            
                            // 设置字体为宋体
                            RFonts rFonts = rpr.getRFonts();
                            if (rFonts == null) {
                                rFonts = factory.createRFonts();
                                rpr.setRFonts(rFonts);
                            }
                            rFonts.setEastAsia("宋体");
                            rFonts.setHAnsi("宋体");
                            
                            // 设置字号为小五号（9pt = 18半磅）
                            HpsMeasure fontSize = factory.createHpsMeasure();
                            fontSize.setVal(BigInteger.valueOf(18));
                            rpr.setSz(fontSize);
                            rpr.setSzCs(fontSize);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置单元格字体失败", e);
        }
    }

    /**
     * 创建详细计算表格，展示分项得分的计算过程
     * @param document Word文档
     * @param contentOrder 检查内容顺序
     * @param contentGroupedData 按检查内容分组的数据
     * @param allDevices 所有设备信息
     * @param totalWeight 总权重
     * @param totalWeightedScore 总加权得分
     * @param itemName 分项名称
     * @param itemScore 分项得分
     */
    private void createDetailedCalculationTable(WordprocessingMLPackage document, List<String> contentOrder,
                                               Map<String, List<CheckEnumRelation>> contentGroupedData,
                                               List<DeviceInfo> allDevices, BigDecimal totalWeight,
                                               BigDecimal totalWeightedScore, String itemName, BigDecimal itemScore) {
        try {
            // 创建表格：行数 = 检查内容数量 + 1（表头）
            int rowCount = contentOrder.size() + 1;
            Tbl detailTable = WordDocumentUtil.addTable(document, rowCount, 5);
            
            // 移除详细计算表格的外框线，仅保留内框线
            removeTableOuterBorders(detailTable);
            
            // 移除详细计算表格第1、2、3列的右边框线（索引分别为0、1、2）
            int[] detailTableColumnsToRemoveRightBorder = {0, 1, 2};
            removeSpecificColumnRightBorders(detailTable, detailTableColumnsToRemoveRightBorder,2);
            
            // 计算主表格的总宽度（基于setEqualColumnWidths方法中的逻辑）
            double[] mainTableRatios = {1.05, 2.5, 1.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.45};
            double mainTableTotalRatio = 0;
            for (double ratio : mainTableRatios) {
                mainTableTotalRatio += ratio;
            }
            double mainTableTotalWidth = 10800; // 与setEqualColumnWidths中的总宽度一致
            
            // 为5列详细计算表格设置列宽，保持总宽度与主表格一致
            TblGrid tblGrid = detailTable.getTblGrid();
            List<TblGridCol> gridCols = tblGrid.getGridCol();
            
            // 定义5列的比例（可以根据需要调整，这里使用相对平均的分配）
            double[] detailTableRatios = {5.4, 2.5, 2.5, 2.5, 2.6}; // 主要检查内容、权重、平均分、得分结果、分项得分
            double detailTableTotalRatio = 0;
            for (double ratio : detailTableRatios) {
                detailTableTotalRatio += ratio;
            }
            
            // 按比例分配宽度，但总宽度与主表格一致
            for (int i = 0; i < 5; i++) {
                double columnWidth = (detailTableRatios[i] / detailTableTotalRatio) * mainTableTotalWidth;
                gridCols.get(i).setW(BigInteger.valueOf((long) columnWidth));
            }
            
            // 设置表格字体
            setTableFont(detailTable);
            
            // 设置表格间距
            setTableSpacing(detailTable);
            
            // 设置表头 - 使用false参数取消背景色
            WordDocumentUtil.setCellText(detailTable, 0, 0, "主要检查内容", 9, true, false, 2);
            WordDocumentUtil.setCellText(detailTable, 0, 1, "①权重", 9, true, false, 2);
            WordDocumentUtil.setCellText(detailTable, 0, 2, "②单项检测指标平均分", 9, true, false, 2);
            WordDocumentUtil.setCellText(detailTable, 0, 3, "③单项检测指标得分结果(①×②)", 9, true, false, 2);
            WordDocumentUtil.setCellText(detailTable, 0, 4, itemName + "检测项目得分((∑③/∑①)×100)", 9, true, false, 2);
            
            // 填充数据行
            int rowIndex = 1;
            for (String content : contentOrder) {
                List<CheckEnumRelation> contentData = contentGroupedData.get(content);
                if (contentData.isEmpty()) continue;
                
                // 获取权重
                BigDecimal weight = contentData.get(0).getWeight();
                if (weight == null) weight = BigDecimal.ONE;
                
                // 计算当前检查内容的平均分
                BigDecimal totalScore = BigDecimal.ZERO;
                int validDeviceCount = 0;
                boolean allValuesAreMissing = true;
                
                for (DeviceInfo device : allDevices) {
                    for (CheckEnumRelation relation : contentData) {
                        if (device.getDeviceCode().equals(relation.getCode()) &&
                                device.getLocationCode().equals(relation.getLocation())) {
                            if (relation.getScore() != null && relation.getScore().compareTo(BigDecimal.ZERO) >= 0) {
                                allValuesAreMissing = false;
                                totalScore = totalScore.add(relation.getScore());
                                validDeviceCount++;
                            }
                            break;
                        }
                    }
                }
                
                BigDecimal averageScore;
                BigDecimal weightedScore;
                
                if (allValuesAreMissing) {
                    // 如果所有设备分值都是"/"，则平均分为权重值
                    averageScore = weight;
                    weightedScore = weight;
                } else {
                    // 计算平均分
                    averageScore = validDeviceCount > 0 ? 
                        totalScore.divide(new BigDecimal(validDeviceCount), 3, RoundingMode.HALF_EVEN) : 
                        BigDecimal.ZERO;
                    // 计算加权得分
                    weightedScore = weight.multiply(averageScore);
                }
                
                // 填充表格数据
                WordDocumentUtil.setCellText(detailTable, rowIndex, 0, content, 9, false, false, 1);
                WordDocumentUtil.setCellText(detailTable, rowIndex, 1, WordDocumentUtil.formatBigDecimal(weight, 1), 9, false, false, 2);
                WordDocumentUtil.setCellText(detailTable, rowIndex, 2, WordDocumentUtil.formatBigDecimal(averageScore, 3), 9, false, false, 2);
                WordDocumentUtil.setCellText(detailTable, rowIndex, 3, WordDocumentUtil.formatBigDecimalForScore(weightedScore, 3), 9, false, false, 2);
                
                // 第5列只在第一行显示分项得分，其他行为空
                if (rowIndex == 1) {
                    WordDocumentUtil.setCellText(detailTable, rowIndex, 4, WordDocumentUtil.formatBigDecimalForScore(itemScore, 2), 9, true, false, 2);
                } else {
                    WordDocumentUtil.setCellText(detailTable, rowIndex, 4, "", 9, false, false, 2);
                }
                
                rowIndex++;
            }
            
            // 合并第5列（分项得分列）
            if (contentOrder.size() > 1) {
                mergeVerticalCells(detailTable, 4, 1, contentOrder.size());
                log.info("成功合并分项得分列，行范围: 1-{}", contentOrder.size());
            }
            
                         log.info("成功创建分项[{}]的详细计算表格", itemName);
         } catch (Exception e) {
             log.error("创建详细计算表格失败", e);
         }
     }

    /**
     * 创建备注表格
     * @param document Word文档
     * @param itemName 分项名称
     * @param partCode 分部代码
     * @param itemCode 分项代码
     * @param damageRatioMap 损坏率映射
     * @param partItemRemarkMap 备注映射
     * @param tunnelInfo 隧道信息
     */
    private void createRemarkTable(WordprocessingMLPackage document, String itemName, String partCode, String itemCode,
                                 Map<String, BigDecimal> damageRatioMap, Map<String, String> partItemRemarkMap, TunnelInfo tunnelInfo) {
        try {
            // 获取备注信息
            String remarkKey = tunnelInfo.getId() + "_" + partCode + "_" + itemCode;
            String remark = partItemRemarkMap.getOrDefault(remarkKey, "");

            // 创建备注表格（5列，与详细计算表格保持一致）
            Tbl remarkTable = WordDocumentUtil.addTable(document, 1, 5);
            
            // 移除备注表格的外框线，仅保留内框线
            removeTableOuterBorders(remarkTable);
            
            // 设置与主表格相同的总宽度
            TblGrid tblGrid = remarkTable.getTblGrid();
            List<TblGridCol> gridCols = tblGrid.getGridCol();
            
            // 使用与详细计算表格相同的总宽度和列宽比例
            double mainTableTotalWidth = 10800; // 与主表格总宽度一致
            double[] detailTableRatios = {3.0, 1.5, 2.0, 2.0, 3.0}; // 与详细计算表格相同的比例
            double detailTableTotalRatio = 0;
            for (double ratio : detailTableRatios) {
                detailTableTotalRatio += ratio;
            }
            
            // 按比例分配宽度
            for (int i = 0; i < 5; i++) {
                double columnWidth = (detailTableRatios[i] / detailTableTotalRatio) * mainTableTotalWidth;
                gridCols.get(i).setW(BigInteger.valueOf((long) columnWidth));
            }
            
            // 设置表格字体
            setTableFont(remarkTable);
            
            // 设置表格间距
            setTableSpacing(remarkTable);

            // 备注内容
            StringBuilder remarkBuilder = new StringBuilder("备注：");

            if (!StringUtils.isBlank(remark)) {
                // 使用从partItemRemarkMap获取的备注内容
                remarkBuilder.append(remark);
            } else {
                remarkBuilder = new StringBuilder("备注：-");
            }

            // 添加损坏率/完好率信息（如果有）
            if (damageRatioMap.containsKey(itemName)) {
                BigDecimal damageRatio = damageRatioMap.get(itemName);
                BigDecimal goodRatio = damageRatio.multiply(new BigDecimal(100));

                // 确保有分隔符
                if (remarkBuilder.length() > 0 && !remarkBuilder.toString().equals("备注：-")) {
                    remarkBuilder.append("，");
                } else if (remarkBuilder.toString().equals("备注：-")) {
                    remarkBuilder = new StringBuilder("备注：");
                }

                // 添加完好率信息
                remarkBuilder.append(itemName).append("完好率为").append(goodRatio.setScale(0, RoundingMode.HALF_EVEN)).append("%");
            }

            // 设置备注内容
            WordDocumentUtil.setCellText(remarkTable, 0, 0, remarkBuilder.toString(), 9, false, false, 1);

            // 填充其他列为空
            for (int i = 1; i < 5; i++) {
                WordDocumentUtil.setCellText(remarkTable, 0, i, "", 9, false, false, 1);
            }

            // 合并备注行的所有单元格
            WordDocumentUtil.mergeRowCellsSimple(remarkTable, 0, 0, 4);

            // 设置备注行的高度为正常高度的3倍
            setTableRowHeight(remarkTable, 0, 2400);
            
            log.info("成功创建分项[{}]的备注表格", itemName);
        } catch (Exception e) {
            log.error("创建备注表格失败", e);
        }
    }

    /**
     * 设置表格间距为0，移除段落分隔，彻底消除表格间隙
     * @param table 表格对象
     */
    private void setTableSpacing(Tbl table) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 获取或创建表格属性
            TblPr tblPr = table.getTblPr();
            if (tblPr == null) {
                tblPr = factory.createTblPr();
                table.setTblPr(tblPr);
            }
            
            // 设置表格单元格间距为0
            TblWidth cellSpacing = factory.createTblWidth();
            cellSpacing.setW(BigInteger.ZERO);
            cellSpacing.setType("dxa");
            tblPr.setTblCellSpacing(cellSpacing);
            
            // 设置表格缩进为0
            TblWidth tblInd = factory.createTblWidth();
            tblInd.setW(BigInteger.ZERO);
            tblInd.setType("dxa");
            tblPr.setTblInd(tblInd);
            
            // 彻底消除表格上下间距 - 设置表格前后的段落间距为0
            try {
                // 设置表格前间距为0
                TblWidth tblSpacingBefore = factory.createTblWidth();
                tblSpacingBefore.setW(BigInteger.ZERO);
                tblSpacingBefore.setType("dxa");
                
                // 设置表格后间距为0  
                TblWidth tblSpacingAfter = factory.createTblWidth();
                tblSpacingAfter.setW(BigInteger.ZERO);
                tblSpacingAfter.setType("dxa");
                
                log.debug("表格前后间距设置完成");
            } catch (Exception ex) {
                log.warn("设置表格前后间距时出现异常: {}", ex.getMessage());
            }
            
            log.debug("成功设置表格间距为0");
        } catch (Exception e) {
            log.error("设置表格间距失败", e);
        }
    }

    /**
     * 移除文档开始的空段落
     * @param document Word文档对象
     */
    private void removeInitialEmptyParagraphs(WordprocessingMLPackage document) {
        try {
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            
            // 从开头删除空段落，直到遇到非空段落或非段落元素
            while (!docObjects.isEmpty()) {
                Object firstObj = docObjects.get(0);
                
                if (firstObj instanceof P) {
                    P paragraph = (P) firstObj;
                    String paragraphText = getParagraphText(paragraph);
                    
                    // 如果是空段落，删除它
                    if (paragraphText == null || paragraphText.trim().isEmpty()) {
                        docObjects.remove(0);
                        log.debug("删除文档开始的空段落");
                    } else {
                        // 遇到非空段落，停止删除
                        break;
                    }
                } else {
                    // 遇到非段落元素，停止删除
                    break;
                }
            }
            
            log.info("成功清理文档开始的空行");
        } catch (Exception e) {
            log.error("清理文档开始空行失败", e);
        }
    }

    /**
     * 获取段落的文本内容
     * @param paragraph 段落对象
     * @return 段落文本
     */
    private String getParagraphText(P paragraph) {
        if (paragraph == null) return "";
        
        StringBuilder text = new StringBuilder();
        for (Object content : paragraph.getContent()) {
            if (content instanceof R) {
                R run = (R) content;
                for (Object runContent : run.getContent()) {
                    if (runContent instanceof Text) {
                        Text textNode = (Text) runContent;
                        text.append(textNode.getValue());
                    }
                }
            }
        }
        return text.toString();
    }

    /**
     * 移除附表区域段落的间距，确保表格紧密排列
     * 只处理从第一个"附表"标题开始的内容，不影响模板前面的格式
     * @param document Word文档对象
     */
    private void removeAttachmentTablesParagraphSpacing(WordprocessingMLPackage document) {
        try {
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = new ObjectFactory();
            
            // 寻找第一个"附表"标题的位置
            int startIndex = -1;
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String paragraphText = getParagraphText(paragraph);
                    // 更精确的匹配：附表 + 数字 + 、 + 内容 + 检查结果
                    if (paragraphText != null && paragraphText.matches(".*附表\\s*\\d+\\s*、.*检查结果.*")) {
                        startIndex = i;
                        log.info("✓ 找到第一个附表标题位置: {}, 内容: {}", i, paragraphText.trim());
                        break;
                    }
                }
            }
            
            // 如果没有找到附表标题，则不处理任何段落
            if (startIndex == -1) {
                log.warn("未找到附表标题，跳过段落间距处理");
                return;
            }
            
            int processedCount = 0;
            // 只处理从附表标题开始的段落
            for (int i = startIndex; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    
                    // 获取或创建段落属性
                    PPr ppr = paragraph.getPPr();
                    if (ppr == null) {
                        ppr = factory.createPPr();
                        paragraph.setPPr(ppr);
                    }
                    
                    // 彻底消除段落前后间距和行间距
                    PPrBase.Spacing spacing = factory.createPPrBaseSpacing();
                    spacing.setBefore(BigInteger.ZERO);
                    spacing.setAfter(BigInteger.ZERO);
                    spacing.setLine(BigInteger.valueOf(100)); // 极小的行距，几乎为0
                    spacing.setLineRule(STLineSpacingRule.EXACT); // 使用精确行距
                    ppr.setSpacing(spacing);
                    
                    // 额外设置段落缩进为0
                    PPrBase.Ind ind = factory.createPPrBaseInd();
                    ind.setLeft(BigInteger.ZERO);
                    ind.setRight(BigInteger.ZERO);
                    ind.setFirstLine(BigInteger.ZERO);
                    ppr.setInd(ind);
                    processedCount++;
                }
            }
            
            log.info("✓ 段落间距优化完成：处理了{}个附表区域段落，保留了模板前{}个对象的原始格式", 
                    processedCount, startIndex);
        } catch (Exception e) {
            log.error("移除段落间距失败", e);
        }
    }

    /**
     * 在表格后添加一个极小间距的段落，确保表格间紧密连接
     * @param document Word文档对象
     */
    private void addTightParagraphAfterTable(WordprocessingMLPackage document) {
        try {
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            ObjectFactory factory = new ObjectFactory();
            
            // 创建一个空段落
            P paragraph = factory.createP();
            
            // 设置段落属性为极小间距
            PPr ppr = factory.createPPr();
            
            // 设置段落间距为0
            PPrBase.Spacing spacing = factory.createPPrBaseSpacing();
            spacing.setBefore(BigInteger.ZERO);
            spacing.setAfter(BigInteger.ZERO);
            spacing.setLine(BigInteger.valueOf(50)); // 极小的行距
            spacing.setLineRule(STLineSpacingRule.EXACT);
            ppr.setSpacing(spacing);
            
            // 设置段落缩进为0
            PPrBase.Ind ind = factory.createPPrBaseInd();
            ind.setLeft(BigInteger.ZERO);
            ind.setRight(BigInteger.ZERO);
            ind.setFirstLine(BigInteger.ZERO);
            ppr.setInd(ind);
            
            paragraph.setPPr(ppr);
            
            // 添加空的文本运行
            R run = factory.createR();
            Text text = factory.createText();
            text.setValue("");
            run.getContent().add(text);
            paragraph.getContent().add(run);
            
            // 将段落添加到文档
            mainDocumentPart.addObject(paragraph);
            
            log.debug("添加紧密连接段落完成");
        } catch (Exception e) {
            log.error("添加紧密连接段落失败", e);
        }
    }

    /**
     * 移除表格的外框线，仅保留内框线
     * @param table 表格对象
     */
    private void removeTableOuterBorders(Tbl table) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 获取或创建表格属性
            TblPr tblPr = table.getTblPr();
            if (tblPr == null) {
                tblPr = factory.createTblPr();
                table.setTblPr(tblPr);
            }
            
            // 重新设置边框，只保留内框线
            TblBorders borders = factory.createTblBorders();
            CTBorder solidBorder = factory.createCTBorder();
            solidBorder.setVal(STBorder.SINGLE);
            solidBorder.setColor("000000");
            
            // 只设置内框线，不设置外框线
            borders.setInsideH(solidBorder);  // 内框线：水平
            borders.setInsideV(solidBorder);  // 内框线：垂直
            
            // 外框线设置为无边框
            CTBorder noBorder = factory.createCTBorder();
            noBorder.setVal(STBorder.NONE);
            borders.setTop(noBorder);      // 外框线：上 - 无边框
            borders.setBottom(noBorder);   // 外框线：下 - 无边框
            borders.setLeft(noBorder);     // 外框线：左 - 无边框
            borders.setRight(noBorder);    // 外框线：右 - 无边框
            
            tblPr.setTblBorders(borders);
            
            log.debug("成功移除表格外框线，保留内框线");
        } catch (Exception e) {
            log.error("移除表格外框线失败", e);
        }
    }

    /**
     * 移除主表格指定列的右边框线
     * @param table 表格对象
     * @param columnsToRemoveRightBorder 需要移除右边框的列索引数组（0-based）
     */
    private void removeSpecificColumnRightBorders(Tbl table, int[] columnsToRemoveRightBorder,int rightOrLeft) {
        try {
            ObjectFactory factory = new ObjectFactory();
            List<Object> rows = table.getContent();
            
            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();
                    
                    for (int colIndex = 0; colIndex < cells.size(); colIndex++) {
                        // 检查当前列是否需要移除右边框
                        boolean shouldRemoveRightBorder = false;
                        for (int targetCol : columnsToRemoveRightBorder) {
                            if (colIndex == targetCol) {
                                shouldRemoveRightBorder = true;
                                break;
                            }
                        }
                        
                        if (shouldRemoveRightBorder) {
                            Object cellObj = cells.get(colIndex);
                            Tc cell = null;
                            
                            if (cellObj instanceof Tc) {
                                cell = (Tc) cellObj;
                            } else if (cellObj instanceof JAXBElement) {
                                JAXBElement<?> jaxbElement = (JAXBElement<?>) cellObj;
                                if (jaxbElement.getValue() instanceof Tc) {
                                    cell = (Tc) jaxbElement.getValue();
                                }
                            }
                            
                            if (cell != null) {
                                // 获取或创建单元格属性
                                TcPr tcPr = cell.getTcPr();
                                if (tcPr == null) {
                                    tcPr = factory.createTcPr();
                                    cell.setTcPr(tcPr);
                                }
                                
                                // 获取或创建单元格边框设置
                                TcPrInner.TcBorders tcBorders = tcPr.getTcBorders();
                                if (tcBorders == null) {
                                    tcBorders = factory.createTcPrInnerTcBorders();
                                    tcPr.setTcBorders(tcBorders);
                                }
                                
                                // 设置右边框为无边框
                                CTBorder noBorder = factory.createCTBorder();
                                if(Objects.equals(2,rightOrLeft)){
                                    tcBorders.setRight(noBorder);
                                }else{
                                    tcBorders.setLeft(noBorder);
                                }
                                noBorder.setVal(STBorder.NONE);

                            }
                        }
                    }
                }
            }
            
            log.info("成功移除主表格指定列的右边框线: {}", Arrays.toString(columnsToRemoveRightBorder));
        } catch (Exception e) {
            log.error("移除主表格指定列右边框线失败", e);
        }
    }

    /**
     * 格式化表格单元格文本，将"/"和空字符串转换为"-"
     * @param text 原始文本
     * @param shouldFormat 是否需要格式化（true表示对数据单元格进行格式化，false表示保持原样）
     * @return 格式化后的文本
     */
    private String formatTableCellText(String text, boolean shouldFormat) {
        if (shouldFormat && ("/".equals(text))) {
            return "-";
        }
        return text;
    }
    
    /**
     * 格式化表格单元格文本，将"/"和空字符串转换为"-"（兼容原有方法）
     * @param text 原始文本
     * @return 格式化后的文本
     */
    private String formatTableCellText(String text) {
        return formatTableCellText(text, true);
    }

    /**
     * 遍历文档中的所有表格单元格，将空单元格填充为"-"
     * @param document Word文档对象
     */
    private void fillEmptyTableCells(WordprocessingMLPackage document) {
        try {
            MainDocumentPart mainDocumentPart = document.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = new ObjectFactory();
            
            int totalProcessedCells = 0;
            int filledCells = 0;
            
            // 遍历文档中的所有对象
            for (Object obj : docObjects) {
                if (obj instanceof Tbl) {
                    Tbl table = (Tbl) obj;
                    int[] result = fillEmptyTableCellsInTable(table, factory);
                    totalProcessedCells += result[0];
                    filledCells += result[1];
                }
            }
            
            log.info("✓ 完成空单元格填充：处理了{}个单元格，填充了{}个空单元格", totalProcessedCells, filledCells);
        } catch (Exception e) {
            log.error("填充空单元格失败", e);
        }
    }
    
    /**
     * 填充单个表格中的空单元格
     * @param table 表格对象
     * @param factory 对象工厂
     * @return int数组，[0]为处理的单元格总数，[1]为填充的空单元格数
     */
    private int[] fillEmptyTableCellsInTable(Tbl table, ObjectFactory factory) {
        int processedCells = 0;
        int filledCells = 0;
        
        try {
            List<Object> rows = table.getContent();
            
            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();
                    
                    for (Object cellObj : cells) {
                        Tc cell = null;
                        
                        if (cellObj instanceof Tc) {
                            cell = (Tc) cellObj;
                        } else if (cellObj instanceof JAXBElement) {
                            JAXBElement<?> jaxbElement = (JAXBElement<?>) cellObj;
                            if (jaxbElement.getValue() instanceof Tc) {
                                cell = (Tc) jaxbElement.getValue();
                            }
                        }
                        
                        if (cell != null) {
                            processedCells++;
                            String cellText = getCellText(cell);
                            
                            // 如果单元格为空或只包含空白字符，填充为"-"
                            if (cellText == null || cellText.trim().isEmpty()) {
                                setCellTextContent(cell, "-", factory);
                                filledCells++;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理表格单元格时发生错误", e);
        }
        
        return new int[]{processedCells, filledCells};
    }
    
    /**
     * 获取单元格的文本内容
     * @param cell 单元格对象
     * @return 单元格文本内容
     */
    private String getCellText(Tc cell) {
        try {
            StringBuilder textBuilder = new StringBuilder();
            List<Object> cellContent = cell.getContent();
            
            for (Object content : cellContent) {
                if (content instanceof P) {
                    P paragraph = (P) content;
                    String paragraphText = getParagraphTextFromCell(paragraph);
                    if (paragraphText != null) {
                        textBuilder.append(paragraphText);
                    }
                }
            }
            
            return textBuilder.toString();
        } catch (Exception e) {
            log.debug("获取单元格文本失败", e);
            return "";
        }
    }
    
    /**
     * 从单元格段落中获取文本内容
     * @param paragraph 段落对象
     * @return 段落文本内容
     */
    private String getParagraphTextFromCell(P paragraph) {
        try {
            StringBuilder textBuilder = new StringBuilder();
            List<Object> paragraphContent = paragraph.getContent();
            
            for (Object content : paragraphContent) {
                if (content instanceof R) {
                    R run = (R) content;
                    List<Object> runContent = run.getContent();
                    
                    for (Object runObj : runContent) {
                        if (runObj instanceof Text) {
                            Text text = (Text) runObj;
                            textBuilder.append(text.getValue());
                        } else if (runObj instanceof JAXBElement) {
                            JAXBElement<?> jaxbElement = (JAXBElement<?>) runObj;
                            if (jaxbElement.getValue() instanceof Text) {
                                Text text = (Text) jaxbElement.getValue();
                                textBuilder.append(text.getValue());
                            }
                        }
                    }
                }
            }
            
            return textBuilder.toString();
        } catch (Exception e) {
            log.debug("获取段落文本失败", e);
            return "";
        }
    }
    
    /**
     * 设置单元格的文本内容
     * @param cell 单元格对象
     * @param text 要设置的文本
     * @param factory 对象工厂
     */
    private void setCellTextContent(Tc cell, String text, ObjectFactory factory) {
        try {
            // 清空单元格原有内容
            cell.getContent().clear();
            
            // 创建新的段落
            P paragraph = factory.createP();
            
            // 设置段落属性为居中对齐
            PPr paragraphProperties = factory.createPPr();
            Jc justification = factory.createJc();
            justification.setVal(JcEnumeration.CENTER); // 设置居中对齐
            paragraphProperties.setJc(justification);
            paragraph.setPPr(paragraphProperties);
            
            // 创建文本运行
            R run = factory.createR();
            Text textElement = factory.createText();
            textElement.setValue(text);
            
            run.getContent().add(textElement);
            paragraph.getContent().add(run);
            cell.getContent().add(paragraph);
            
            log.debug("成功设置单元格文本（居中对齐）: {}", text);
        } catch (Exception e) {
            log.error("设置单元格文本失败", e);
        }
    }

}