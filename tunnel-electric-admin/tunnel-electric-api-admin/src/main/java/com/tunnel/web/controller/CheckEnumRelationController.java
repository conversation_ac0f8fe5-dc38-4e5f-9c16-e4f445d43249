package com.tunnel.web.controller;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.word.WordDocumentUtil;
import com.tunnel.domain.*;
import com.tunnel.domain.dto.QuestionTable;
import com.tunnel.mapper.*;
import com.tunnel.service.CheckEnumRelationService;
import com.tunnel.service.TunnelCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.docx4j.jaxb.Context;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.FooterPart;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.openpackaging.parts.relationships.RelationshipsPart;
import org.docx4j.relationships.Relationship;
import org.docx4j.wml.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBElement;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 分项设备检查设备对应关系Controller
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@RestController
@RequestMapping("/tunnel/electric/relation")
@Slf4j
public class CheckEnumRelationController extends BaseController {

    @Autowired
    private CheckEnumRelationService checkEnumRelationService;
    @Resource
    private TunnelCheckMapper tunnelCheckMapper;
    @Resource
    private CheckEnumMapper checkEnumMapper;
    @Resource
    private FacilityInfoMapper facilityInfoMapper;
    @Resource
    private TunnelInfoMapper tunnelInfoMapper;
    @Resource
    private CheckDemandMapper checkDemandMapper;
    @Resource
    private CheckEnumWordController checkEnumWordController;

    /**
     * 查询分项设备检查设备对应关系列表
     */
    @PreAuthorize("@ss.hasPermi('domain:relation:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckEnumRelation checkEnumRelation) {
        startPage();
        List<CheckEnumRelation> list = checkEnumRelationService.selectCheckEnumRelationList(checkEnumRelation);
        return getDataTable(list);
    }


    /**
     * 导出隧道养护检测日期、结果汇总表
     */
    @Log(title = "隧道养护检测日期、结果汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSummary")
    public void exportSummary(HttpServletResponse response, CheckEnumRelation checkEnumRelation, String tunnelIds) {
        // 支持多个隧道ID，以逗号分隔
        if (Objects.isNull(tunnelIds) || tunnelIds.isEmpty()) {
            // 如果tunnelIds为空，尝试从checkEnumRelation获取单个隧道ID
            if (Objects.isNull(checkEnumRelation.getTunnelId())) {
                throw new ServiceException("隧道ID不能为空");
            }
            tunnelIds = checkEnumRelation.getTunnelId().toString();
        }

        try {
            // 设置响应格式
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 获取当前年份，不硬编码
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            String fileName = URLEncoder.encode(currentYear + "年养护检测日期、结果汇总表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("汇总表");

            // 设置标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setBorderTop(BorderStyle.THIN);
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 14);
            titleStyle.setFont(titleFont);

            // 设置表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 11);
            headerStyle.setFont(headerFont);
            headerStyle.setWrapText(true); // 确保表头自动换行

            // 设置数据单元格样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setWrapText(true); // 确保数据单元格自动换行

            // 设置总计行样式
            CellStyle totalStyle = workbook.createCellStyle();
            totalStyle.setAlignment(HorizontalAlignment.CENTER);
            totalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
            totalStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            totalStyle.setBorderBottom(BorderStyle.THIN);
            totalStyle.setBorderLeft(BorderStyle.THIN);
            totalStyle.setBorderRight(BorderStyle.THIN);
            totalStyle.setBorderTop(BorderStyle.THIN);
            Font totalFont = workbook.createFont();
            totalFont.setBold(true);
            totalStyle.setFont(totalFont);
            totalStyle.setWrapText(true); // 确保总计行自动换行

            // 预定义数字格式样式，避免在循环中重复创建
            CellStyle scoreNumberStyle = workbook.createCellStyle();
            scoreNumberStyle.cloneStyleFrom(dataStyle);
            DataFormat format = workbook.createDataFormat();
            scoreNumberStyle.setDataFormat(format.getFormat("0.00"));

            // 设置分项名称样式（用于合并单元格）
            CellStyle itemNameStyle = workbook.createCellStyle();
            itemNameStyle.setAlignment(HorizontalAlignment.CENTER);
            itemNameStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            itemNameStyle.setBorderBottom(BorderStyle.THIN);
            itemNameStyle.setBorderLeft(BorderStyle.THIN);
            itemNameStyle.setBorderRight(BorderStyle.THIN);
            itemNameStyle.setBorderTop(BorderStyle.THIN);
            itemNameStyle.setWrapText(true); // 确保分项名称自动换行

            // 设置结果列样式（加粗显示）
            CellStyle resultStyle = workbook.createCellStyle();
            resultStyle.setAlignment(HorizontalAlignment.CENTER);
            resultStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            resultStyle.setBorderBottom(BorderStyle.THIN);
            resultStyle.setBorderLeft(BorderStyle.THIN);
            resultStyle.setBorderRight(BorderStyle.THIN);
            resultStyle.setBorderTop(BorderStyle.THIN);
            Font resultFont = workbook.createFont();
            resultFont.setBold(true);
            resultStyle.setFont(resultFont);
            resultStyle.setWrapText(true); // 确保结果列自动换行

            // 获取所有分部信息，以动态生成表头
            List<CheckEnum> allParts = checkEnumMapper.selectDistinctPartCodeAndName();

            // 创建分部名称映射
            Map<String, String> partCodeToNameMap = new HashMap<>();
            // 分部列索引映射，用于记录每个分部对应的列索引
            Map<String, Integer> partCodeToColumnMap = new HashMap<>();
            // 记录所有分部的代码，用于统一顺序
            List<String> allPartCodes = new ArrayList<>();

            // 初始固定列数量
            int fixedColumnCount = 8; // 0-7列是固定的：序号、标段、运营公司、路段名称、隧道名称等
            int columnIndex = fixedColumnCount;

            // 处理所有分部列
            for (CheckEnum part : allParts) {
                String partCode = part.getPartCode();
                String partName = part.getPartName();

                if (partName != null && !partName.isEmpty() && !allPartCodes.contains(partCode)) {
                    partCodeToNameMap.put(partCode, partName);
                    partCodeToColumnMap.put(partCode, columnIndex);
                    allPartCodes.add(partCode);
                    columnIndex++;
                }
            }

            // 总分和类别列索引
            int totalScoreColumnIndex = columnIndex;
            int categoryColumnIndex = columnIndex + 1;

            // 获取第一个隧道的标段信息用于标题
            String section = "";
            if (tunnelIds != null && !tunnelIds.isEmpty()) {
                Long firstTunnelId = Long.parseLong(tunnelIds.split(",")[0].trim());
                TunnelInfo firstTunnelInfo = tunnelInfoMapper.selectTunnelInfoById(firstTunnelId);
                if (firstTunnelInfo != null && firstTunnelInfo.getSection() != null) {
                    section = firstTunnelInfo.getSection();
                }
            }

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30); // 设置行高
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(currentYear + "年隧道机电设施检测结果汇总表");
            titleCell.setCellStyle(titleStyle);
            // 合并标题单元格，跨越所有列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, categoryColumnIndex));

            // 创建表头行
            Row headerRow = sheet.createRow(1);

            // 添加固定表头
            String[] fixedHeaders = {"序号", "标段", "运营公司", "路段名称", "隧道名称", "左幅长度/m", "右幅长度/m", "隧道类型"};
            for (int i = 0; i < fixedHeaders.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(fixedHeaders[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 20 * 256);
            }

            // 添加动态分部表头
            for (String partCode : allPartCodes) {
                String partName = partCodeToNameMap.get(partCode);
                int colIndex = partCodeToColumnMap.get(partCode);

                Cell cell = headerRow.createCell(colIndex);
                cell.setCellValue(partName);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(colIndex, 20 * 256);
            }

            // 添加总分和类别表头
            Cell totalScoreHeaderCell = headerRow.createCell(totalScoreColumnIndex);
            totalScoreHeaderCell.setCellValue("总得分");
            totalScoreHeaderCell.setCellStyle(headerStyle);
            sheet.setColumnWidth(totalScoreColumnIndex, 20 * 256);

            Cell categoryHeaderCell = headerRow.createCell(categoryColumnIndex);
            categoryHeaderCell.setCellValue("类别");
            categoryHeaderCell.setCellStyle(headerStyle);
            sheet.setColumnWidth(categoryColumnIndex, 20 * 256);

            // 处理多个隧道ID
            String[] tunnelIdArray = tunnelIds.split(",");
            int rowIndex = 2; // 从第三行开始添加数据行

            for (int idx = 0; idx < tunnelIdArray.length; idx++) {
                Long tunnelId = Long.parseLong(tunnelIdArray[idx].trim());
                // 根据隧道ID查询隧道信息
                TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(tunnelId);
                if (Objects.isNull(tunnelInfo)) {
                    log.warn("隧道ID {} 不存在", tunnelId);
                    continue;
                }

                // 查询检测关系数据
                CheckEnumRelation queryParam = new CheckEnumRelation();
                queryParam.setTunnelId(tunnelId);
                List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(queryParam);
                for (CheckEnumRelation enumRelation : checkEnumRelationList) {
                    if(StringUtils.isBlank(enumRelation.getName())){
                        enumRelation.setName("");
                    }
                }
                // 如果没有检测数据，跳过该隧道
                if (checkEnumRelationList.isEmpty()) {
                    log.warn("隧道ID {} 没有检测数据", tunnelId);
                    continue;
                }

                // 查询当前隧道下的所有资产
                List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(tunnelId);
                for (FacilityInfo facilityInfo : facilityInfoList) {
                    if(StringUtils.isBlank(facilityInfo.getName())){
                        facilityInfo.setName("");
                    }
                }
                // 根据partCode+itemCode进行分组
                Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream()
                        .collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));

                // 创建映射，用于存储设备信息
                Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();
                Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>(); // 按编号查找
                Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>(); // 默认分值

                // 预处理checkEnumRelationList
                Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream()
                        .collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));

                // 使用已有方法计算分数
                checkEnumRelationService.initCommonScore(checkEnumRelationList, queryParam, relationGroupMap, facilityGroupMap,
                        deviceInfoMap, codeScoreMap, defaultScoreMap,new HashMap<>(),new HashMap<>(),Lists.newArrayList());

                // 创建数据行
                Row dataRow = sheet.createRow(rowIndex++);
                dataRow.setHeightInPoints(22); // 设置数据行高

                // 序号 - 从1开始
                Cell cell0 = dataRow.createCell(0);
                cell0.setCellValue(idx + 1);
                cell0.setCellStyle(dataStyle);

                // 标段
                Cell cell1 = dataRow.createCell(1);
                cell1.setCellValue(tunnelInfo.getSection() != null ? tunnelInfo.getSection() : "");
                cell1.setCellStyle(dataStyle);

                // 运营公司
                Cell cell2 = dataRow.createCell(2);
                cell2.setCellValue(tunnelInfo.getCompanyName() != null ? tunnelInfo.getCompanyName() : "");
                cell2.setCellStyle(dataStyle);

                // 路段名称
                Cell cell3 = dataRow.createCell(3);
                cell3.setCellValue(tunnelInfo.getRoadName() != null ? tunnelInfo.getRoadName() : "");
                cell3.setCellStyle(dataStyle);

                // 隧道名称
                Cell cell4 = dataRow.createCell(4);
                cell4.setCellValue(tunnelInfo.getTunnelName() != null ? tunnelInfo.getTunnelName() : "");
                cell4.setCellStyle(dataStyle);

                // 左幅长度/m - 使用上行长度
                Cell cell5 = dataRow.createCell(5);
                cell5.setCellValue(tunnelInfo.getUpTunnelLength() != null ? tunnelInfo.getUpTunnelLength().toString() : "");
                cell5.setCellStyle(dataStyle);

                // 右幅长度/m - 使用下行长度
                Cell cell6 = dataRow.createCell(6);
                cell6.setCellValue(tunnelInfo.getDownTunnelLength() != null ? tunnelInfo.getDownTunnelLength().toString() : "");
                cell6.setCellStyle(dataStyle);

                // 隧道类型
                Cell cell7 = dataRow.createCell(7);
                cell7.setCellValue(tunnelInfo.getTunnelType() != null ? tunnelInfo.getTunnelType() : "");
                cell7.setCellStyle(dataStyle);

                // 按分部代码进行分组
                Map<String, List<CheckEnumRelation>> partGroupMap = checkEnumRelationList.stream()
                        .collect(Collectors.groupingBy(r -> r.getPartCode()));

                // 计算各系统分数
                Map<String, BigDecimal> partScores = new HashMap<>();

                for (Map.Entry<String, List<CheckEnumRelation>> entry : partGroupMap.entrySet()) {
                    String partCode = entry.getKey();
                    List<CheckEnumRelation> partData = entry.getValue();

                    // 根据分项进行分组 - 与exportDetail保持一致，使用itemCode + itemName分组
                    Map<String, List<CheckEnumRelation>> itemGroupMap = partData.stream()
                            .collect(Collectors.groupingBy(r -> r.getItemCode() + "_" + r.getItemName()));

                    // 使用与exportDetail方法相同的计算方式，确保分数一致
                    BigDecimal itemTotalScore = BigDecimal.ZERO;
                    int itemCount = 0;

                    // 获取分部名称，记录日志用
                    String partName = "";
                    if (!partData.isEmpty()) {
                        partName = partData.get(0).getPartName();
                    }

                    // 遍历并计算每个分项的分数
                    for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                        String itemKey = itemEntry.getKey();
                        List<CheckEnumRelation> itemData = itemEntry.getValue();

                        // 获取分项名称，便于日志记录
                        String itemName = "";
                        if (!itemData.isEmpty()) {
                            itemName = itemData.get(0).getItemName();
                        }

                        // 使用与Excel公式相同的计算方式
                        BigDecimal itemScore = calculateItemScore(itemData);

                        // 记录分项得分
                        log.info("隧道[{}] 分部[{}:{}] 分项[{}]得分: {}",
                                tunnelInfo.getTunnelName(), partCode, partName, itemName, itemScore);

                        // 修复计算逻辑：分项得分为"/"的不计入（即itemScore < 0），但0分需要计入分部得分计算
                        // 原来的条件只包含了分值大于0的情况，修改为分值>=0以包含0分，与exportDetail保持一致
                        if (itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                            itemTotalScore = itemTotalScore.add(itemScore);
                            itemCount++;
                        }
                    }

                    // 计算分项的平均值，即分部的总得分
                    BigDecimal partScore = BigDecimal.ZERO;

                    // 照明设施特殊处理，partCode为"2"
                    if ("2".equals(partCode)) {
                        // 找到"隧道灯具"和"洞外路灯"分项
                        BigDecimal tunnelLightScore = BigDecimal.ZERO;
                        BigDecimal outsideLightScore = BigDecimal.ZERO;
                        boolean hasTunnelLight = false;
                        boolean hasOutsideLight = false;

                        // 重新遍历，找到对应分项
                        for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                            List<CheckEnumRelation> itemData = itemEntry.getValue();
                            if (itemData.isEmpty()) continue;

                            String itemName = itemData.get(0).getItemName();
                            BigDecimal itemScore = calculateItemScore(itemData);

                            if ("隧道灯具".equals(itemName) && itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                                tunnelLightScore = itemScore;
                                hasTunnelLight = true;
                                log.info("隧道[{}] 分部[{}:{}] 分项[隧道灯具]得分(照明设施特殊计算): {}",
                                        tunnelInfo.getTunnelName(), partCode, partName, tunnelLightScore);
                            } else if ("洞外路灯".equals(itemName) && itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                                outsideLightScore = itemScore;
                                hasOutsideLight = true;
                                log.info("隧道[{}] 分部[{}:{}] 分项[洞外路灯]得分(照明设施特殊计算): {}",
                                        tunnelInfo.getTunnelName(), partCode, partName, outsideLightScore);
                            }
                        }

                        // 特殊计算照明设施分部得分：(隧道灯具得分*5+洞外路灯得分*1)/6
                        if (hasTunnelLight && hasOutsideLight) {
                            // 计算加权平均分
                            BigDecimal weightedSum = tunnelLightScore.multiply(new BigDecimal("5"))
                                    .add(outsideLightScore.multiply(new BigDecimal("1")));
                            partScore = weightedSum.divide(new BigDecimal("6"), 2, RoundingMode.HALF_EVEN);
                            log.info("隧道[{}] 分部[{}:{}]得分(特殊计算): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, partScore);
                        } else if (hasTunnelLight) {
                            // 只有隧道灯具有效，使用隧道灯具分数
                            partScore = tunnelLightScore;
                            log.info("隧道[{}] 分部[{}:{}]得分(仅使用隧道灯具分数): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, partScore);
                        } else if (hasOutsideLight) {
                            // 只有洞外路灯有效，使用洞外路灯分数
                            partScore = outsideLightScore;
                            log.info("隧道[{}] 分部[{}:{}]得分(仅使用洞外路灯分数): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, partScore);
                        } else {
                            // 两个分项都没有有效分数，使用原来的计算方式
                            if (itemCount > 0) {
                                partScore = itemTotalScore.divide(new BigDecimal(itemCount), 2, RoundingMode.HALF_EVEN);
                            }
                            log.info("隧道[{}] 分部[{}:{}]得分(常规计算): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, partScore);
                        }
                    } else {
                        // 其他分部使用常规计算
                        if (itemCount > 0) {
                            partScore = itemTotalScore.divide(new BigDecimal(itemCount), 2, RoundingMode.HALF_EVEN);
                        }
                        log.info("隧道[{}] 分部[{}:{}]得分: {}", tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    }

                    // 记录分部得分
                    partScores.put(partCode, partScore);
                }

                // 填充各分部得分
                for (String partCode : allPartCodes) {
                    Integer columnIdx = partCodeToColumnMap.get(partCode);
                    if (columnIdx != null) {
                        Cell partScoreCell = dataRow.createCell(columnIdx);
                        BigDecimal score = partScores.getOrDefault(partCode, BigDecimal.ZERO);

                        // 只有分数大于0时才显示，否则显示为空
                        if (score.compareTo(BigDecimal.ZERO) > 0) {
                            partScoreCell.setCellValue(score.doubleValue());
                        } else {
                            partScoreCell.setCellValue("/");
                        }
                        partScoreCell.setCellStyle(dataStyle);
                    }
                }

                // 计算总得分 - 使用统一计算方法
                BigDecimal totalScore = calculateTotalScore(partScores);

                // 总得分
                Cell totalScoreCell = dataRow.createCell(totalScoreColumnIndex);
                totalScoreCell.setCellValue(totalScore.doubleValue());

                // 使用预定义的数字格式样式，而不是创建新样式
                totalScoreCell.setCellStyle(scoreNumberStyle);

                // 类别
                String category = determineCategory(totalScore);
                Cell categoryCell = dataRow.createCell(categoryColumnIndex);
                categoryCell.setCellValue(category);
                categoryCell.setCellStyle(dataStyle);

                // 记录该隧道总分
                log.info("隧道[{}]总得分: {}, 类别: {}", tunnelInfo.getTunnelName(), totalScore, category);
            }

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("汇总表导出完成，共处理{}个隧道", tunnelIdArray.length);
        } catch (Exception e) {
            log.error("导出汇总表异常", e);
            throw new ServiceException("导出汇总表失败: " + e.getMessage());
        }
    }


    /**
     * 计算分项的平均得分
     * @param itemData 分项数据列表
     * @return 平均得分
     */
    private BigDecimal calculateItemScore(List<CheckEnumRelation> itemData) {
        if (itemData == null || itemData.isEmpty()) {
            return BigDecimal.ZERO;
        }
        // 按检查内容分组
        Map<String, List<CheckEnumRelation>> contentGroups = itemData.stream().collect(Collectors.groupingBy(item -> item.getCheckContent() != null ? item.getCheckContent() : "未分类"));
        BigDecimal totalWeightedScore = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;
        // 计算每个检查内容的加权得分
        for (Map.Entry<String, List<CheckEnumRelation>> entry : contentGroups.entrySet()) {
            List<CheckEnumRelation> contentItems = entry.getValue();
            // 获取权重
            BigDecimal weight = contentItems.get(0).getWeight();
            // 计算该检查内容的平均得分
            BigDecimal contentScore = BigDecimal.ZERO;
            int validItemCount = 0;
            for (CheckEnumRelation item : contentItems) {
                // 确保0分被正确计入：只有值不为空且大于等于0才参与计算
                // score为null或负值表示为"/"，这些不应该参与计算
                if (item.getScore() != null && item.getScore().compareTo(BigDecimal.ZERO) >= 0) {
                    contentScore = contentScore.add(item.getScore());
                    validItemCount++;
                }
            }
            totalWeight = totalWeight.add(weight);
            if (validItemCount > 0) {
                totalWeightedScore = totalWeightedScore.add(contentScore.multiply(weight).divide(new BigDecimal(validItemCount), 3, RoundingMode.HALF_EVEN));
            }else{
                contentScore = BigDecimal.ONE;
                totalWeightedScore = totalWeightedScore.add(contentScore.multiply(weight));
            }
        }

        // 计算最终加权平均分，与Excel公式一致使用MROUND四舍五入到0.01
        if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rawScore = totalWeightedScore.divide(totalWeight, 4, RoundingMode.HALF_EVEN).multiply(new BigDecimal(100));
            // 模拟Excel的MROUND函数，四舍五入到0.01
            return rawScore.setScale(2, RoundingMode.HALF_EVEN);
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算总得分（所有分部的加权平均分）
     * @param partScores 各分部得分
     * @return 总得分
     */
    private BigDecimal calculateTotalScore(Map<String, BigDecimal> partScores) {
        if (partScores.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;
        Map<String,BigDecimal> weightMap = new HashMap<>();
        weightMap.put("1",new BigDecimal(23));
        weightMap.put("2",new BigDecimal(18));
        weightMap.put("3",new BigDecimal(19));
        weightMap.put("4",new BigDecimal(21));
        weightMap.put("5",new BigDecimal(19));

        for (Map.Entry<String, BigDecimal> entry : partScores.entrySet()) {
            String partCode = entry.getKey();
            BigDecimal score = entry.getValue();

            // 修改：分数大于等于0的分部都参与计算，包括0分
            if (score.compareTo(BigDecimal.ZERO) >= 0) {
                BigDecimal weight = weightMap.get(partCode);
                if (weight != null) {
                    sum = sum.add(score.multiply(weight));
                    totalWeight = totalWeight.add(weight);
                }
            }
        }

        // 总权重大于0时才进行除法，否则返回0
        if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
            // 使用与Excel MROUND相同的精度
            return sum.divide(totalWeight, 2, RoundingMode.HALF_EVEN);
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据总分确定类别
     * @param totalScore 总分
     * @return 类别（1-4类）
     */
    private String determineCategory(BigDecimal totalScore) {
        if (totalScore.compareTo(new BigDecimal(90)) >= 0) {
            return "1类";
        } else if (totalScore.compareTo(new BigDecimal(80)) >= 0) {
            return "2类";
        } else if (totalScore.compareTo(new BigDecimal(70)) >= 0) {
            return "3类";
        } else {
            return "4类";
        }
    }

    /**
     * 根据等级生成对应的描述文本
     * @param category 等级类别
     * @return 描述文本
     */
    private String generateRankDescription(String category) {
        switch (category) {
            case "1类":
                return "机电设施技术状况评定为1类，应进行正常养护。";
            case "2类":
                return "机电设施技术状况评定为2类，应进行正常养护，并对损坏设备及时修复。";
            case "3类":
                return "机电设施技术状况评定为3类，宜实施专项工程，并应加强日常巡查。";
            case "4类":
                return "机电设施技术状况评定为4类，应实施专项工程，并应加强日常巡查，并采取交通管制措施。";
            default:
                return "机电设施技术状况评定为" + category + "，应进行正常养护。";
        }
    }



    /**
     * 获取分项设备检查设备对应关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:relation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkEnumRelationService.selectCheckEnumRelationById(id));
    }

    /**
     * 新增分项设备检查设备对应关系
     */
    @PreAuthorize("@ss.hasPermi('domain:relation:add')")
    @Log(title = "分项设备检查设备对应关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckEnumRelation checkEnumRelation) {
        // 确保设备名称字段不为null，如果为空则设置为空字符串
        if (checkEnumRelation.getName() == null || checkEnumRelation.getName().trim().isEmpty()) {
            checkEnumRelation.setName("");
        }
        return toAjax(checkEnumRelationService.insertCheckEnumRelation(checkEnumRelation));
    }

    /**
     * 修改分项设备检查设备对应关系
     */
    @PreAuthorize("@ss.hasPermi('domain:relation:edit')")
    @Log(title = "分项设备检查设备对应关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckEnumRelation checkEnumRelation) {
        // 确保设备名称字段不为null，如果为空则设置为空字符串
        if (checkEnumRelation.getName() == null || checkEnumRelation.getName().trim().isEmpty()) {
            checkEnumRelation.setName("");
        }
        return toAjax(checkEnumRelationService.updateCheckEnumRelation(checkEnumRelation));
    }

    /**
     * 删除分项设备检查设备对应关系
     */
    @PreAuthorize("@ss.hasPermi('domain:relation:remove')")
    @Log(title = "分项设备检查设备对应关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkEnumRelationService.deleteCheckEnumRelationByIds(ids));
    }

    /**
     * 检查两个编号是否连续
     */
    private boolean isConsecutive(String code1, String code2) {
        try {
            int num1 = Integer.parseInt(code1.replaceAll("[^0-9]", ""));
            int num2 = Integer.parseInt(code2.replaceAll("[^0-9]", ""));
            return num2 - num1 == 1;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 获取隧道分部评分详情
     */
    @GetMapping("/getTunnelScoreDetail")
    public AjaxResult getTunnelScoreDetail(Long tunnelId) {
        // 创建StopWatch监控性能
        org.springframework.util.StopWatch stopWatch = new org.springframework.util.StopWatch("getTunnelScoreDetail");

        try {
            stopWatch.start("参数验证");
            if (Objects.isNull(tunnelId)) {
                throw new ServiceException("隧道ID不能为空");
            }
            stopWatch.stop();

            stopWatch.start("查询隧道信息");
            // 根据隧道ID查询隧道信息
            TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(tunnelId);
            if (Objects.isNull(tunnelInfo)) {
                throw new ServiceException("隧道不存在");
            }
            stopWatch.stop();

            stopWatch.start("查询检测关系数据");
            // 查询检测关系数据
            CheckEnumRelation queryParam = new CheckEnumRelation();
            queryParam.setTunnelId(tunnelId);
            List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(queryParam);
            for (CheckEnumRelation enumRelation : checkEnumRelationList) {
                if(StringUtils.isBlank(enumRelation.getName())){
                    enumRelation.setName("");
                }
            }
            stopWatch.stop();

            // 如果没有检测数据，返回空结果
            if (checkEnumRelationList.isEmpty()) {
                log.warn("隧道ID {} 没有检测数据", tunnelId);
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("tunnelInfo", tunnelInfo);
                emptyResult.put("partScores", new ArrayList<>());
                emptyResult.put("totalScore", 0);
                emptyResult.put("category", "无评级");
                return AjaxResult.success(emptyResult);
            }

            stopWatch.start("查询设施信息");
            // 查询当前隧道下的所有资产
            List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(tunnelId);
            for (FacilityInfo facilityInfo : facilityInfoList) {
                if(StringUtils.isBlank(facilityInfo.getName())){
                    facilityInfo.setName("");
                }
            }
            stopWatch.stop();

            stopWatch.start("数据预处理");
            // 根据partCode+itemCode进行分组
            Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream()
                    .collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));

            // 创建映射，用于存储设备信息
            Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();
            Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>(); // 按编号查找
            Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>(); // 默认分值

            // 预处理checkEnumRelationList
            Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream()
                    .collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));
            stopWatch.stop();

            stopWatch.start("计算分数-优化版");
            // 使用优化版本的分数计算方法，跳过复杂的Word报告相关逻辑，只计算基础分数
            optimizedScoreCalculation(checkEnumRelationList, queryParam, relationGroupMap, facilityGroupMap,
                    deviceInfoMap, codeScoreMap, defaultScoreMap);
            stopWatch.stop();

            stopWatch.start("查询分部信息");
            // 获取所有分部信息
            List<CheckEnum> allParts = checkEnumMapper.selectDistinctPartCodeAndName();

            // 创建分部名称映射
            Map<String, String> partCodeToNameMap = new HashMap<>();
            List<String> allPartCodes = new ArrayList<>();

            // 处理所有分部
            for (CheckEnum part : allParts) {
                String partCode = part.getPartCode();
                String partName = part.getPartName();

                if (partName != null && !partName.isEmpty() && !allPartCodes.contains(partCode)) {
                    partCodeToNameMap.put(partCode, partName);
                    allPartCodes.add(partCode);
                }
            }
            stopWatch.stop();

            stopWatch.start("分部数据分组");
            // 按分部代码分组
            Map<String, List<CheckEnumRelation>> partRelationsMap = new HashMap<>();
            for (CheckEnumRelation relation : checkEnumRelationList) {
                String partCode = relation.getPartCode();
                if (partCode != null && !partCode.isEmpty()) {
                    partRelationsMap.computeIfAbsent(partCode, k -> new ArrayList<>()).add(relation);
                }
            }
            stopWatch.stop();

            stopWatch.start("计算分部评分");
            // 计算每个分部的评分 - 使用与exportSummary相同的方法
            Map<String, BigDecimal> partScores = new HashMap<>();
            for (String partCode : allPartCodes) {
                List<CheckEnumRelation> partData = partRelationsMap.getOrDefault(partCode, new ArrayList<>());
                if (!partData.isEmpty()) {
                    // 获取分部名称，便于日志记录
                    String partName = "";
                    if (!partData.isEmpty()) {
                        partName = partData.get(0).getPartName();
                    }

                    // 照明设施特殊处理，partCode为"2"
                    if ("2".equals(partCode)) {
                        // 根据分项进行分组
                        Map<String, List<CheckEnumRelation>> itemGroupMap = partData.stream()
                                .collect(Collectors.groupingBy(r -> r.getItemCode()));

                        // 找到"隧道灯具"和"洞外路灯"分项
                        BigDecimal tunnelLightScore = BigDecimal.ZERO;
                        BigDecimal outsideLightScore = BigDecimal.ZERO;

                        // 遍历各个分项，找到对应分项并计算分数
                        for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                            List<CheckEnumRelation> itemData = itemEntry.getValue();
                            if (itemData.isEmpty()) continue;

                            String itemName = itemData.get(0).getItemName();
                            BigDecimal itemScore = this.calculateItemScore(itemData);

                            if ("隧道灯具".equals(itemName)) {
                                tunnelLightScore = itemScore;
                                log.info("隧道[{}] 分部[{}:{}] 分项[隧道灯具]得分: {}",
                                        tunnelInfo.getTunnelName(), partCode, partName, tunnelLightScore);
                            } else if ("洞外路灯".equals(itemName)) {
                                outsideLightScore = itemScore;
                                log.info("隧道[{}] 分部[{}:{}] 分项[洞外路灯]得分: {}",
                                        tunnelInfo.getTunnelName(), partCode, partName, outsideLightScore);
                            }
                        }

                        // 特殊计算照明设施分部得分：(隧道灯具得分*5+洞外路灯得分*1)/6
                        BigDecimal partScore = BigDecimal.ZERO;

                        // 只有两个分项的分数都是有效分数才计算
                        if (tunnelLightScore.compareTo(BigDecimal.ZERO) >= 0 &&
                                outsideLightScore.compareTo(BigDecimal.ZERO) >= 0) {
                            // 计算加权平均分
                            BigDecimal weightedSum = tunnelLightScore.multiply(new BigDecimal("5"))
                                    .add(outsideLightScore.multiply(new BigDecimal("1")));
                            partScore = weightedSum.divide(new BigDecimal("6"), 4, RoundingMode.HALF_EVEN);
                        } else if (tunnelLightScore.compareTo(BigDecimal.ZERO) >= 0) {
                            // 只有隧道灯具有效，使用隧道灯具分数
                            partScore = tunnelLightScore;
                        } else if (outsideLightScore.compareTo(BigDecimal.ZERO) >= 0) {
                            // 只有洞外路灯有效，使用洞外路灯分数
                            partScore = outsideLightScore;
                        }

                        // 记录照明设施分部得分
                        partScores.put(partCode, partScore);
                        log.info("隧道[{}] 分部[{}:{}]得分: {} (特殊计算)",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else {
                        // 其他分部的常规计算方式
                        // 根据分项进行分组
                        Map<String, List<CheckEnumRelation>> itemGroupMap = partData.stream()
                                .collect(Collectors.groupingBy(r -> r.getItemCode()));

                        BigDecimal itemTotalScore = BigDecimal.ZERO;
                        int itemCount = 0;

                        for (Map.Entry<String, List<CheckEnumRelation>> partEntry : itemGroupMap.entrySet()) {
                            List<CheckEnumRelation> itemData = partEntry.getValue();
                            // 计算各个分项的值
                            BigDecimal itemScore = this.calculateItemScore(itemData);

                            // 获取分项名称，便于日志记录
                            String itemName = "";
                            if (!itemData.isEmpty()) {
                                itemName = itemData.get(0).getItemName();
                            }

                            log.info("隧道[{}] 分部[{}:{}] 分项[{}]得分: {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, itemName, itemScore);

                            // 将分项的分数进行累加
                            if (itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                                itemTotalScore = itemTotalScore.add(itemScore);
                                itemCount++;
                            }
                        }

                        // 计算分项的平均值，即分部的总得分
                        BigDecimal partScore = BigDecimal.ZERO;
                        if (itemCount > 0) {
                            partScore = itemTotalScore.divide(new BigDecimal(itemCount), 4, RoundingMode.HALF_EVEN);
                        }

                        // 记录分部得分
                        partScores.put(partCode, partScore);
                        log.info("隧道[{}] 分部[{}:{}]得分: {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    }
                } else {
                    partScores.put(partCode, new BigDecimal(-1));
                }
            }
            stopWatch.stop();

            stopWatch.start("计算总分和等级");
            // 获取总评分 - 应用与exportSummary相同的加权算法
            BigDecimal totalScore = calculateTotalScore(partScores);

            // 确定评定等级
            String category = determineCategory(totalScore);
            stopWatch.stop();

            stopWatch.start("构建返回结果");
            // 构建结果对象
            Map<String, Object> result = new HashMap<>();
            result.put("tunnelInfo", tunnelInfo);

            List<Map<String, Object>> partScoreList = new ArrayList<>();
            for (String partCode : allPartCodes) {
                if(partScores.getOrDefault(partCode, BigDecimal.ZERO).compareTo(new BigDecimal(-1))==0){
                    continue;
                }
                Map<String, Object> partScoreMap = new HashMap<>();
                partScoreMap.put("partCode", partCode);
                partScoreMap.put("partName", partCodeToNameMap.get(partCode));
                partScoreMap.put("score", partScores.getOrDefault(partCode, BigDecimal.ZERO));
                partScoreList.add(partScoreMap);
            }

            result.put("partScores", partScoreList);
            result.put("totalScore", totalScore);
            result.put("category", category);
            stopWatch.stop();

            // 打印性能统计信息
            log.info("getTunnelScoreDetail方法性能统计:\n{}", stopWatch.prettyPrint());
            log.info("getTunnelScoreDetail总耗时: {}ms", stopWatch.getTotalTimeMillis());

            return AjaxResult.success(result);
        } catch (Exception e) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            log.error("getTunnelScoreDetail方法执行异常，当前耗时统计:\n{}", stopWatch.prettyPrint());
            throw e;
        }
    }

    /**
     * 优化版本的分数计算方法，专门用于getTunnelScoreDetail接口
     * 跳过复杂的Word报告生成相关逻辑，只计算基础分数
     */
    private void optimizedScoreCalculation(List<CheckEnumRelation> checkEnumRelationList,
                                           CheckEnumRelation checkEnumRelation,
                                           Map<String, List<CheckEnumRelation>> relationGroupMap,
                                           Map<String, List<FacilityInfo>> facilityGroupMap,
                                           Map<String, Map<String, String>> deviceInfoMap,
                                           Map<String, CheckEnumRelation> codeScoreMap,
                                           Map<String, CheckEnumRelation> defaultScoreMap) {
        try {
            // 简化版本：只处理基础的设备信息映射，跳过复杂的Word报告相关逻辑
            for (Map.Entry<String, List<CheckEnumRelation>> entry : relationGroupMap.entrySet()) {
                String key = entry.getKey();
                List<CheckEnumRelation> relations = entry.getValue();
                List<FacilityInfo> facilities = facilityGroupMap.get(key);

                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(facilities)) {
                    facilities.sort(Comparator.comparing(FacilityInfo::getLocation).thenComparing(FacilityInfo::getCode));

                    Set<String> uniqueFacilityKeys = new HashSet<>();
                    List<FacilityInfo> uniqueFacilities = new ArrayList<>();

                    for (FacilityInfo facility : facilities) {
                        String facilityKey = facility.getLocation() + "_" + facility.getCode();
                        if (uniqueFacilityKeys.add(facilityKey)) {
                            uniqueFacilities.add(facility);
                        }
                    }

                    facilities = uniqueFacilities;

                    for (int i = 0; i < facilities.size(); i++) {
                        FacilityInfo facility = facilities.get(i);
                        for (CheckEnumRelation relation : relations) {
                            String codeValue = (i + 1) + "#";
                            String mapKey = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent() + "_" + codeValue;
                            Map<String, String> deviceInfo = new HashMap<>();
                            deviceInfo.put("locationName", facility.getLocationName() != null ? facility.getLocationName() : "");
                            deviceInfo.put("deviceName", facility.getName() != null ? facility.getName() : "");
                            deviceInfo.put("deviceCode", codeValue);
                            deviceInfo.put("facilityId", facility.getId() != null ? facility.getId().toString() : "");
                            deviceInfo.put("location", facility.getLocation() != null ? facility.getLocation() : "");
                            deviceInfoMap.put(mapKey, deviceInfo);
                        }
                    }
                }
            }

            // 构建查找映射
            for (CheckEnumRelation relation : checkEnumRelationList) {
                if (relation.getCode() != null) {
                    String codeKey = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent() + "_" + relation.getCode();
                    codeScoreMap.put(codeKey, relation);
                }

                if (relation.getName() == null || relation.getName().trim().isEmpty()) {
                    String defaultKey = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent();
                    defaultScoreMap.put(defaultKey, relation);
                }
            }

            // 简化版本：只查询一次TunnelCheck数据，避免在循环中重复查询
            TunnelCheck tunnelCheck = new TunnelCheck();
            tunnelCheck.setTunnelId(checkEnumRelation.getTunnelId());
            List<TunnelCheck> checkList = tunnelCheckMapper.selectTunnelCheckList(tunnelCheck);

            // 预处理TunnelCheck数据，避免重复计算
            for (TunnelCheck check : checkList) {
                if(StringUtils.isBlank(check.getName())){
                    check.setName("");
                }
            }

            // 创建扣分映射表
            Map<String, BigDecimal> namedDeductionMap = new HashMap<>();
            Map<String, BigDecimal> emptyNamedDeductionMap = new HashMap<>();
            Map<String, BigDecimal> rateMap = new HashMap<>();

            for (TunnelCheck check : checkList) {
                String namedKey = check.getPartCode() + "_" + check.getItemCode() + "_" + check.getCheckContent()+ "_" + check.getLocation() +"_"+ check.getCode()+ "_" + check.getName();
                if(namedDeductionMap.containsKey(namedKey)){
                    namedDeductionMap.put(namedKey, check.getScore().add(namedDeductionMap.get(namedKey)));
                }else{
                    namedDeductionMap.put(namedKey, check.getScore());
                }

                String emptyNamedKey = check.getPartCode() + "_" + check.getItemCode() + "_" + check.getCheckContent()+ "_" + check.getLocation() +"_"+ check.getCode();
                if(emptyNamedDeductionMap.containsKey(emptyNamedKey)){
                    emptyNamedDeductionMap.put(emptyNamedKey, check.getScore().add(emptyNamedDeductionMap.get(emptyNamedKey)));
                }else{
                    emptyNamedDeductionMap.put(emptyNamedKey, check.getScore());
                }

                if(Objects.nonNull(check.getRate())){
                    String tunnelLightKey = check.getPartCode() + "_" + check.getItemCode() + "_" + check.getCheckContent();
                    rateMap.put(tunnelLightKey, check.getRate());
                }
            }

            // 计算有名称的关系得分
            List<CheckEnumRelation> namedCheckEnumRelationList = checkEnumRelationList.stream()
                    .filter(v->StringUtils.isNotBlank(v.getName())).collect(Collectors.toList());

            for (CheckEnumRelation relation : namedCheckEnumRelationList) {
                String key = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent() + "_" + relation.getLocation() +"_"+ relation.getCode()+ "_" + relation.getName();
                BigDecimal originalScore = relation.getScore();

                if (originalScore.compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }

                BigDecimal deduction = namedDeductionMap.getOrDefault(key, BigDecimal.ZERO);
                String rateKey = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent();

                if(rateMap.containsKey(rateKey)){
                    relation.setScore(rateMap.get(rateKey));
                }else{
                    BigDecimal finalScore = originalScore.subtract(deduction);
                    if (finalScore.compareTo(BigDecimal.ZERO) < 0) {
                        finalScore = BigDecimal.ZERO;
                    }
                    relation.setScore(finalScore);
                }
            }

            // 计算无名称的关系得分
            List<CheckEnumRelation> emptyNamedCheckEnumRelationList = checkEnumRelationList.stream()
                    .filter(v->StringUtils.isBlank(v.getName())).collect(Collectors.toList());

            for (CheckEnumRelation relation : emptyNamedCheckEnumRelationList) {
                String key = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent() + "_" + relation.getLocation() +"_"+ relation.getCode();
                BigDecimal originalScore = relation.getScore();

                if (originalScore.compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }

                BigDecimal deduction = emptyNamedDeductionMap.getOrDefault(key, BigDecimal.ZERO);
                String rateKey = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent();

                if(rateMap.containsKey(rateKey)){
                    relation.setScore(rateMap.get(rateKey));
                }else{
                    BigDecimal finalScore = originalScore.subtract(deduction);
                    if (finalScore.compareTo(BigDecimal.ZERO) < 0) {
                        finalScore = BigDecimal.ZERO;
                    }
                    relation.setScore(finalScore);
                }
            }

            // 处理focus=1且分数为0的设备逻辑
            Map<String, List<CheckEnumRelation>> deviceGroups = new HashMap<>();
            for (CheckEnumRelation relation : checkEnumRelationList) {
                if (relation.getCode() != null) {
                    String location = relation.getLocation() != null ? relation.getLocation() : "";
                    String name = relation.getName() != null ? relation.getName() : "";
                    String deviceKey = relation.getPartCode() + "_" + relation.getItemCode()  + "_" + location + "_" + relation.getCode() + "_" + name;
                    deviceGroups.computeIfAbsent(deviceKey, k -> new ArrayList<>()).add(relation);
                }
            }

            for (Map.Entry<String, List<CheckEnumRelation>> entry : deviceGroups.entrySet()) {
                List<CheckEnumRelation> deviceItems = entry.getValue();
                boolean hasZeroScoreFocusItem = false;

                for (CheckEnumRelation item : deviceItems) {
                    if (item.getScore() != null && BigDecimal.ZERO.compareTo(item.getScore()) == 0
                            && item.getFcous() != null && item.getFcous() == 1) {
                        hasZeroScoreFocusItem = true;
                        break;
                    }
                }

                if (hasZeroScoreFocusItem) {
                    for (CheckEnumRelation item : deviceItems) {
                        if (item.getScore() != null && item.getScore().compareTo(BigDecimal.ZERO) > 0) {
                            item.setScore(BigDecimal.ZERO);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("优化版分数计算失败", e);
            // 如果优化版本失败，回退到原始方法
            checkEnumRelationService.initCommonScore(checkEnumRelationList, checkEnumRelation, relationGroupMap, facilityGroupMap,
                    deviceInfoMap, codeScoreMap, defaultScoreMap, new HashMap<>(), new HashMap<>(), Lists.newArrayList());
        }
    }

    /**
     * 导出隧道分项检测详情表
     */
    @Log(title = "隧道分项检测详情表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetail")
    public void exportDetail(HttpServletResponse response, CheckEnumRelation checkEnumRelation) {
        // 隧道ID不能为空
        if (Objects.isNull(checkEnumRelation.getTunnelId())) {
            throw new ServiceException("隧道ID不能为空");
        }

        Long tunnelId = checkEnumRelation.getTunnelId();

        try {
            // 设置响应格式
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 获取当前年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            String fileName = URLEncoder.encode(currentYear + "年隧道机电设施检查结果汇总表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("隧道机电设施检查结果汇总表");

            // 设置标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setBorderTop(BorderStyle.THIN);
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 14);
            titleStyle.setFont(titleFont);

            // 设置报表头部标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30); // 增加标题行高
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(currentYear + "年SS路段XX隧道定期检查报告");
            titleCell.setCellStyle(titleStyle);
            CellRangeAddress titleRegion = new CellRangeAddress(0, 0, 0, 4);
            sheet.addMergedRegion(titleRegion);
            applyBorderToMergedRegion(sheet, titleRegion, workbook);

            // 创建"附表1、隧道机电设施检查结果汇总表"标题
            Row subTitleRow = sheet.createRow(1);
            subTitleRow.setHeightInPoints(25); // 增加副标题行高
            Cell subTitleCell = subTitleRow.createCell(0);
            subTitleCell.setCellValue("附表1、隧道机电设施检查结果汇总表");
            CellStyle subTitleStyle = workbook.createCellStyle();
            subTitleStyle.setAlignment(HorizontalAlignment.LEFT);
            subTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            subTitleStyle.setBorderBottom(BorderStyle.THIN);
            subTitleStyle.setBorderLeft(BorderStyle.THIN);
            subTitleStyle.setBorderRight(BorderStyle.THIN);
            subTitleStyle.setBorderTop(BorderStyle.THIN);
            Font subTitleFont = workbook.createFont();
            subTitleFont.setBold(true);
            subTitleFont.setFontHeightInPoints((short) 12);
            subTitleStyle.setFont(subTitleFont);
            subTitleCell.setCellStyle(subTitleStyle);
            CellRangeAddress subTitleRegion = new CellRangeAddress(1, 1, 0, 4);
            sheet.addMergedRegion(subTitleRegion);
            applyBorderToMergedRegion(sheet, subTitleRegion, workbook);

            // 设置表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 设置数据单元格样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);

            // 设置表头
            Row headerRow = sheet.createRow(2);
            String[] headers = {"分部设施名称", "分部设施检测\n项目得分", "分部设施技术\n状况等级", "分项设施名称", "分项设施检测\n项目得分"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                // 设置列宽，适应中文
                if (i == 0 || i == 3) {
                    sheet.setColumnWidth(i, 30 * 256); // 名称列宽增加
                } else if (i == 1 || i == 4) {
                    sheet.setColumnWidth(i, 24 * 256); // 得分列宽度更大
                } else if (i == 2) {
                    sheet.setColumnWidth(i, 25 * 256); // 技术状况等级列更宽
                }
            }

            // 设置行高
            headerRow.setHeightInPoints(45); // 表头行高增加

            // 设置数字格式 (显示两位小数)
            CellStyle scoreStyle = workbook.createCellStyle();
            scoreStyle.cloneStyleFrom(dataStyle);
            DataFormat format = workbook.createDataFormat();
            scoreStyle.setDataFormat(format.getFormat("0.00"));

            // 查询隧道信息
            TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(tunnelId);
            if (Objects.isNull(tunnelInfo)) {
                log.warn("隧道ID {} 不存在", tunnelId);
                throw new ServiceException("隧道不存在");
            }

            // 获取隧道信息，用于标题
            String tunnelName = tunnelInfo.getTunnelName() != null ? tunnelInfo.getTunnelName() : "XX";
            String roadName = tunnelInfo.getRoadName() != null ? tunnelInfo.getRoadName() : "SS";

            // 更新标题为实际隧道名称
            titleCell.setCellValue(currentYear + "年" + roadName + "路段" + tunnelName + "隧道定期检查报告");

            // 查询检测关系数据
            CheckEnumRelation queryParam = new CheckEnumRelation();
            queryParam.setTunnelId(tunnelId);
            List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(queryParam);
            for (CheckEnumRelation enumRelation : checkEnumRelationList) {
                if(StringUtils.isBlank(enumRelation.getName())){
                    enumRelation.setName("");
                }
            }

            // 如果没有检测数据，提示错误
            if (checkEnumRelationList.isEmpty()) {
                log.warn("隧道ID {} 没有检测数据", tunnelId);
                throw new ServiceException("该隧道没有检测数据");
            }

            // 查询当前隧道下的所有资产
            List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(tunnelId);
            for (FacilityInfo facilityInfo : facilityInfoList) {
                if(StringUtils.isBlank(facilityInfo.getName())){
                    facilityInfo.setName("");
                }
            }

            // 根据partCode+itemCode进行分组
            Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream()
                    .collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));

            // 创建映射，用于存储设备信息
            Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();
            Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>(); // 按编号查找
            Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>(); // 默认分值

            // 预处理checkEnumRelationList
            Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream()
                    .collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));

            // 使用已有方法计算分数
            checkEnumRelationService.initCommonScore(checkEnumRelationList, queryParam, relationGroupMap, facilityGroupMap,
                    deviceInfoMap, codeScoreMap, defaultScoreMap,new HashMap<>(),new HashMap<>(),Lists.newArrayList());

            // 按分部代码分组
            Map<String, List<CheckEnumRelation>> partGroupMap = checkEnumRelationList.stream()
                    .collect(Collectors.groupingBy(CheckEnumRelation::getPartCode));

            // 获取所有分部信息并排序
            List<String> partCodes = new ArrayList<>(partGroupMap.keySet());
            Collections.sort(partCodes);

            int rowIndex = 3; // 从第4行开始填充数据

            // 遍历每个分部
            for (String partCode : partCodes) {
                List<CheckEnumRelation> partRelations = partGroupMap.get(partCode);
                if (partRelations == null || partRelations.isEmpty()) {
                    continue;
                }

                // 获取分部名称
                String partName = partRelations.get(0).getPartName();
                if (partName == null) {
                    partName = "未命名分部";
                }

                // 根据分项进行分组
                Map<String, List<CheckEnumRelation>> itemGroupMap = partRelations.stream()
                        .collect(Collectors.groupingBy(r -> r.getItemCode() + "_" + r.getItemName()));

                // 使用与/export方法相同的Excel公式计算逻辑计算分部平均分
                BigDecimal itemTotalScore = BigDecimal.ZERO;
                int itemCount = 0;

                // 记录每个分项的分数，用于记录日志
                Map<String, BigDecimal> itemScores = new HashMap<>();

                for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                    String itemKey = itemEntry.getKey();
                    List<CheckEnumRelation> itemData = itemEntry.getValue();

                    // 获取分项名称
                    String itemName = "";
                    if (!itemData.isEmpty()) {
                        itemName = itemData.get(0).getItemName();
                    }

                    // 计算各个分项的值 - 与Excel公式相同的计算方式
                    BigDecimal itemScore = calculateItemScore(itemData);

                    // 修复计算逻辑：分项得分为"/"的不计入（即itemScore < 0），但0分需要计入分部得分计算
                    // 原来的条件只包含了分值大于0的情况，修改为分值>=0以包含0分
                    if (itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                        itemTotalScore = itemTotalScore.add(itemScore);
                        itemCount++;
                    }

                    // 记录分项分数
                    itemScores.put(itemKey, itemScore);
                    log.info("隧道[{}] 分部[{}:{}] 分项[{}]得分: {}",
                            tunnelInfo.getTunnelName(), partCode, partName, itemName, itemScore);
                }

                // 计算分部得分 - 与export方法保持一致的计算逻辑
                BigDecimal partScore = BigDecimal.ZERO;

                // 照明设施特殊处理，partCode为"2"
                if ("2".equals(partCode)) {
                    // 找到"隧道灯具"和"洞外路灯"分项
                    BigDecimal tunnelLightScore = BigDecimal.ZERO;
                    BigDecimal outsideLightScore = BigDecimal.ZERO;
                    boolean hasTunnelLight = false;
                    boolean hasOutsideLight = false;

                    // 遍历找到对应分项的分数
                    for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                        List<CheckEnumRelation> itemData = itemEntry.getValue();
                        if (itemData.isEmpty()) continue;

                        String itemName = itemData.get(0).getItemName();
                        BigDecimal itemScore = calculateItemScore(itemData);

                        if ("隧道灯具".equals(itemName) && itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                            tunnelLightScore = itemScore;
                            hasTunnelLight = true;
                            log.info("隧道[{}] 分部[{}:{}] 分项[隧道灯具]得分(照明设施特殊计算): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, tunnelLightScore);
                        } else if ("洞外路灯".equals(itemName) && itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                            outsideLightScore = itemScore;
                            hasOutsideLight = true;
                            log.info("隧道[{}] 分部[{}:{}] 分项[洞外路灯]得分(照明设施特殊计算): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, outsideLightScore);
                        }
                    }

                    // 特殊计算照明设施分部得分：(隧道灯具得分*5+洞外路灯得分*1)/6
                    if (hasTunnelLight && hasOutsideLight) {
                        // 计算加权平均分
                        BigDecimal weightedSum = tunnelLightScore.multiply(new BigDecimal("5"))
                                .add(outsideLightScore.multiply(new BigDecimal("1")));
                        partScore = weightedSum.divide(new BigDecimal("6"), 2, RoundingMode.HALF_EVEN);
                        log.info("隧道[{}] 分部[{}:{}]得分(特殊计算): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else if (hasTunnelLight) {
                        // 只有隧道灯具有效，使用隧道灯具分数
                        partScore = tunnelLightScore;
                        log.info("隧道[{}] 分部[{}:{}]得分(仅使用隧道灯具分数): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else if (hasOutsideLight) {
                        // 只有洞外路灯有效，使用洞外路灯分数
                        partScore = outsideLightScore;
                        log.info("隧道[{}] 分部[{}:{}]得分(仅使用洞外路灯分数): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else {
                        // 两个分项都没有有效分数，分部得分为0
                        partScore = BigDecimal.ZERO;
                        log.info("隧道[{}] 分部[{}:{}]得分(无有效分数): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    }
                } else {
                    // 其他分部正常计算平均分
                    if (itemCount > 0) {
                        partScore = itemTotalScore.divide(new BigDecimal(itemCount), 2, RoundingMode.HALF_EVEN);
                        log.info("隧道[{}] 分部[{}:{}]得分: {}", tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    }
                }

                // 确定评定等级
                String category = determineCategory(partScore);

                // 获取分项列表并排序
                List<String> itemKeys = new ArrayList<>(itemGroupMap.keySet());
                Collections.sort(itemKeys);

                // 记录第一行以便后面合并单元格
                int partStartRowIndex = rowIndex;

                // 遍历每个分项填充数据
                for (int i = 0; i < itemKeys.size(); i++) {
                    String itemKey = itemKeys.get(i);
                    List<CheckEnumRelation> itemRelations = itemGroupMap.get(itemKey);

                    if (itemRelations == null || itemRelations.isEmpty()) {
                        continue;
                    }

                    // 获取分项名称
                    String itemName = itemRelations.get(0).getItemName();
                    if (itemName == null) {
                        itemName = "未命名分项";
                    }

                    // 计算分项得分
                    BigDecimal itemScore = calculateItemScore(itemRelations);

                    Row dataRow = sheet.createRow(rowIndex);
                    dataRow.setHeightInPoints(22); // 设置数据行高

                    // 只在第一行填写分部信息
                    if (i == 0) {
                        // 分部名称
                        Cell cell1 = dataRow.createCell(0);
                        cell1.setCellValue(partName);
                        cell1.setCellStyle(dataStyle);

                        // 分部得分
                        Cell cell2 = dataRow.createCell(1);
                        if (partScore.compareTo(BigDecimal.ZERO) < 0) {
                            // 如果是负值，使用"/"表示
                            cell2.setCellValue("/");
                            cell2.setCellStyle(dataStyle);
                        } else {
                            cell2.setCellValue(partScore.doubleValue());
                            cell2.setCellStyle(scoreStyle);
                        }

                        // 技术状况等级
                        Cell cell3 = dataRow.createCell(2);
                        if (partScore.compareTo(BigDecimal.ZERO) < 0) {
                            // 如果分数是负值，状态等级显示为"/"
                            cell3.setCellValue("/");
                        } else {
                            cell3.setCellValue(category);
                        }
                        cell3.setCellStyle(dataStyle);
                    }

                    // 分项名称
                    Cell cell4 = dataRow.createCell(3);
                    cell4.setCellValue(itemName);
                    cell4.setCellStyle(dataStyle);

                    // 分项得分
                    Cell cell5 = dataRow.createCell(4);
                    if (itemScore.compareTo(BigDecimal.ZERO) < 0) {
                        // 如果是负值，使用"/"表示
                        cell5.setCellValue("/");
                        cell5.setCellStyle(dataStyle);
                    } else {
                        cell5.setCellValue(itemScore.doubleValue());
                        cell5.setCellStyle(scoreStyle);
                    }

                    rowIndex++;
                }

                // 如果该分部有多个分项，则合并分部单元格
                if (itemKeys.size() > 1) {
                    // 合并区域
                    CellRangeAddress regionCol1 = new CellRangeAddress(partStartRowIndex, rowIndex - 1, 0, 0); // 合并分部名称
                    CellRangeAddress regionCol2 = new CellRangeAddress(partStartRowIndex, rowIndex - 1, 1, 1); // 合并分部得分
                    CellRangeAddress regionCol3 = new CellRangeAddress(partStartRowIndex, rowIndex - 1, 2, 2); // 合并技术状况等级

                    // 添加合并区域
                    sheet.addMergedRegion(regionCol1);
                    sheet.addMergedRegion(regionCol2);
                    sheet.addMergedRegion(regionCol3);

                    // 对合并的单元格应用边框
                    applyBorderToMergedRegion(sheet, regionCol1, workbook);
                    applyBorderToMergedRegion(sheet, regionCol2, workbook);
                    applyBorderToMergedRegion(sheet, regionCol3, workbook);
                }
            }

            // 调整列宽 - 不使用自动调整，保持固定的较大宽度
            /*
            for (int i = 0; i < 5; i++) {
                sheet.autoSizeColumn(i);
                // 确保最小宽度
                int width = sheet.getColumnWidth(i);
                if (width < 15 * 256) {
                    sheet.setColumnWidth(i, 15 * 256);
                }
            }
            */

            // 写入响应
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("隧道分项检测详情表导出完成，隧道ID: {}", tunnelId);
        } catch (Exception e) {
            log.error("导出隧道分项检测详情表异常", e);
            throw new ServiceException("导出隧道分项检测详情表失败: " + e.getMessage());
        }
    }

    /**
     * 为合并区域的单元格添加边框
     * @param sheet 工作表
     * @param region 合并区域
     * @param workbook 工作簿
     */
    private void applyBorderToMergedRegion(Sheet sheet, CellRangeAddress region, Workbook workbook) {
        BorderStyle borderStyle = BorderStyle.THIN;
        // 创建边框样式
        RegionUtil.setBorderBottom(borderStyle, region, sheet);
        RegionUtil.setBorderLeft(borderStyle, region, sheet);
        RegionUtil.setBorderRight(borderStyle, region, sheet);
        RegionUtil.setBorderTop(borderStyle, region, sheet);

        // 为合并区域内的每个单元格应用边框样式
        for (int rowNum = region.getFirstRow(); rowNum <= region.getLastRow(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                row = sheet.createRow(rowNum);
            }

            for (int colNum = region.getFirstColumn(); colNum <= region.getLastColumn(); colNum++) {
                Cell cell = row.getCell(colNum);
                if (cell == null) {
                    cell = row.createCell(colNum);
                }

                CellStyle cellStyle = cell.getCellStyle();
                if (cellStyle == null) {
                    cellStyle = workbook.createCellStyle();
                }

                cellStyle.setBorderBottom(borderStyle);
                cellStyle.setBorderLeft(borderStyle);
                cellStyle.setBorderRight(borderStyle);
                cellStyle.setBorderTop(borderStyle);

                cell.setCellStyle(cellStyle);
            }
        }
    }

    /**
     * 获取隧道特定分部的分项得分详情
     */
    @GetMapping("/getPartScoreDetail")
    public AjaxResult getPartScoreDetail(@RequestParam("tunnelId") Long tunnelId, @RequestParam("partName") String partName) {
        if (tunnelId == null) {
            return AjaxResult.error("隧道ID不能为空");
        }

        if (StringUtils.isEmpty(partName)) {
            return AjaxResult.error("分部名称不能为空");
        }

        try {
            // 获取隧道信息
            TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(tunnelId);
            if (tunnelInfo == null) {
                return AjaxResult.error("未找到隧道信息");
            }

            // 准备查询条件
            CheckEnumRelation queryParam = new CheckEnumRelation();
            queryParam.setTunnelId(tunnelId);

            // 使用与exportDetail相同的方法查询并获取分数
            List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(queryParam);

            // 处理空名称
            for (CheckEnumRelation enumRelation : checkEnumRelationList) {
                if(StringUtils.isBlank(enumRelation.getName())){
                    enumRelation.setName("");
                }
            }

            // 如果没有检测数据，返回错误
            if (checkEnumRelationList.isEmpty()) {
                return AjaxResult.error("该隧道没有检测数据");
            }

            // 查询当前隧道下的所有资产
            List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(tunnelId);
            for (FacilityInfo facilityInfo : facilityInfoList) {
                if(StringUtils.isBlank(facilityInfo.getName())){
                    facilityInfo.setName("");
                }
            }

            // 根据partCode+itemCode进行分组
            Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream()
                    .collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));

            // 创建映射，用于存储设备信息和分数
            Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();
            Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>();
            Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>();

            // 预处理checkEnumRelationList
            Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream()
                    .collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));

            // 使用initCommonScore处理分数
            checkEnumRelationService.initCommonScore(checkEnumRelationList, queryParam, relationGroupMap, facilityGroupMap,
                    deviceInfoMap, codeScoreMap, defaultScoreMap, new HashMap<>(), new HashMap<>(), Lists.newArrayList());

            // 查询结果
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> itemScores = new ArrayList<>();

            // 找到匹配分部名称的分部代码
            Set<String> matchedPartCodes = new HashSet<>();
            for (CheckEnumRelation relation : checkEnumRelationList) {
                if (partName.equals(relation.getPartName())) {
                    matchedPartCodes.add(relation.getPartCode());
                }
            }

            // 按分部代码分组
            for (String partCode : matchedPartCodes) {
                // 按分项分组
                Map<String, List<CheckEnumRelation>> itemGroupMap = checkEnumRelationList.stream()
                        .filter(r -> partCode.equals(r.getPartCode()))
                        .collect(Collectors.groupingBy(r -> r.getItemCode() + "_" + r.getItemName()));

                // 计算每个分项的得分
                for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                    List<CheckEnumRelation> itemData = itemEntry.getValue();

                    if (!itemData.isEmpty()) {
                        String itemName = itemData.get(0).getItemName();
                        BigDecimal itemScore = calculateItemScore(itemData);

                        Map<String, Object> itemInfo = new HashMap<>();
                        itemInfo.put("itemName", itemName);
                        itemInfo.put("score", itemScore);

                        // 获取分项下的检查内容及其得分
                        List<Map<String, Object>> contentDetails = new ArrayList<>();

                        // 按检查内容分组
                        Map<String, List<CheckEnumRelation>> contentGroups = itemData.stream()
                                .collect(Collectors.groupingBy(relation ->
                                        StringUtils.isNotEmpty(relation.getCheckContent()) ? relation.getCheckContent() : "未分类"));

                        for (Map.Entry<String, List<CheckEnumRelation>> contentEntry : contentGroups.entrySet()) {
                            String checkContent = contentEntry.getKey();
                            List<CheckEnumRelation> contentData = contentEntry.getValue();

                            if (!contentData.isEmpty()) {
                                // 计算该检查内容的平均得分 - 使用与calculateItemScore相同的逻辑
                                BigDecimal contentScore = BigDecimal.ZERO;
                                int validItemCount = 0;


                                for (CheckEnumRelation item : contentData) {
                                    if (item.getScore() != null && item.getScore().compareTo(BigDecimal.ZERO) >= 0) {
                                        contentScore = contentScore.add(item.getScore());
                                        validItemCount++;
                                    }
                                }

                                // 计算平均分
                                BigDecimal avgScore = validItemCount > 0
                                        ? contentScore.divide(new BigDecimal(validItemCount), 2, RoundingMode.HALF_EVEN)
                                        : null;

                                Map<String, Object> contentInfo = new HashMap<>();
                                contentInfo.put("checkContent", checkContent);
                                contentInfo.put("score", avgScore);
                                contentDetails.add(contentInfo);
                            }
                        }

                        itemInfo.put("contents", contentDetails);
                        itemScores.add(itemInfo);
                    }
                }
            }

            // 对分项得分进行排序，从高到低
            itemScores.sort((a, b) -> {
                BigDecimal scoreA = (BigDecimal) a.get("score");
                BigDecimal scoreB = (BigDecimal) b.get("score");
                if (scoreA == null && scoreB == null) return 0;
                if (scoreA == null) return 1;
                if (scoreB == null) return -1;
                return scoreB.compareTo(scoreA);
            });

            result.put("tunnelName", tunnelInfo.getTunnelName());
            result.put("partName", partName);
            result.put("itemScores", itemScores);

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取分部得分详情失败", e);
            return AjaxResult.error("获取分部得分详情失败: " + e.getMessage());
        }
    }





    /**
     * 导出设施检查Word版
     */
    @Log(title = "导出检测报告word版本", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCheckReportWord")
    public void exportCheckReportWord(HttpServletResponse response, CheckEnumRelation checkEnumRelation) {
        try {
            //隧道ID不能为空
            if (Objects.isNull(checkEnumRelation.getTunnelId())) {
                throw new ServiceException("隧道ID不能为空");
            }

            TunnelInfo tunnelInfo = tunnelInfoMapper.selectTunnelInfoById(checkEnumRelation.getTunnelId());
            if (Objects.isNull(tunnelInfo)) {
                throw new ServiceException("隧道不存在");
            }
            //查询当前隧道所在的路段下的所有隧道
            List<TunnelInfo> roadTunnelList = tunnelInfoMapper.selectListByRoadName(tunnelInfo.getRoadName());

            //查询隧道资产,根据分部分项来统计数量
            List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelIdGroupByItemCode(checkEnumRelation.getTunnelId());

            List<String> partNameList = facilityInfoList.stream().map(FacilityInfo::getPartName).distinct().collect(Collectors.toList());

            // 获取当前年份
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);

            // 创建标题文本 2024 年十巫高速鲍峡至溢水段横岩寨隧道机电设施定期检查报告
            String pageTitle = currentYear + "年" + tunnelInfo.getRoadName() + tunnelInfo.getTunnelName() + "机电设施定期检查报告";
            String projectName ="湖北交投高速公路检测评定"+tunnelInfo.getSection()+"标段";
            //1.短,2.中,3.长,4.特长
            //横岩寨隧道隶属湖北交投鄂西北高速公路运营管理有限公司 ，位于十巫高速 鲍峡至溢水段 ，上行起点桩号为 K81+727，下行起点桩号为 K84+540，为上、下  行分离式隧道 ，
            // 隧道上行洞长 2871m，隧道下行洞长 2850m，为长隧道 。横岩寨  隧道机电设施设置有供配电设施、照明设施、通风设施、消防设施、监控与通信 设施。
            String projectDescription =tunnelInfo.getTunnelName()+"隶属"+tunnelInfo.getCompanyName()+",位于"+ tunnelInfo.getRoadName()
                    +",上行起点桩号为"+tunnelInfo.getUpStartCode()+",下行起点桩号为"+tunnelInfo.getDownStartCode()+",为上、下行分离式隧道,"+"隧道上行洞长"+tunnelInfo.getUpTunnelLength()+"m"
                    +",隧道下行洞长"+tunnelInfo.getDownTunnelLength()+"m,为"+tunnelInfo.getTunnelType()+"隧道。"
                    +tunnelInfo.getTunnelName()+"机电设施设置有"+StringUtils.join(partNameList, "、")+"。";

            //本次定期检查横岩寨隧道机电设施包括供配电设施、照明设施、通风设施、 消防设施、监控与通信设施共五个分部设施 ，隧道机电分项设施检测项 目得分、 分部设施、隧道机电设施技术状况评分及分类如表 5.1 和表 5.2 所示。
            String checkResult = "本次定期检查"+tunnelInfo.getTunnelName()+"机电设施包括"+StringUtils.join(partNameList, "、")+"共"+partNameList.size()+"个分部设施 ，隧道机电分项设施检测项目得分、 分部设施、隧道机电设施技术状况评分及分类如表 5.1 和表 5.2 所示。";


            try {
                // 1. 读取模板文件
                InputStream is = this.getClass().getResourceAsStream("/public/word/check_report_template.docx");
                if (is == null) {
                    log.error("找不到报告模板文件");
                    throw new ServiceException("找不到报告模板文件");
                }

                // 2. 加载Word文档
                WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(is);

                // 3. 替换文档中的变量
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "projectName", projectName);
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "roadName", tunnelInfo.getRoadName());
                WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "section", tunnelInfo.getSection());
                WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "roadName", tunnelInfo.getRoadName());
                // 特别处理表格中的日期占位符
                WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "checkDate", DateUtil.format(DateUtil.date(), "yyyy年MM月dd日"));
                WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "tunnelName", tunnelInfo.getTunnelName());
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "projectDescription", projectDescription);
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "checkResult", checkResult);
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "tunnelName", tunnelInfo.getTunnelName());


                // 4. 替换页眉中的变量
                WordDocumentUtil.replaceHeaderPlaceholder(wordMLPackage, "pageTitle", pageTitle);

                // 5. 生成设施表格并替换${facilityTable}占位符
                generateAndReplaceFacilityTable(wordMLPackage, facilityInfoList);
                //生成分部分项的分值
                generateCheckResultTable(wordMLPackage,tunnelInfo);

                // 生成隧道列表并替换${roadTunnelList}占位符
                WordDocumentUtil.generateRoadTunnelList(wordMLPackage, roadTunnelList, tunnelInfo.getTunnelName());

                // 生成附表目录并替换${indexPartList}占位符
//                generateIndexPartList(wordMLPackage, partNameList);

                // 根据partNameList删除不需要的4.1-4.5检测内容部分，并重新排列序号
//                deleteAndReorderDetectionContent(wordMLPackage, partNameList);

                // 生成项目范围并替换${itemRange}占位符
                generateAndReplaceItemRange(wordMLPackage, partNameList);

                //生成检查内容和技术要求
                generateAndReplaceCheckContentAndDemand(wordMLPackage, tunnelInfo);


                //添加检测内容表格到检测报告里面
                checkEnumWordController.initCheckDetailWord(checkEnumRelation,wordMLPackage,tunnelInfo,false);

                // 生成分部名称列表并替换${partNameList}占位符（放在最后，避免被其他操作覆盖）
                String partNameListStr = String.join("、", partNameList);
                log.info("准备替换partNameList占位符，数据：{}", partNameListStr);

                // 先尝试使用常规方法替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "partNameList", partNameListStr);

                // 再尝试使用段落级别的替换方法（针对可能被拆分的占位符）
                try {
                    MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
                    List<Object> docObjects = mainDocumentPart.getContent();
                    boolean found = false;

                    for (int i = 0; i < docObjects.size(); i++) {
                        Object obj = docObjects.get(i);
                        if (obj instanceof P) {
                            P paragraph = (P) obj;
                            String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

                            if (paragraphText.contains("${partNameList}")) {
                                log.info("找到partNameList占位符，段落内容：{}", paragraphText);
                                updateParagraphText(paragraph, paragraphText.replace("${partNameList}", partNameListStr));
                                found = true;
                                log.info("使用段落级替换方法成功替换partNameList占位符");
                                break;
                            }
                        }
                    }

                    if (!found) {
                        log.warn("在所有段落中都未找到partNameList占位符");
                    }
                } catch (Exception e) {
                    log.error("段落级替换partNameList失败", e);
                }

                log.info("partNameList占位符替换完成");

                // 在保存文档前，最后一步填充页码信息
//                generatePageNumbers(wordMLPackage);

                // 生成自动目录（如果模板中有${autoTOC}占位符）
                WordDocumentUtil.generateAutoTableOfContents(wordMLPackage, "${autoTOC}");

                // 清理每页最后的空行
//                removeEmptyLinesAtEndOfPages(wordMLPackage);

                // 先将文档保存到字节数组，以便获取文件大小
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                wordMLPackage.save(baos);
                byte[] documentBytes = baos.toByteArray();

                // 设置响应头，包含文件大小信息
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode(pageTitle, "UTF-8");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
                response.setHeader("Content-Length", String.valueOf(documentBytes.length));

                // 将字节数组写入响应流
                response.getOutputStream().write(documentBytes);
                response.getOutputStream().flush();

                log.info("导出检测报告Word版本成功，隧道ID: {}", checkEnumRelation.getTunnelId());
            } catch (Exception e) {
                log.error("处理Word模板异常", e);
                throw new ServiceException("处理Word模板异常: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("导出Word格式报告失败", e);
            throw new ServiceException("导出Word格式报告失败: " + e.getMessage());
        }
    }

    /**
     * 生成并替换检测内容和技术要求表格
     * 根据隧道ID查询检测内容数据，按分部分组生成标题和表格，替换Word文档中的占位符
     *
     * @param wordMLPackage Word文档对象
     * @param tunnelInfo 隧道信息
     */
    private void generateAndReplaceCheckContentAndDemand(WordprocessingMLPackage wordMLPackage, TunnelInfo tunnelInfo) {
        try {
            // 查询检测内容技术要求数据
            List<CheckDemand> checkDemandList = checkDemandMapper.queryListByFacilityExist(tunnelInfo.getId());

            if (checkDemandList == null || checkDemandList.isEmpty()) {
                // 如果没有数据，替换为空内容
                replaceAdvancedPlaceholder(wordMLPackage, "${checkDemandList}", "");
                return;
            }

            // 按分部分组并排序
            Map<String, List<CheckDemand>> partGroupMap = checkDemandList.stream()
                    .collect(Collectors.groupingBy(
                            CheckDemand::getPartCode,
                            LinkedHashMap::new,
                            Collectors.toList()
                    ));

            ObjectFactory factory = new ObjectFactory();
            List<Object> contentList = new ArrayList<>();

            int partIndex = 1;
            for (Map.Entry<String, List<CheckDemand>> entry : partGroupMap.entrySet()) {
                String partCode = entry.getKey();
                List<CheckDemand> partDemands = entry.getValue();
                String partName = partDemands.get(0).getPartName();

                // 创建二级标题：4.序号 分部名称检测内容和方法
                String titleText = "4." + partIndex + " " + partName + "检测内容和方法";
                P titleP = createCheckDemandTitleParagraph(titleText, factory);
                contentList.add(titleP);

                // 创建表格标题：表 4.序号 分部名称检测内容及方法
                String tableTitleText = "表 4." + partIndex + " " + partName + "检测内容及方法";
                P tableTitleP = createCheckDemandTableTitleParagraph(tableTitleText, factory);
                contentList.add(tableTitleP);

                // 创建表格
                Tbl table = createCheckDemandTable(partDemands, factory);
                contentList.add(table);

                partIndex++;
            }

            // 替换Word文档中的占位符
            replaceCheckDemandPlaceholderInDocument(wordMLPackage, contentList);

        } catch (Exception e) {
            log.error("生成检测内容和技术要求表格失败", e);
            // 出错时替换为空内容
            try {
                replaceAdvancedPlaceholder(wordMLPackage, "${checkDemandList}", "");
            } catch (Exception ex) {
                log.error("清理占位符时也发生错误", ex);
            }
        }
    }

    /**
     * 在Word文档中替换检测内容占位符
     *
     * @param wordMLPackage Word文档对象
     * @param contentList 要替换的内容列表
     */
    private void replaceCheckDemandPlaceholderInDocument(WordprocessingMLPackage wordMLPackage, List<Object> contentList) {
        try {
            // 获取主文档部分
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            // 查找包含${checkDemandList}占位符的段落并替换为检测内容
            boolean replacementDone = false;
            List<Object> docObjects = mainDocumentPart.getContent();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains("${checkDemandList}")) {
                        log.info("找到包含${checkDemandList}占位符的段落，准备替换为{}个内容对象", contentList.size());

                        // 替换占位符段落为内容列表的第一个元素
                        if (!contentList.isEmpty()) {
                            docObjects.set(i, contentList.get(0));

                            // 依次插入剩余的内容对象
                            for (int j = 1; j < contentList.size(); j++) {
                                docObjects.add(i + j, contentList.get(j));
                            }

                            log.info("成功替换${checkDemandList}占位符为{}个内容对象（包含检测内容标题和表格）", contentList.size());
                        } else {
                            // 如果内容列表为空，直接移除占位符段落
                            docObjects.remove(i);
                            log.info("内容列表为空，移除${checkDemandList}占位符段落");
                        }

                        replacementDone = true;
                        break;
                    }
                }
            }

            if (!replacementDone) {
                log.warn("未找到${checkDemandList}占位符，尝试作为字符串替换");
                // 如果没有找到占位符段落，尝试作为普通文本替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "checkDemandList", "");
            }

        } catch (Exception e) {
            log.error("替换检测内容占位符失败", e);
            throw e;
        }
    }

    /**
     * 创建检测内容标题段落（二级标题）
     */
    private P createCheckDemandTitleParagraph(String titleText, ObjectFactory factory) {
        P paragraph = factory.createP();

        // 设置段落属性
        PPr pPr = factory.createPPr();

        // 设置左对齐
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.LEFT);
        pPr.setJc(jc);

        // 设置大纲级别为二级标题（1表示二级标题）- 这是在导航视图中显示的关键
        PPrBase.OutlineLvl outlineLvl = factory.createPPrBaseOutlineLvl();
        outlineLvl.setVal(BigInteger.valueOf(1)); // 0=一级标题, 1=二级标题, 2=三级标题...
        pPr.setOutlineLvl(outlineLvl);

        // 设置1.5倍行距
        PPrBase.Spacing spacing = factory.createPPrBaseSpacing();
        spacing.setLine(BigInteger.valueOf(360)); // 1.5倍行距 = 360 (240 * 1.5)
        spacing.setLineRule(STLineSpacingRule.AUTO);
        pPr.setSpacing(spacing);
        paragraph.setPPr(pPr);

        // 创建文本运行
        R run = factory.createR();
        Text text = factory.createText();
        text.setValue(titleText);
        text.setSpace("preserve");
        run.getContent().add(text);

        // 直接在Run级别设置字体样式，不依赖段落样式
        RPr rPr = factory.createRPr();

        // 设置字体为黑体
        RFonts rFonts = factory.createRFonts();
        rFonts.setEastAsia("黑体");
        rFonts.setHAnsi("黑体");
        rPr.setRFonts(rFonts);

        // 设置字号为四号（14磅 = 28半磅）
        HpsMeasure fontSize = factory.createHpsMeasure();
        fontSize.setVal(BigInteger.valueOf(28)); // 14磅 * 2 = 28半磅
        rPr.setSz(fontSize);
        rPr.setSzCs(fontSize);

        // 设置字体加粗
        BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
        bold.setVal(true);
        rPr.setB(bold);

        // 设置字体颜色为黑色
        org.docx4j.wml.Color color = factory.createColor();
        color.setVal("000000"); // 黑色
        rPr.setColor(color);

        run.setRPr(rPr);
        paragraph.getContent().add(run);

        return paragraph;
    }

    /**
     * 创建检测内容表格标题段落
     */
    private P createCheckDemandTableTitleParagraph(String titleText, ObjectFactory factory) {
        P paragraph = factory.createP();

        // 设置段落属性
        PPr pPr = factory.createPPr();

        // 设置居中对齐
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.CENTER);
        pPr.setJc(jc);

        paragraph.setPPr(pPr);

        // 创建文本内容
        R run = factory.createR();
        RPr rPr = factory.createRPr();

        // 设置字体为黑体
        RFonts rFonts = factory.createRFonts();
        rFonts.setAscii("SimHei");
        rFonts.setHAnsi("SimHei");
        rFonts.setEastAsia("SimHei");
        rPr.setRFonts(rFonts);

        // 设置字号为五号（10.5磅）
        HpsMeasure fontSize = factory.createHpsMeasure();
        fontSize.setVal(BigInteger.valueOf(21)); // 10.5*2
        rPr.setSz(fontSize);
        rPr.setSzCs(fontSize);

        run.setRPr(rPr);

        Text text = factory.createText();
        text.setValue(titleText);
        run.getContent().add(text);
        paragraph.getContent().add(run);

        return paragraph;
    }

    /**
     * 创建检测内容和技术要求表格
     */
    private Tbl createCheckDemandTable(List<CheckDemand> checkDemands, ObjectFactory factory) {
        Tbl table = factory.createTbl();

        // 设置表格属性
        TblPr tblPr = factory.createTblPr();

        // 设置表格宽度为100%
        TblWidth tblWidth = factory.createTblWidth();
        tblWidth.setW(BigInteger.valueOf(0));
        tblWidth.setType("auto");
        tblPr.setTblW(tblWidth);

        // 设置表格边框
        TblBorders tblBorders = factory.createTblBorders();
        CTBorder border = factory.createCTBorder();
        border.setVal(STBorder.SINGLE);
        border.setSz(BigInteger.valueOf(4));
        border.setColor("000000");

        tblBorders.setTop(border);
        tblBorders.setBottom(border);
        tblBorders.setLeft(border);
        tblBorders.setRight(border);
        tblBorders.setInsideH(border);
        tblBorders.setInsideV(border);
        tblPr.setTblBorders(tblBorders);

        table.setTblPr(tblPr);

        // 创建表头
        Tr headerRow = factory.createTr();

        // 表头列：分项设施名称、主要检查内容、技术要求、检查方法
        String[] headers = {"分项设施名称", "主要检查内容", "技术要求", "检查方法"};
        int[] widths = {2000, 3000, 3000, 2500}; // 列宽分配

        for (int i = 0; i < headers.length; i++) {
            Tc headerCell = createCheckDemandTableCell(headers[i], widths[i], true, factory, i);
            headerRow.getContent().add(headerCell);
        }

        table.getContent().add(headerRow);

        // 按分项名称分组，用于合并相同的分项设施名称
        Map<String, List<CheckDemand>> itemGroupMap = checkDemands.stream()
                .collect(Collectors.groupingBy(
                        CheckDemand::getItemName,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        // 创建数据行
        for (Map.Entry<String, List<CheckDemand>> itemEntry : itemGroupMap.entrySet()) {
            String itemName = itemEntry.getKey();
            List<CheckDemand> itemDemands = itemEntry.getValue();

            for (int i = 0; i < itemDemands.size(); i++) {
                CheckDemand demand = itemDemands.get(i);
                Tr dataRow = factory.createTr();

                // 分项设施名称列（第一行显示，其他行合并）
                if (i == 0) {
                    Tc itemNameCell = createCheckDemandTableCell(itemName, widths[0], false, factory, 0);
                    if (itemDemands.size() > 1) {
                        // 设置垂直合并的起始单元格
                        setVerticalMergeRestart(itemNameCell, factory);
                    }
                    dataRow.getContent().add(itemNameCell);
                } else {
                    // 设置垂直合并的继续单元格
                    Tc mergedCell = createCheckDemandTableCell("", widths[0], false, factory, 0);
                    setVerticalMergeContinue(mergedCell, factory);
                    dataRow.getContent().add(mergedCell);
                }

                // 主要检查内容
                Tc checkContentCell = createCheckDemandTableCell(demand.getCheckContent(), widths[1], false, factory, 1);
                dataRow.getContent().add(checkContentCell);

                // 技术要求
                Tc demandCell = createCheckDemandTableCell(demand.getDemand(), widths[2], false, factory, 2);
                dataRow.getContent().add(demandCell);

                // 检查方法
                Tc checkMethodCell = createCheckDemandTableCell(demand.getCheckMethod(), widths[3], false, factory, 3);
                dataRow.getContent().add(checkMethodCell);

                table.getContent().add(dataRow);
            }
        }

        return table;
    }

    /**
     * 创建检测内容表格单元格
     */
    private Tc createCheckDemandTableCell(String content, int width, boolean isHeader, ObjectFactory factory) {
        return createCheckDemandTableCell(content, width, isHeader, factory, 0);
    }

    /**
     * 创建检测内容表格单元格（带列索引）
     * @param content 单元格内容
     * @param width 单元格宽度
     * @param isHeader 是否为表头
     * @param factory ObjectFactory对象
     * @param columnIndex 列索引（0=第一列，1=第二列，以此类推）
     */
    private Tc createCheckDemandTableCell(String content, int width, boolean isHeader, ObjectFactory factory, int columnIndex) {
        Tc cell = factory.createTc();

        // 设置单元格属性
        TcPr tcPr = factory.createTcPr();

        // 设置单元格宽度
        TblWidth cellWidth = factory.createTblWidth();
        cellWidth.setW(BigInteger.valueOf(width));
        cellWidth.setType("dxa");
        tcPr.setTcW(cellWidth);

        // 设置单元格垂直居中对齐
        CTVerticalJc vAlign = factory.createCTVerticalJc();
        vAlign.setVal(STVerticalJc.CENTER);
        tcPr.setVAlign(vAlign);

        cell.setTcPr(tcPr);

        // 创建段落
        P paragraph = factory.createP();

        // 设置段落属性
        PPr pPr = factory.createPPr();

        // 设置水平对齐方式
        Jc jc = factory.createJc();
        if (columnIndex >= 1) {
            // 第2、3、4列（索引1、2、3）水平居左
            jc.setVal(JcEnumeration.LEFT);
        } else {
            // 第1列（索引0）水平居中
            jc.setVal(JcEnumeration.CENTER);
        }
        pPr.setJc(jc);

        paragraph.setPPr(pPr);

        // 创建文本运行
        R run = factory.createR();
        RPr rPr = factory.createRPr();

        // 设置字体为宋体
        RFonts rFonts = factory.createRFonts();
        rFonts.setAscii("SimSun");
        rFonts.setHAnsi("SimSun");
        rFonts.setEastAsia("SimSun");
        rPr.setRFonts(rFonts);

        // 设置字号为五号（10.5磅）
        HpsMeasure fontSize = factory.createHpsMeasure();
        fontSize.setVal(BigInteger.valueOf(21)); // 10.5*2
        rPr.setSz(fontSize);
        rPr.setSzCs(fontSize);

        // 表头加粗
        if (isHeader) {
            BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
            rPr.setB(bold);
            rPr.setBCs(bold);
        }

        run.setRPr(rPr);

        // 处理多行文本
        if (content != null && !content.isEmpty()) {
            String[] lines = content.split("\n");
            for (int i = 0; i < lines.length; i++) {
                if (i > 0) {
                    // 添加换行符
                    Br lineBreak = factory.createBr();
                    run.getContent().add(lineBreak);
                }
                Text text = factory.createText();
                text.setValue(lines[i]);
                run.getContent().add(text);
            }
        } else {
            Text text = factory.createText();
            text.setValue("");
            run.getContent().add(text);
        }

        paragraph.getContent().add(run);
        cell.getContent().add(paragraph);

        return cell;
    }

    /**
     * 设置垂直合并开始
     */
    private void setVerticalMergeRestart(Tc cell, ObjectFactory factory) {
        TcPr tcPr = cell.getTcPr();
        if (tcPr == null) {
            tcPr = factory.createTcPr();
            cell.setTcPr(tcPr);
        }

        org.docx4j.wml.TcPrInner.VMerge vMerge = factory.createTcPrInnerVMerge();
        vMerge.setVal("restart");
        tcPr.setVMerge(vMerge);
    }

    /**
     * 设置垂直合并继续
     */
    private void setVerticalMergeContinue(Tc cell, ObjectFactory factory) {
        TcPr tcPr = cell.getTcPr();
        if (tcPr == null) {
            tcPr = factory.createTcPr();
            cell.setTcPr(tcPr);
        }

        org.docx4j.wml.TcPrInner.VMerge vMerge = factory.createTcPrInnerVMerge();
        vMerge.setVal("continue");
        tcPr.setVMerge(vMerge);
    }



    /**
     * 生成分部分项的分值
     * @param wordMLPackage Word文档对象
     * @param tunnelInfo 隧道信息
     */
    private void generateCheckResultTable(WordprocessingMLPackage wordMLPackage, TunnelInfo tunnelInfo) {
        // 查询检测关系数据
        CheckEnumRelation queryParam = new CheckEnumRelation();
        queryParam.setTunnelId(tunnelInfo.getId());
        List<CheckEnumRelation> checkEnumRelationList = checkEnumRelationService.selectCheckEnumRelationListMatch(queryParam);
        for (CheckEnumRelation enumRelation : checkEnumRelationList) {
            if(StringUtils.isBlank(enumRelation.getName())){
                enumRelation.setName("");
            }
        }
        //缺陷备注
        Map<String, List<String>> partItemLocationRemarkMap = new HashMap<>();
        //分部分项对应的建议措施
        Map<String,List<String>> partImportantMap= new HashMap<>();
        List<QuestionTable> questionTableList = new ArrayList<>();
        this.generatePartOne(checkEnumRelationList,wordMLPackage,tunnelInfo,queryParam,questionTableList,partImportantMap);

        // 查询隧道检查数据
        TunnelCheck tunnelCheck = new TunnelCheck();
        tunnelCheck.setTunnelId(tunnelInfo.getId());
        List<TunnelCheck> checkList = tunnelCheckMapper.selectTunnelCheckList(tunnelCheck);
        // 根据partCode进行排序
        checkList.sort(Comparator.comparing(TunnelCheck::getPartCode));
        List<String> indexList = Lists.newArrayList();
        for (TunnelCheck check : checkList) {
            if (indexList.contains(check.getPartName())) {
                continue;
            }
            indexList.add(check.getPartName());
        }
        //生成对应的分部评分和等级
        generatePartScore(wordMLPackage, checkEnumRelationList,tunnelInfo);
        //生成各个分部对应的分项的缺陷描述描述和建议，获取有数据的分部列表
        List<String> partNamesWithData = generateQuestionRemarkAndSuggestion(wordMLPackage,questionTableList,tunnelInfo.getId());
        //把分部对应测试照片添加进来
        generatePartItemPhoto(wordMLPackage,tunnelInfo);
        //创建需重点关注设施汇总表格
        generatePartImportantTable(wordMLPackage,partImportantMap);
        //根据 partNamesWithData.size()来替换占位符 ${maxNo}
        replaceMaxNoPlaceholder(wordMLPackage, partNamesWithData.size()+1);
        //生成目录--必须要放在deletePart后面
        generateIndexContents(wordMLPackage,partNamesWithData);
        //根据实际目录生成 分部检测内容和方法
        generatePartIndexContents(wordMLPackage,partNamesWithData);
    }


    /**
     * 生成检测结果-机电分项设施检测项目得分数据汇总
     * @param checkEnumRelationList
     * @param wordMLPackage
     * @param tunnelInfo
     * @param queryParam
     * @param partImportantMap
     */
    private void generatePartOne(List<CheckEnumRelation> checkEnumRelationList,WordprocessingMLPackage wordMLPackage,TunnelInfo tunnelInfo,CheckEnumRelation queryParam
            , List<QuestionTable> questionTableList,Map<String,List<String>> partImportantMap){
        try {
            // 如果没有检测数据，提示错误
            if (checkEnumRelationList.isEmpty()) {
                log.warn("隧道ID {} 没有检测数据", tunnelInfo.getId());
                // 如果没有数据，替换为空字符串
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "partAndItemList", "");
                return;
            }

            // 查询当前隧道下的所有资产
            List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectListByTunnelId(tunnelInfo.getId());
            for (FacilityInfo facilityInfo : facilityInfoList) {
                if(StringUtils.isBlank(facilityInfo.getName())){
                    facilityInfo.setName("");
                }
            }

            // 根据partCode+itemCode进行分组
            Map<String, List<FacilityInfo>> facilityGroupMap = facilityInfoList.stream().collect(Collectors.groupingBy(facility -> facility.getPartCode() + "_" + facility.getItemCode()));

            // 创建映射，用于存储设备信息
            Map<String, Map<String, String>> deviceInfoMap = new HashMap<>();
            Map<String, CheckEnumRelation> codeScoreMap = new HashMap<>(); // 按编号查找
            Map<String, CheckEnumRelation> defaultScoreMap = new HashMap<>(); // 默认分值

            // 预处理checkEnumRelationList
            Map<String, List<CheckEnumRelation>> relationGroupMap = checkEnumRelationList.stream().collect(Collectors.groupingBy(r -> r.getPartCode() + "_" + r.getItemCode()));

            // 使用已有方法计算分数
            checkEnumRelationService.initCommonScore(checkEnumRelationList, queryParam, relationGroupMap, facilityGroupMap,
                    deviceInfoMap, codeScoreMap, defaultScoreMap, new HashMap<>(), new HashMap<>(),questionTableList);

            // 按partCode分组并排序
            Map<String, List<CheckEnumRelation>> partGroupMap = new LinkedHashMap<>();
            // 获取所有分部信息并排序
            List<String> partCodes = checkEnumRelationList.stream()
                    .map(CheckEnumRelation::getPartCode)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            // 按排序后的partCode填充partGroupMap
            for (String partCode : partCodes) {
                List<CheckEnumRelation> partRelations = checkEnumRelationList.stream()
                        .filter(r -> partCode.equals(r.getPartCode()))
                        .collect(Collectors.toList());
                partGroupMap.put(partCode, partRelations);
            }

            // 创建表格
            // 首先计算总行数：表头1行 + 所有分项数据行
            int totalRows = 1; // 表头
            // 计算每个分部下的分项数量，合计总行数
            for (String partCode : partCodes) {
                List<CheckEnumRelation> partRelations = partGroupMap.get(partCode);
                if (partRelations != null && !partRelations.isEmpty()) {
                    // 根据itemCode对分项分组
                    Map<String, List<CheckEnumRelation>> itemGroupMap = partRelations.stream().collect(Collectors.groupingBy(CheckEnumRelation::getItemCode));
                    totalRows += itemGroupMap.size(); // 每个分项一行
                }
            }

            // 创建表格对象，3列：分部设施名称、分项设施名称、分项设施检测项目得分
            Tbl table = WordDocumentUtil.createTable(totalRows, 3);

            // 设置表格列宽
            TblGrid tblGrid = table.getTblGrid();
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(1500)); // 分部设施名称列
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(2000)); // 分项设施名称列
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(1500)); // 分项设施检测项目得分列

            // 设置表头 - 使用false参数取消背景色
            WordDocumentUtil.setCellText(table, 0, 0, "分部设施名称", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "分项设施名称", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "分项设施检测项目得分", 12, true, false, 2);

            // 优化表格样式：设置宋体五号字体和重复表头
            optimizeTableStyleWithSize5Font(table, 1);

            // 当前行索引（从表头后开始）
            int currentRow = 1;

            // 遍历每个分部填充数据
            for (String partCode : partCodes) {
                List<CheckEnumRelation> partRelations = partGroupMap.get(partCode);
                if (partRelations == null || partRelations.isEmpty()) {
                    continue;
                }
                // 获取分部名称
                String partName = partRelations.get(0).getPartName();
                // 根据分项(itemCode)进行分组并按itemCode排序
                Map<String, List<CheckEnumRelation>> itemGroupMap = new TreeMap<>(); // 使用TreeMap自动按key排序
                for (CheckEnumRelation relation : partRelations) {
                    String itemCode = relation.getItemCode();
                    if (itemCode != null) {
                        itemGroupMap.computeIfAbsent(itemCode, k -> new ArrayList<>()).add(relation);
                    }
                }

                // 记录该分部的起始行
                int partStartRow = currentRow;

                // 计算该分部的分数
                BigDecimal partScore = calculatePartScore(itemGroupMap);
                // 遍历每个分项
                boolean isFirstItemInPart = true;
                for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                    List<CheckEnumRelation> itemData = itemEntry.getValue();
                    if (itemData.isEmpty()) continue;
                    // 获取分项名称
                    String itemName = itemData.get(0).getItemName();
                    // 计算分项得分
                    BigDecimal itemScore = calculateItemScore(itemData);
                    if (itemScore.compareTo(new BigDecimal("80")) < 0) {
                        // 如果map中已有这个partName，追加itemName；否则新建一个list
                        partImportantMap.computeIfAbsent(partName, k -> new ArrayList<>()).add(itemName);
                    }
                    // 只在第一个分项填写分部名称
                    if (isFirstItemInPart) {
                        WordDocumentUtil.setCellText(table, currentRow, 0, partName, 12, false, false, 2);
                        isFirstItemInPart = false;
                    }

                    // 填写分项名称和得分
                    WordDocumentUtil.setCellText(table, currentRow, 1, itemName, 12, false, false, 2);

                    // 显示分项得分 (保留两位小数)
                    String scoreText = itemScore.compareTo(BigDecimal.ZERO) < 0 ? "/" : formatScore(itemScore);
                    WordDocumentUtil.setCellText(table, currentRow, 2, scoreText, 12, false, false, 2);

                    currentRow++;
                }

                // 如果该分部有多个分项，合并分部名称单元格
                if (currentRow - partStartRow > 1) {
                    mergeVerticalCells(table, 0, partStartRow, currentRow - 1);
                }
            }

            // 数据填充完成后，再次强制应用宋体五号字体到所有表格内容
            forceApplySize5FontToAllTableContent(table);

            // 获取主文档部分
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();

            // 查找包含${partAndItemList}占位符的段落并替换为表格
            boolean replacementDone = false;
            List<Object> docObjects = mainDocumentPart.getContent();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains("${partAndItemList}")) {
                        // 替换占位符段落为表格
                        docObjects.set(i, table);
                        replacementDone = true;
                        log.info("成功替换${partAndItemList}占位符为分部分项得分表格");
                        break;
                    }
                }
            }

            if (!replacementDone) {
                log.warn("未找到${partAndItemList}占位符，尝试作为字符串替换");
                // 如果没有找到占位符段落，尝试作为普通文本替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "partAndItemList", "");
            }

        } catch (Exception e) {
            log.error("生成分部分项得分表格失败", e);
            // 发生异常时尝试替换占位符为空字符串
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "partAndItemList", "");
        }
    }

    /**
     * 删除不存在的分部对应的段落，并重新编排剩余段落的编号
     * @param wordMLPackage Word文档包
     * @param filteredParts 实际存在的分部名称列表
     */
    private void deletePart(WordprocessingMLPackage wordMLPackage, List<String> filteredParts) {
        try {
            if (filteredParts == null || filteredParts.isEmpty()) {
                log.warn("没有要保留的分部数据，不执行删除操作");
                return;
            }

            log.info("开始处理分部段落，保留分部数量: {}", filteredParts.size());
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docContent = mainDocumentPart.getContent();

            // 创建映射表，记录每个分部名称
            Map<String, String> partMapping = new HashMap<>();
            partMapping.put("供配电设施", "供配电设施");
            partMapping.put("照明设施", "照明设施");
            partMapping.put("通风设施", "通风设施");
            partMapping.put("消防设施", "消防设施");
            partMapping.put("监控与通信设施", "监控与通信设施");

            // 记录要删除的段落索引
            List<Integer> indexesToRemove = new ArrayList<>();

            // 记录段落区块，用于标记连续的要删除的内容
            int startDeleteIndex = -1;
            String currentPartName = null;
            boolean isInDeleteBlock = false;

            // 收集所有章节标题段落
            List<Integer> sectionTitleIndexes = new ArrayList<>();
            List<String> sectionPartNames = new ArrayList<>();

            // 第一遍遍历，标记要删除的段落并收集所有章节标题
            for (int i = 0; i < docContent.size(); i++) {
                Object obj = docContent.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String text = WordDocumentUtil.getParagraphText(paragraph);

                    // 检查是否是标题段落（匹配 "6.x.x 分部名称主要问题及建议措施" 格式）
                    if (text.matches("6\\.[0-9]\\.[0-9]\\s+.*主要问题及建议措施.*")) {
                        // 如果之前在删除区块中，结束该区块
                        if (isInDeleteBlock) {
                            // 标记从startDeleteIndex到i-1的所有段落为删除
                            for (int j = startDeleteIndex; j < i; j++) {
                                indexesToRemove.add(j);
                            }
                            isInDeleteBlock = false;
                        }

                        // 提取分部名称
                        String partName = null;
                        for (String key : partMapping.keySet()) {
                            if (text.contains(key)) {
                                partName = key;
                                break;
                            }
                        }

                        log.info("找到标题段落: {}, 分部名称: {}", text, partName);

                        // 检查分部是否应该被删除
                        if (partName != null && !filteredParts.contains(partName)) {
                            log.info("标记删除分部: {}", partName);
                            isInDeleteBlock = true;
                            startDeleteIndex = i;
                            currentPartName = partName;
                        } else if (partName != null) {
                            // 如果是要保留的分部，记录其索引和名称，用于后续重新编号
                            sectionTitleIndexes.add(i);
                            sectionPartNames.add(partName);
                        }
                    }
                    // 如果遇到新的章节标题（如 "6.2 需重点关注设施汇总"），结束当前删除区块
                    else if (text.matches("6\\.[0-9]\\s+.*") && isInDeleteBlock) {
                        // 标记从startDeleteIndex到i-1的所有段落为删除
                        for (int j = startDeleteIndex; j < i; j++) {
                            indexesToRemove.add(j);
                        }
                        isInDeleteBlock = false;
                        log.info("在段落 {} 处结束删除区块", text);
                    }
                }
            }

            // 如果文档结束时仍在删除区块中，添加最后的段落
            if (isInDeleteBlock) {
                for (int j = startDeleteIndex; j < docContent.size(); j++) {
                    indexesToRemove.add(j);
                }
                log.info("在文档结束处结束删除区块");
            }

            // 按照索引从大到小删除段落（避免删除影响索引）
            indexesToRemove.sort(Collections.reverseOrder());
            for (int index : indexesToRemove) {
                if (index >= 0 && index < docContent.size()) {
                    docContent.remove(index);
                }
            }

            log.info("删除操作完成，共删除 {} 个段落", indexesToRemove.size());

            // 删除后重新收集章节标题段落，因为索引已经改变
            sectionTitleIndexes.clear();
            sectionPartNames.clear();

            for (int i = 0; i < docContent.size(); i++) {
                Object obj = docContent.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String text = WordDocumentUtil.getParagraphText(paragraph);

                    if (text.matches("6\\.[0-9]\\.[0-9]\\s+.*主要问题及建议措施.*")) {
                        String partName = null;
                        for (String key : partMapping.keySet()) {
                            if (text.contains(key)) {
                                partName = key;
                                break;
                            }
                        }

                        if (partName != null) {
                            sectionTitleIndexes.add(i);
                            sectionPartNames.add(partName);
                        }
                    }
                }
            }

            // 重新编排章节编号
            if (!sectionTitleIndexes.isEmpty()) {
                log.info("开始重新编排章节编号，共有 {} 个章节", sectionTitleIndexes.size());

                for (int i = 0; i < sectionTitleIndexes.size(); i++) {
                    int index = sectionTitleIndexes.get(i);
                    String partName = sectionPartNames.get(i);

                    if (index >= 0 && index < docContent.size() && partName != null) {
                        P paragraph = (P) docContent.get(index);

                        // 获取当前段落的文本并解析出原有编号部分
                        String currentText = WordDocumentUtil.getParagraphText(paragraph);
                        String newText = currentText;

                        // 替换编号部分，保持"6.1."前缀，后面的数字为(i+1)
                        newText = newText.replaceFirst("6\\.[0-9]\\.[0-9]", "6.1." + (i+1));

                        // 如果文本有变化，更新段落内容
                        if (!newText.equals(currentText)) {
                            // 创建新的段落内容
                            updateParagraphText(paragraph, newText);
                            log.info("更新章节编号: {} -> {}", currentText, newText);
                        }
                    }
                }

                log.info("章节编号重排完成");
            }

        } catch (Exception e) {
            log.error("处理分部段落失败", e);
        }
    }

    /**
     * 生成目录
     * @param wordMLPackage Word文档包
     */
    private void generateIndexContents(WordprocessingMLPackage wordMLPackage, List<String> filteredParts) {
        try {
            // 如果没有内容，直接返回
            if (filteredParts.isEmpty()) {
                log.warn("没有找到分部数据，目录生成失败");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "indexList", "");
                return ;
            }

            log.info("开始生成目录，共有{}个分部", filteredParts.size());

            // 获取主文档部分和占位符段落
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;

                    // 检查段落是否包含占位符
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);
                    if (paragraphText.contains("${indexList}")) {
                        // 清空当前段落的内容
                        paragraph.getContent().clear();

                        // 创建并添加目录内容
                        int startPageNum = 33;
                        int indexNum = 1;

                        // 设置宋体小四字号的样式
                        RPr rpr = factory.createRPr();
                        RFonts rFonts = factory.createRFonts();
                        rFonts.setEastAsia("宋体");
                        rFonts.setAscii("宋体");
                        rFonts.setHAnsi("宋体");
                        rFonts.setCs("宋体");
                        rpr.setRFonts(rFonts);

                        HpsMeasure fontSize = factory.createHpsMeasure();
                        fontSize.setVal(BigInteger.valueOf(24)); // 小四=12pt=24半磅
                        rpr.setSz(fontSize);
                        rpr.setSzCs(fontSize);

                        // 设置不加粗
                        BooleanDefaultTrue bFalse = new BooleanDefaultTrue();
                        bFalse.setVal(false);
                        rpr.setB(bFalse);

                        // 创建第一个目录项
                        boolean isFirst = true;
                        for (String partName : filteredParts) {
                            // 第一个目录项不需要添加换行
                            if (!isFirst) {
                                // 添加换行符
                                R breakRun = factory.createR();
                                breakRun.setRPr(rpr);
                                Br br = factory.createBr();
                                breakRun.getContent().add(br);
                                paragraph.getContent().add(breakRun);
                            } else {
                                isFirst = false;
                            }

                            // 添加目录文本内容
                            R textRun = factory.createR();
                            textRun.setRPr(rpr);
                            Text text = factory.createText();
                            text.setValue("6.6." + indexNum + " " + partName + "主要问题及建议措施");
                            text.setSpace("preserve");
                            textRun.getContent().add(text);
                            paragraph.getContent().add(textRun);

                            // 添加制表符
                            R tabRun = factory.createR();
                            tabRun.setRPr(rpr);
                            Text tabText = factory.createText();
                            tabText.setValue("\t");
                            tabText.setSpace("preserve");
                            tabRun.getContent().add(tabText);
                            paragraph.getContent().add(tabRun);

                            // 添加页码
                            R pageRun = factory.createR();
                            pageRun.setRPr(rpr);
                            Text pageText = factory.createText();
                            pageText.setValue(String.valueOf(startPageNum));
                            pageText.setSpace("preserve");
                            pageRun.getContent().add(pageText);
                            paragraph.getContent().add(pageRun);

                            indexNum++;
                            startPageNum++;
                        }

                        log.info("目录生成完成，使用内联换行符");
                        return ; // 成功找到并替换占位符，不需要继续处理
                    }
                }
            }

            // 如果没有找到占位符，使用常规替换方法
            log.warn("未找到${indexList}占位符，使用常规替换方法");
            StringBuilder contentBuilder = new StringBuilder();
            int startPageNum = 33;
            int indexNum = 1;

            for (String partName : filteredParts) {
                contentBuilder.append("6.6.").append(indexNum).append(" ")
                        .append(partName).append("主要问题及建议措施")
                        .append("\t").append(startPageNum);
                if (indexNum < filteredParts.size()) {
                    contentBuilder.append("\r\n  "); // 使用Windows换行符
                }
                indexNum++;
                startPageNum++;
            }

            WordDocumentUtil.replacePlaceholder(wordMLPackage, "indexList", contentBuilder.toString());
            log.info("目录生成完成，使用文本替换");
            return ;
        } catch (Exception e) {
            log.error("生成目录失败", e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "indexList", "");
        }
    }



    private void generatePartIndexContents(WordprocessingMLPackage wordMLPackage, List<String> filteredParts) {
        try {
            // 如果没有内容，直接返回
            if (filteredParts.isEmpty()) {
                log.warn("没有找到分部数据，目录生成失败");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "partIndexList", "");
                return ;
            }

            log.info("开始生成目录，共有{}个分部", filteredParts.size());

            // 获取主文档部分和占位符段落
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;

                    // 检查段落是否包含占位符
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);
                    if (paragraphText.contains("${partIndexList}")) {
                        // 清空当前段落的内容
                        paragraph.getContent().clear();

                        // 设置段落属性，包括制表位
                        PPr ppr = paragraph.getPPr();
                        if (ppr == null) {
                            ppr = factory.createPPr();
                            paragraph.setPPr(ppr);
                        }

                        // 创建制表位设置
                        Tabs tabs = factory.createTabs();
                        org.docx4j.wml.CTTabStop tabStop = factory.createCTTabStop();
                        tabStop.setVal(org.docx4j.wml.STTabJc.RIGHT); // 右对齐制表位
                        tabStop.setPos(BigInteger.valueOf(9100)); // 制表位位置
                        tabStop.setLeader(org.docx4j.wml.STTabTlc.DOT); // 设置引导线为点号
                        tabs.getTab().add(tabStop);
                        ppr.setTabs(tabs);

                        // 创建并添加目录内容
                        int startPageNum = 4;
                        int indexNum = 1;

                        // 设置宋体小四字号的样式
                        RPr rpr = factory.createRPr();
                        RFonts rFonts = factory.createRFonts();
                        rFonts.setEastAsia("宋体");
                        rFonts.setAscii("宋体");
                        rFonts.setHAnsi("宋体");
                        rFonts.setCs("宋体");
                        rpr.setRFonts(rFonts);

                        HpsMeasure fontSize = factory.createHpsMeasure();
                        fontSize.setVal(BigInteger.valueOf(24)); // 小四=12pt=24半磅
                        rpr.setSz(fontSize);
                        rpr.setSzCs(fontSize);

                        // 设置不加粗
                        BooleanDefaultTrue bFalse = new BooleanDefaultTrue();
                        bFalse.setVal(false);
                        rpr.setB(bFalse);

                        // 为每个分部创建单独的段落
                        for (int j = 0; j < filteredParts.size(); j++) {
                            String partName = filteredParts.get(j);

                            // 如果不是第一个条目，创建新段落
                            P currentParagraph;
                            if (j == 0) {
                                currentParagraph = paragraph; // 使用当前段落
                            } else {
                                currentParagraph = factory.createP();
                                // 复制段落属性（包括制表位设置）
                                currentParagraph.setPPr(ppr);
                                // 将新段落插入到文档中
                                docObjects.add(i + j, currentParagraph);
                            }

                            // 添加目录文本内容
                            R textRun = factory.createR();
                            RPr textRpr = factory.createRPr();
                            textRpr.setRFonts(rFonts);
                            textRpr.setSz(fontSize);
                            textRpr.setSzCs(fontSize);
                            textRpr.setB(bFalse);
                            textRun.setRPr(textRpr);

                            Text text = factory.createText();
                            text.setValue("4." + (j + 1) + " " + partName + "检测内容和方法");
                            text.setSpace("preserve");
                            textRun.getContent().add(text);
                            currentParagraph.getContent().add(textRun);

                            // 添加制表符
                            R tabRun = factory.createR();
                            RPr tabRpr = factory.createRPr();
                            tabRpr.setRFonts(rFonts);
                            tabRpr.setSz(fontSize);
                            tabRpr.setSzCs(fontSize);
                            tabRpr.setB(bFalse);
                            tabRun.setRPr(tabRpr);

                            Text tabText = factory.createText();
                            tabText.setValue("\t");
                            tabText.setSpace("preserve");
                            tabRun.getContent().add(tabText);
                            currentParagraph.getContent().add(tabRun);

                            // 添加页码
                            R pageRun = factory.createR();
                            RPr pageRpr = factory.createRPr();
                            pageRpr.setRFonts(rFonts);
                            pageRpr.setSz(fontSize);
                            pageRpr.setSzCs(fontSize);
                            pageRpr.setB(bFalse);
                            pageRun.setRPr(pageRpr);

                            Text pageText = factory.createText();
                            pageText.setValue(String.valueOf(startPageNum + j));
                            pageText.setSpace("preserve");
                            pageRun.getContent().add(pageText);
                            currentParagraph.getContent().add(pageRun);
                        }

                        log.info("目录生成完成，共生成{}个目录项", filteredParts.size());
                        String maxNo = "4." + (filteredParts.size() + 1);
                        WordDocumentUtil.replacePlaceholder(wordMLPackage, "maxNo", maxNo);

                        // 生成子目录
                        generateChildIndexContents(wordMLPackage, maxNo);

                        return; // 成功找到并替换占位符，不需要继续处理
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成目录失败", e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "partIndexList", "");
        }
    }



    /**
     * 生成子目录（技术状况评定方法子项）
     * @param wordMLPackage Word文档包
     * @param maxNo 最大编号（如"4.5"）
     */
    private void generateChildIndexContents(WordprocessingMLPackage wordMLPackage, String maxNo) {
        try {
            // 定义子目录项：分项设施评定方法、分部设施评定方法、机电设施技术状况评定方法
            List<String> childItems = Lists.newArrayList(
                    maxNo + ".1分项设施评定方法",
                    maxNo + ".2分部设施评定方法",
                    maxNo + ".3机电设施技术状况评定方法"
            );

            // 对应的页码（根据实际情况调整）
            List<Integer> pageNumbers = Lists.newArrayList(19, 26, 26);

            // 获取主文档部分和占位符段落
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;

                    // 检查段落是否包含占位符
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);
                    if (paragraphText.contains("${childIndexList}")) {
                        // 清空当前段落的内容
                        paragraph.getContent().clear();

                        // 设置段落属性，包括制表位
                        PPr ppr = paragraph.getPPr();
                        if (ppr == null) {
                            ppr = factory.createPPr();
                            paragraph.setPPr(ppr);
                        }

                        // 创建制表位设置
                        Tabs tabs = factory.createTabs();
                        org.docx4j.wml.CTTabStop tabStop = factory.createCTTabStop();
                        tabStop.setVal(org.docx4j.wml.STTabJc.RIGHT); // 右对齐制表位
                        tabStop.setPos(BigInteger.valueOf(9100)); // 制表位位置
                        tabStop.setLeader(org.docx4j.wml.STTabTlc.DOT); // 设置引导线为点号
                        tabs.getTab().add(tabStop);
                        ppr.setTabs(tabs);

                        // 设置宋体小四字号的样式
                        RFonts rFonts = factory.createRFonts();
                        rFonts.setEastAsia("宋体");
                        rFonts.setAscii("宋体");
                        rFonts.setHAnsi("宋体");
                        rFonts.setCs("宋体");

                        HpsMeasure fontSize = factory.createHpsMeasure();
                        fontSize.setVal(BigInteger.valueOf(24)); // 小四=12pt=24半磅

                        // 设置不加粗
                        BooleanDefaultTrue bFalse = new BooleanDefaultTrue();
                        bFalse.setVal(false);

                        // 为每个子项创建单独的段落
                        for (int j = 0; j < childItems.size(); j++) {
                            String childItem = childItems.get(j);
                            Integer pageNum = pageNumbers.get(j);

                            // 如果不是第一个条目，创建新段落
                            P currentParagraph;
                            if (j == 0) {
                                currentParagraph = paragraph; // 使用当前段落
                            } else {
                                currentParagraph = factory.createP();
                                // 复制段落属性（包括制表位设置）
                                currentParagraph.setPPr(ppr);
                                // 将新段落插入到文档中
                                docObjects.add(i + j, currentParagraph);
                            }

                            // 添加缩进空格（模拟子级缩进）
                            R indentRun = factory.createR();
                            RPr indentRpr = factory.createRPr();
                            indentRpr.setRFonts(rFonts);
                            indentRpr.setSz(fontSize);
                            indentRpr.setSzCs(fontSize);
                            indentRpr.setB(bFalse);
                            indentRun.setRPr(indentRpr);

                            Text indentText = factory.createText();
                            indentText.setValue("    "); // 4个空格缩进
                            indentText.setSpace("preserve");
                            indentRun.getContent().add(indentText);
                            currentParagraph.getContent().add(indentRun);

                            // 添加目录文本内容
                            R textRun = factory.createR();
                            RPr textRpr = factory.createRPr();
                            textRpr.setRFonts(rFonts);
                            textRpr.setSz(fontSize);
                            textRpr.setSzCs(fontSize);
                            textRpr.setB(bFalse);
                            textRun.setRPr(textRpr);

                            Text text = factory.createText();
                            text.setValue(childItem);
                            text.setSpace("preserve");
                            textRun.getContent().add(text);
                            currentParagraph.getContent().add(textRun);

                            // 添加制表符
                            R tabRun = factory.createR();
                            RPr tabRpr = factory.createRPr();
                            tabRpr.setRFonts(rFonts);
                            tabRpr.setSz(fontSize);
                            tabRpr.setSzCs(fontSize);
                            tabRpr.setB(bFalse);
                            tabRun.setRPr(tabRpr);

                            Text tabText = factory.createText();
                            tabText.setValue("\t");
                            tabText.setSpace("preserve");
                            tabRun.getContent().add(tabText);
                            currentParagraph.getContent().add(tabRun);

                            // 添加页码
                            R pageRun = factory.createR();
                            RPr pageRpr = factory.createRPr();
                            pageRpr.setRFonts(rFonts);
                            pageRpr.setSz(fontSize);
                            pageRpr.setSzCs(fontSize);
                            pageRpr.setB(bFalse);
                            pageRun.setRPr(pageRpr);

                            Text pageText = factory.createText();
                            pageText.setValue(String.valueOf(pageNum));
                            pageText.setSpace("preserve");
                            pageRun.getContent().add(pageText);
                            currentParagraph.getContent().add(pageRun);
                        }

                        log.info("子目录生成完成，共生成{}个子项", childItems.size());
                        return; // 成功找到并替换占位符
                    }
                }
            }

            // 如果没有找到占位符，替换为空
            log.warn("未找到${childIndexList}占位符");
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "childIndexList", "");

        } catch (Exception e) {
            log.error("生成子目录失败", e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "childIndexList", "");
        }
    }



    /**
     * 更新段落文本内容
     * @param paragraph 段落对象
     * @param newText 新的文本内容
     */
    private void updateParagraphText(P paragraph, String newText) {
        try {
            // 清空当前段落内容
            paragraph.getContent().clear();

            // 创建新的Run和Text对象
            ObjectFactory factory = Context.getWmlObjectFactory();
            R run = factory.createR();
            Text text = factory.createText();
            text.setValue(newText);

            // 检查是否是4.x检测内容和方法的二级标题，如果是则设置黑体四号字体
            if (newText.matches("^\\s*4\\.[0-9]+\\s+.*检测内容和方法\\s*$")) {
                // 设置字体为黑体四号
                RPr rpr = factory.createRPr();

                // 设置字体为黑体
                RFonts rFonts = factory.createRFonts();
                rFonts.setEastAsia("黑体");
                rFonts.setHAnsi("黑体");
                rpr.setRFonts(rFonts);

                // 设置字号为四号（14pt = 28半磅）
                HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                fontSizeMeasure.setVal(BigInteger.valueOf(28));
                rpr.setSz(fontSizeMeasure);
                rpr.setSzCs(fontSizeMeasure);

                // 应用字体设置到Run
                run.setRPr(rpr);

                log.debug("为二级标题设置黑体四号字体: {}", newText);
            }

            // 将Text添加到Run，然后将Run添加到段落
            run.getContent().add(text);
            paragraph.getContent().add(run);
        } catch (Exception e) {
            log.error("更新段落文本失败", e);
        }
    }

    /**
     * 根据分部对应的分项集合,生成对应的重点关注设施汇总表格
     * @param wordMLPackage
     * @param partImportantMap
     */
    private void generatePartImportantTable(WordprocessingMLPackage wordMLPackage, Map<String, List<String>> partImportantMap) {
        try {
            log.info("开始生成重点关注设施汇总表格");
            if (partImportantMap == null || partImportantMap.isEmpty()) {
                log.info("没有需要重点关注的设施，替换占位符为空");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "importantTable", "");
                return;
            }

            // 过滤掉空值，并计算实际行数
            Map<String, List<String>> filteredMap = new HashMap<>();
            for (Map.Entry<String, List<String>> entry : partImportantMap.entrySet()) {
                List<String> itemNames = entry.getValue();
                if (itemNames != null && !itemNames.isEmpty()) {
                    // 对分项名称去重
                    itemNames = itemNames.stream().distinct().collect(Collectors.toList());
                    filteredMap.put(entry.getKey(), itemNames);
                }
            }

            if (filteredMap.isEmpty()) {
                log.info("过滤后没有需要重点关注的设施，替换占位符为空");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "importantTable", "");
                return;
            }

            log.info("过滤后的重点关注设施数据: {}", filteredMap);

            // 创建表格 - 行数为分部数量 + 表头行 + 备注行（移除第一行标题）
            int rows = filteredMap.size() + 2; // 表头行 + 数据行 + 备注行
            int cols = 3; // 序号、分部设施、需重点分项设施名称

            // 使用WordDocumentUtil创建表格
            Tbl table = WordDocumentUtil.createTable(rows, cols);

            // 设置表格列宽
            TblGrid tblGrid = table.getTblGrid();
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(1000)); // 序号列宽度
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(2500)); // 分部设施列宽度
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(5500)); // 需重点分项设施名称列宽度

            // 设置表头行（第一行） - 使用false参数取消背景色
            WordDocumentUtil.setCellText(table, 0, 0, "序号", 9, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "分部设施", 9, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "需重点分项设施名称", 9, true, false, 2);

            // 设置表格重复表头
            setTableHeaderRepeat(table, 1);

            // 填充数据行
            int rowIndex = 1; // 从表格第二行开始填充数据
            int serialNumber = 1; // 序号从1开始

            // 按照分部名称排序，确保输出顺序一致
            List<Map.Entry<String, List<String>>> sortedEntries = filteredMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toList());

            for (Map.Entry<String, List<String>> entry : sortedEntries) {
                String partName = entry.getKey();
                List<String> itemNames = entry.getValue();

                log.info("处理分部: {}, 分项数量: {}", partName, itemNames.size());

                // 添加序号单元格
                WordDocumentUtil.setCellText(table, rowIndex, 0, String.valueOf(serialNumber), 9, false, false, 2);

                // 添加分部名称单元格
                WordDocumentUtil.setCellText(table, rowIndex, 1, partName, 9, false, false, 2);

                // 添加分项设施名称单元格 - 使用顿号拼接
                String itemNamesStr = String.join("、", itemNames);
                WordDocumentUtil.setCellText(table, rowIndex, 2, itemNamesStr, 9, false, false, 1); // 左对齐

                log.info("添加数据行 {}: 序号={}, 分部={}, 分项={}", rowIndex, serialNumber, partName, itemNamesStr);

                rowIndex++;
                serialNumber++;
            }

            // 添加备注行
            WordDocumentUtil.setCellText(table, rows - 1, 0, "备注：分项设施检测项目得分小于80的设备均需要重点关注", 9, false, false, 1); // 左对齐
            // 合并备注行单元格
            WordDocumentUtil.mergeRowCellsSimple(table, rows - 1, 0, cols);

            // 先对除备注行外的所有行设置宋体五号字体
            forceApplySize5FontToTableContentExceptLastRow(table);

            // 为表格内容行（除表头行和备注行外）设置1.5倍行距
            setContentRowsLineSpacing(table, 1, rows - 2); // 从第1行（索引1）到倒数第二行

            // 特别设置备注行为宋体小五号字体
            setRemarkRowFontToSmall5(table, rows - 1);

            log.info("表格创建完成，总行数: {}, 数据行数: {}", rows, filteredMap.size());

            // 直接替换占位符为表格，不生成表格标题
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();

            boolean replacementDone = false;
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();
                    if (paragraphText.contains("${importantTable}")) {
                        // 直接替换为表格，不添加标题
                        docObjects.set(i, table);
                        replacementDone = true;
                        log.info("成功替换importantTable占位符为表格，无标题");
                        break;
                    }
                }
            }

            if (!replacementDone) {
                // 如果没有找到占位符，尝试普通文本替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "importantTable", "");
                log.warn("未找到importantTable占位符");
            }
            log.info("重点关注设施汇总表格生成完成");
        } catch (Exception e) {
            log.error("生成重点关注设施汇总表格失败", e);
            // 出错时替换为空字符串
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "importantTable", "");
        }
    }

    /**
     * 为指定范围的表格行设置1.5倍行距
     * @param table 表格对象
     * @param startRowIndex 开始行索引（包含）
     * @param endRowIndex 结束行索引（包含）
     */
    private void setContentRowsLineSpacing(Tbl table, int startRowIndex, int endRowIndex) {
        try {
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 获取表格所有行
            List<Object> rows = table.getContent();

            for (int rowIndex = startRowIndex; rowIndex <= endRowIndex && rowIndex < rows.size(); rowIndex++) {
                Object rowObj = rows.get(rowIndex);
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    // 处理该行的所有单元格
                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;

                            // 获取单元格内的所有段落
                            List<Object> cellContent = cell.getContent();
                            for (Object contentObj : cellContent) {
                                if (contentObj instanceof P) {
                                    P paragraph = (P) contentObj;

                                    // 获取或创建段落属性
                                    PPr pPr = paragraph.getPPr();
                                    if (pPr == null) {
                                        pPr = factory.createPPr();
                                        paragraph.setPPr(pPr);
                                    }

                                    // 设置1.5倍行距
                                    PPrBase.Spacing spacing = factory.createPPrBaseSpacing();
                                    spacing.setLine(BigInteger.valueOf(360)); // 1.5倍行距 = 360 (240 * 1.5)
                                    spacing.setLineRule(STLineSpacingRule.AUTO);
                                    pPr.setSpacing(spacing);
                                }
                            }
                        }
                    }
                }
            }

            log.debug("成功为表格第{}行到第{}行设置1.5倍行距", startRowIndex, endRowIndex);
        } catch (Exception e) {
            log.error("设置表格行距失败", e);
        }
    }

    private void generatePartItemPhoto(WordprocessingMLPackage wordMLPackage, TunnelInfo tunnelInfo) {
        try {
            Map<String, String> partCodePlaceholderMap = new HashMap<>();
            // 各个分部编号对应的照片占位符
            partCodePlaceholderMap.put("1", "partOneQuestionPicture");
            partCodePlaceholderMap.put("2", "partTwoQuestionPicture");
            partCodePlaceholderMap.put("3", "partThreeQuestionPicture");
            partCodePlaceholderMap.put("4", "partFourQuestionPicture");
            partCodePlaceholderMap.put("5", "partFiveQuestionPicture");

            // 分部名称映射，用于图片标题显示
            Map<String, String> partCodeNameMap = new HashMap<>();
            partCodeNameMap.put("1", "供配电设施");
            partCodeNameMap.put("2", "照明设施");
            partCodeNameMap.put("3", "通风设施");
            partCodeNameMap.put("4", "消防设施");
            partCodeNameMap.put("5", "监控与通信设施");

            Long tunnelId = tunnelInfo.getId();

            // 查询该隧道的所有检查项
            TunnelCheck tunnelCheckQuery = new TunnelCheck();
            tunnelCheckQuery.setTunnelId(tunnelId);
            List<TunnelCheck> tunnelCheckList = tunnelCheckMapper.selectTunnelCheckList(tunnelCheckQuery);

            // 按partCode分组
            Map<String, List<TunnelCheck>> partGroupMap = tunnelCheckList.stream()
                    .collect(Collectors.groupingBy(TunnelCheck::getPartCode));

            // 全局图片计数器，用于生成连续的图片序号
            AtomicInteger globalImageCounter = new AtomicInteger(1);

            // 处理各个分部的图片
            for (String partCode : partCodePlaceholderMap.keySet()) {
                String placeholder = partCodePlaceholderMap.get(partCode);
                String partName = partCodeNameMap.get(partCode);

                // 获取该分部的所有检查项
                List<TunnelCheck> partChecks = partGroupMap.getOrDefault(partCode, new ArrayList<>());

                // 处理该分部的图片
                processPicturesForPartWithGlobalCounter(wordMLPackage, partChecks, placeholder, partCode, partName, globalImageCounter);
            }
        } catch (Exception e) {
            log.error("生成隧道检测分项设施照片失败", e);
        }
    }


    @Value("${spring.profiles.active}")
    private String active;
    /**
     * 将图片地址替换为内网地址
     * @param imgUrl
     * @return
     */
    private String replaceImageUrl(String imgUrl) {
        if(StringUtils.isEmpty(imgUrl)){
            return "";
        }
        if (Objects.equals( "pro", active)) {
            imgUrl = imgUrl.replace("shanghai", "shanghai-internal");
        }
        return imgUrl;
    }

    /**
     * 处理指定分部的所有图片，使用全局计数器来生成连续的图片序号
     * 优化：相同缺陷只保留一张图片，设置图片尺寸为高5cm宽6.67cm，文字为黑体五号
     *
     * @param wordMLPackage Word文档包
     * @param partChecks 分部检查项列表
     * @param placeholder 要替换的占位符
     * @param partCode 分部代码
     * @param partName 分部名称
     * @param globalImageCounter 全局图片计数器
     */
    private void processPicturesForPartWithGlobalCounter(WordprocessingMLPackage wordMLPackage, List<TunnelCheck> partChecks,
                                                         String placeholder, String partCode, String partName, AtomicInteger globalImageCounter) {
        try {
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            ObjectFactory factory = new ObjectFactory();

            // 用于记录已处理的缺陷，确保同一缺陷只保留一张图片
            Set<String> processedQuestions = new HashSet<>();

            // 收集图片信息，按缺陷去重
            List<Map<String, Object>> allPictures = new ArrayList<>();

            for (TunnelCheck check : partChecks) {
                String questionDesc = check.getQuestionDesc();

                // 如果该缺陷已经处理过，跳过后续图片
                if (StringUtils.isNotEmpty(questionDesc) && processedQuestions.contains(questionDesc)) {
                    continue;
                }

                // 按优先级选择图片：picUrl1 > picUrl2 > picUrl3
                String selectedUrl = null;
                if (StringUtils.isNotEmpty(check.getPicUrl1())) {
                    selectedUrl = check.getPicUrl1();
                } else if (StringUtils.isNotEmpty(check.getPicUrl2())) {
                    selectedUrl = check.getPicUrl2();
                } else if (StringUtils.isNotEmpty(check.getPicUrl3())) {
                    selectedUrl = check.getPicUrl3();
                }

                // 如果找到了图片，添加到列表中并记录该缺陷
                if (selectedUrl != null) {
                    Map<String, Object> picInfo = new HashMap<>();
                    picInfo.put("url", selectedUrl);
                    picInfo.put("itemName", check.getItemName());
                    picInfo.put("questionDesc", questionDesc);
                    allPictures.add(picInfo);

                    // 记录已处理的缺陷
                    if (StringUtils.isNotEmpty(questionDesc)) {
                        processedQuestions.add(questionDesc);
                    }
                }
            }

            // 如果没有图片，删除占位符段落
            if (allPictures.isEmpty()) {
                log.info("分部[{}]没有图片，删除占位符段落", partName);
                // 删除包含占位符的段落
                WordDocumentUtil.deletePlaceholderParagraph(wordMLPackage, placeholder);
                return;
            }

            log.info("分部[{}]共找到{}个缺陷，去重后保留{}张图片", partName, processedQuestions.size(), allPictures.size());

            // 用于存储生成的内容
            List<Object> pictureContent = new ArrayList<>();

            // 在图片内容开始前添加"测试照片"标题（居左，黑体小四，不在导航视图中显示）
            P titleParagraph = createSimpleTitleParagraph("测试照片:");
            if (titleParagraph != null) {
                pictureContent.add(titleParagraph);
            }

            // 每行放置两张图片，计算需要的行数
            int rows = (int) Math.ceil(allPictures.size() / 2.0);

            // 处理所有图片，每行两张
            for (int row = 0; row < rows; row++) {
                // 创建表格，2列，每列一张图片
                Tbl pictureTable = factory.createTbl();

                // 设置表格属性
                TblPr tblPr = factory.createTblPr();
                TblWidth tblWidth = factory.createTblWidth();
                tblWidth.setType("dxa");
                tblWidth.setW(BigInteger.valueOf(9600)); // 表格宽度适应新图片尺寸：6.67cm * 2 + 间距 ≈ 9600 twips
                tblPr.setTblW(tblWidth);

                // 设置表格居中
                Jc tableJc = factory.createJc();
                tableJc.setVal(JcEnumeration.CENTER);
                tblPr.setJc(tableJc);

                pictureTable.setTblPr(tblPr);

                // 创建表格行
                Tr tr = factory.createTr();

                // 计算当前行的图片数量
                int startIdx = row * 2;
                int endIdx = Math.min(startIdx + 2, allPictures.size());
                int picturesInCurrentRow = endIdx - startIdx;

                // 根据图片数量设置表格列数和列宽
                TblGrid tblGrid = factory.createTblGrid();
                if (picturesInCurrentRow == 1) {
                    // 只有一张图片时，创建单列表格
                    TblGridCol col = factory.createTblGridCol();
                    col.setW(BigInteger.valueOf(4800)); // 单列宽度：6.67cm = 约4800 twips
                    tblGrid.getGridCol().add(col);
                } else {
                    // 有两张图片时，创建两列表格
                    TblGridCol col1 = factory.createTblGridCol();
                    col1.setW(BigInteger.valueOf(4800)); // 第一列宽度：6.67cm = 约4800 twips
                    TblGridCol col2 = factory.createTblGridCol();
                    col2.setW(BigInteger.valueOf(4800)); // 第二列宽度：6.67cm = 约4800 twips
                    tblGrid.getGridCol().add(col1);
                    tblGrid.getGridCol().add(col2);
                }
                pictureTable.setTblGrid(tblGrid);

                // 处理当前行的每张图片
                for (int i = startIdx; i < endIdx; i++) {
                    Map<String, Object> pictureInfo = allPictures.get(i);
                    String imageUrl = (String) pictureInfo.get("url");
                    String itemName = (String) pictureInfo.get("itemName");
                    String questionDesc = (String) pictureInfo.get("questionDesc");

                    // 使用全局计数器获取当前图片的序号
                    int currentImageNumber = globalImageCounter.getAndIncrement();

                    // 创建单元格
                    Tc tc = factory.createTc();

                    // 设置单元格属性
                    TcPr tcPr = factory.createTcPr();

                    // 使用正确的边距设置方式
                    TblWidth tcWidth = factory.createTblWidth();
                    tcWidth.setType("dxa");

                    // 根据图片数量设置单元格宽度
                    if (picturesInCurrentRow == 1) {
                        tcWidth.setW(BigInteger.valueOf(4800)); // 单列宽度：6.67cm = 约4800 twips
                    } else {
                        tcWidth.setW(BigInteger.valueOf(4800)); // 每列宽度：6.67cm = 约4800 twips
                    }

                    tcPr.setTcW(tcWidth);
                    tc.setTcPr(tcPr);

                    try {
                        // 添加图片
                        P picParagraph = factory.createP();
                        PPr picPPr = factory.createPPr();
                        Jc jc = factory.createJc();
                        jc.setVal(JcEnumeration.CENTER); // 居中对齐
                        picPPr.setJc(jc);
                        picParagraph.setPPr(picPPr);

                        // 处理图片URL
                        String baseUrl = "/public"; // 图片相对路径的基础部分
                        String imagePath = imageUrl;
                        InputStream imageStream = null;

                        // 处理网络图片URL
                        if (imagePath.startsWith("http")) {
                            log.info("从网络加载图片: {}", imagePath);
                            try {
                                java.net.URL url = new java.net.URL(replaceImageUrl(imagePath));
                                imageStream = url.openStream();
                            } catch (Exception e) {
                                log.warn("无法从网络加载图片: {}", imagePath, e);
                            }
                        }

                        // 如果不是网络图片或网络图片加载失败，尝试本地加载
                        if (imageStream == null) {
                            // 对于本地路径，如果URL包含public路径，提取相对路径
                            if (imagePath.startsWith("http")) {
                                int publicIndex = imagePath.indexOf("/public");
                                if (publicIndex >= 0) {
                                    imagePath = imagePath.substring(publicIndex);
                                } else {
                                    // 网络图片且没有/public路径，可能加载失败，使用默认图片
                                    imagePath = "/public/static/image_not_found.png";
                                }
                            }

                            // 确保路径以/开头
                            if (!imagePath.startsWith("/")) {
                                imagePath = "/" + imagePath;
                            }

                            // 尝试从类路径加载
                            imageStream = this.getClass().getResourceAsStream(imagePath);

                            // 如果类路径加载失败，尝试使用文件路径
                            if (imageStream == null) {
                                try {
                                    java.io.File file = new java.io.File(imagePath);
                                    if (file.exists()) {
                                        imageStream = new java.io.FileInputStream(file);
                                    }
                                } catch (Exception e) {
                                    log.warn("无法加载本地图片: {}", imagePath, e);
                                }
                            }
                        }

                        // 最后尝试加载默认图片
                        if (imageStream == null) {
                            log.warn("图片加载失败: {}, 使用默认图片", imageUrl);
                            imagePath = "/public/static/image_not_found.png";
                            imageStream = this.getClass().getResourceAsStream(imagePath);

                            if (imageStream == null) {
                                log.error("默认图片也无法加载，跳过此图片");
                                continue;
                            }
                        }

                        // 获取图片字节并创建图片部分
                        byte[] imageBytes = IOUtils.toByteArray(imageStream);
                        imageStream.close();

                        // 创建图片部分
                        org.docx4j.openpackaging.parts.WordprocessingML.BinaryPartAbstractImage imagePart =
                                org.docx4j.openpackaging.parts.WordprocessingML.BinaryPartAbstractImage
                                        .createImagePart(wordMLPackage, imageBytes);

                        // 设置图片属性 - 使用全局序号
                        int docPrId = currentImageNumber * 2 + 1;
                        int cNvPrId = currentImageNumber * 2 + 2;
                        org.docx4j.dml.wordprocessingDrawing.Inline inline =
                                imagePart.createImageInline("Image" + currentImageNumber,
                                        "缺陷图片" + currentImageNumber, docPrId, cNvPrId, false);

                        // 设置图片尺寸：宽6.67厘米，高5厘米
                        // 1厘米 = 360000 EMU (English Metric Units)
                        inline.getExtent().setCx(2401200); // 宽度：6.67cm = 6.67 * 360000 = 2401200 EMU
                        inline.getExtent().setCy(1800000); // 高度：5cm = 5 * 360000 = 1800000 EMU

                        // 创建绘图对象
                        org.docx4j.wml.Drawing drawing = factory.createDrawing();
                        drawing.getAnchorOrInline().add(inline);

                        // 添加绘图到段落
                        R picRun = factory.createR();
                        picRun.getContent().add(drawing);
                        picParagraph.getContent().add(picRun);

                        // 添加图片段落到单元格
                        tc.getContent().add(picParagraph);

                        // 添加图片标题段落
                        P captionParagraph = factory.createP();
                        PPr captionPPr = factory.createPPr();
                        Jc captionJc = factory.createJc();
                        captionJc.setVal(JcEnumeration.CENTER); // 居中对齐
                        captionPPr.setJc(captionJc);
                        captionParagraph.setPPr(captionPPr);

                        R captionRun = factory.createR();

                        // 设置文字样式为黑体五号
                        RPr captionRPr = factory.createRPr();

                        // 设置字体为黑体
                        RFonts rFonts = factory.createRFonts();
                        rFonts.setEastAsia("黑体");
                        rFonts.setHAnsi("黑体");
                        captionRPr.setRFonts(rFonts);

                        // 设置字号为五号（10.5pt = 21半磅）
                        HpsMeasure fontSize = factory.createHpsMeasure();
                        fontSize.setVal(BigInteger.valueOf(21));
                        captionRPr.setSz(fontSize);
                        captionRPr.setSzCs(fontSize);

                        captionRun.setRPr(captionRPr);

                        Text captionText = factory.createText();

                        // 格式化图片标题 - 使用全局序号
                        String caption = String.format("图6-%d %s%s",
                                currentImageNumber,
                                itemName != null ? itemName : "",
                                questionDesc != null ? questionDesc : "");

                        captionText.setValue(caption);
                        captionRun.getContent().add(captionText);
                        captionParagraph.getContent().add(captionRun);

                        // 添加标题段落到单元格
                        tc.getContent().add(captionParagraph);
                    } catch (Exception e) {
                        log.error("处理图片失败: {}", imageUrl, e);

                        // 添加错误信息段落
                        P errorParagraph = factory.createP();
                        R errorRun = factory.createR();
                        Text errorText = factory.createText();
                        errorText.setValue("[图片加载失败]");
                        errorRun.getContent().add(errorText);
                        errorParagraph.getContent().add(errorRun);
                        tc.getContent().add(errorParagraph);
                    }

                    // 添加单元格到行
                    tr.getContent().add(tc);
                }

                // 添加行到表格
                pictureTable.getContent().add(tr);

                // 添加表格到内容列表
                pictureContent.add(pictureTable);
            }

            // 查找并替换占位符
            boolean replacementDone = false;
            List<Object> docObjects = mainDocumentPart.getContent();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains("${" + placeholder + "}")) {
                        // 替换占位符段落为一系列图片内容
                        docObjects.remove(i);
                        docObjects.addAll(i, pictureContent);
                        replacementDone = true;
                        log.info("成功替换${{{}}}}占位符为分部[{}]图片内容", placeholder, partName);
                        break;
                    }
                }
            }

            if (!replacementDone) {
                log.warn("未找到${{{}}}}占位符，尝试作为字符串替换", placeholder);
                // 如果没有找到占位符段落，尝试作为普通文本替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, placeholder, "");
            }
        } catch (Exception e) {
            log.error("处理分部[{}]图片失败", partName, e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, placeholder, "");
        }
    }

    /**
     * 生成各个分部问题描述和建议措施表格（优化版）
     * @param wordMLPackage Word文档包
     * @param questionTableList 分项备注映射
     * @return 有数据的分部名称列表
     */
    private List<String> generateQuestionRemarkAndSuggestion(WordprocessingMLPackage wordMLPackage, List<QuestionTable> questionTableList,Long tunnelId) {
        try {
            // 分部代码对应的占位符映射和分部名称映射
            Map<String, String> partCodePlaceholderMap = new LinkedHashMap<>();
            Map<String, String> partCodeNameMap = new LinkedHashMap<>();

            // 使用LinkedHashMap保持顺序
            partCodePlaceholderMap.put("1", "partOneQuestionTable");
            partCodePlaceholderMap.put("2", "partTwoQuestionTable");
            partCodePlaceholderMap.put("3", "partThreeQuestionTable");
            partCodePlaceholderMap.put("4", "partFourQuestionTable");
            partCodePlaceholderMap.put("5", "partFiveQuestionTable");

            partCodeNameMap.put("1", "供配电设施");
            partCodeNameMap.put("2", "照明设施");
            partCodeNameMap.put("3", "通风设施");
            partCodeNameMap.put("4", "消防设施");
            partCodeNameMap.put("5", "监控与通信设施");

            // 按分部代码对数据进行分组，优化数据处理


                        // 收集有数据的分部名称
            List<String> partNamesWithData = new ArrayList<>();
            
            // 从 questionTableList 中提取有数据的分部名称
            Set<String> partNamesWithDataSet = questionTableList.stream()
                    .map(QuestionTable::getPartName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            partNamesWithData.addAll(partNamesWithDataSet);

            // 动态分配连续的章节编号和附表编号
            int sectionIndex = 1;
            int tableIndex = 1; // 附表编号计数器

            // 按顺序处理每个分部
            for (String partCode : partCodePlaceholderMap.keySet()) {
                String placeholder = partCodePlaceholderMap.get(partCode);
                String partName = partCodeNameMap.get(partCode);

                // 检查该分部是否有数据
                boolean hasData = questionTableList.stream()
                        .anyMatch(q -> partName.equals(q.getPartName()));
                
                // 如果该分部没有数据，不生成任何内容
                if (!hasData) {
                    log.info("分部 {} 没有数据，不生成任何内容", partName);
                    WordDocumentUtil.deletePlaceholderParagraph(wordMLPackage, placeholder);
                    continue;
                }

                // 生成连续的章节编号
                String sectionNumber = "6.1." + sectionIndex;
                sectionIndex++;

                log.info("开始处理分部: {} - {}", sectionNumber, partName);

                // 创建章节标题（如：6.1.2 照明设施主要问题及建议措施）
                String sectionTitle = sectionNumber + " " + partName + "主要问题及建议措施";

                // 创建该分部的表格，包含所有分项，传递连续的附表编号用于备注
                Tbl questionTable = createQuestionTable(partName, questionTableList, tableIndex, tunnelId);

                // 生成动态的表格标题
                String dynamicTableTitle = generateDynamicTableTitle(partName, tableIndex);
                
                // 替换占位符为章节标题和表格（使用动态标题）
                replaceTablePlaceholderWithDynamicTitle(wordMLPackage, placeholder, sectionTitle, questionTable, dynamicTableTitle);

                // 递增附表编号
                tableIndex++;
            }

            log.info("问题描述和建议措施表格生成完成，共有 {} 个分部有数据", partNamesWithData.size());
            return partNamesWithData;
        } catch (Exception e) {
            log.error("生成问题描述和建议措施表格失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建问题和建议表格
     * @param partName 分部名称
     * @param questionTableList 分项数据映射
     * @return 表格对象
     */
        private Tbl createQuestionTable(String partName, List<QuestionTable> questionTableList, int sectionNumber, Long tunnelId) {
        try {
            // 过滤出当前分部的数据
            List<QuestionTable> currentPartQuestions = questionTableList.stream()
                    .filter(q -> partName.equals(q.getPartName()))
                    .collect(Collectors.toList());

            // 如果没有数据，创建空表格
            if (currentPartQuestions.isEmpty()) {
                return createEmptyQuestionTable(partName, sectionNumber, tunnelId);
            }

            // 计算总行数：表头 + 问题行数 + 备注行
            // 每个QuestionTable对象对应一行数据
            int totalProblemRows = currentPartQuestions.size();
            int rowCount = 1 + totalProblemRows + 1;

            // 创建3列表格：序号、主要问题、建议措施
            Tbl table = WordDocumentUtil.createTable(rowCount, 3);

            // 设置固定列宽：序号1.2cm，主要问题和建议措施各固定为8.0cm (写死列宽)
            TblGrid tblGrid = table.getTblGrid();
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(680));   // 序号列：1.2cm = 680 twips
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(4536));  // 主要问题列：8.0cm = 4536 twips (写死固定列宽)
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(4536));  // 建议措施列：8.0cm = 4536 twips (写死固定列宽)

            // 设置表头 - 使用false参数取消背景色
            WordDocumentUtil.setCellText(table, 0, 0, "序号", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "主要问题", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "建议措施", 12, true, false, 2);

            // 优化表格样式：设置宋体五号字体和重复表头
            optimizeTableStyleWithSize5Font(table, 1);

            // 填充数据行
            int rowIndex = 1;
            int serialNumber = 1;

            // 创建对象工厂，用于创建Word元素
            ObjectFactory factory = new ObjectFactory();

            // 遍历当前分部的问题数据，每个QuestionTable对象对应一行
            for (QuestionTable questionTable : currentPartQuestions) {
                String remark = questionTable.getRemark();
                List<String> suggestions = questionTable.getSuggestions();

                // 确保问题描述不为空
                String validRemark = (remark != null && !remark.trim().isEmpty()) ? remark.trim() : "无";
                // 添加 itemCode 信息用于标识
//                if (questionTable.getItemCode() != null) {
//                    validRemark = "[" + questionTable.getItemCode() + "] " + validRemark;
//                }

                // 处理建议措施列表：去重并合并为单个字符串
                String combinedSuggestions = "无";
                if (suggestions != null && !suggestions.isEmpty()) {
                    List<String> validSuggestions = suggestions.stream()
                            .filter(s -> s != null && !s.trim().isEmpty())
                            .map(String::trim)
                            .distinct() // 去重
                            .collect(Collectors.toList());
                    
                    if (!validSuggestions.isEmpty()) {
                        combinedSuggestions = String.join("\n", validSuggestions);
                    }
                }

                // 序号单元格
                Tc serialCell = getTableCell(table, rowIndex, 0);
                P serialP = factory.createP();
                R serialR = factory.createR();

                // 设置宋体五号字体
                RPr serialRpr = factory.createRPr();
                RFonts serialRFonts = factory.createRFonts();
                serialRFonts.setEastAsia("宋体");
                serialRFonts.setHAnsi("宋体");
                serialRpr.setRFonts(serialRFonts);
                HpsMeasure serialFontSizeMeasure = factory.createHpsMeasure();
                serialFontSizeMeasure.setVal(BigInteger.valueOf(21)); // 五号字体（10.5pt = 21半磅）
                serialRpr.setSz(serialFontSizeMeasure);
                serialRpr.setSzCs(serialFontSizeMeasure);
                serialR.setRPr(serialRpr);

                Text serialText = factory.createText();
                serialText.setValue(String.valueOf(serialNumber));
                serialR.getContent().add(serialText);
                serialP.getContent().add(serialR);
                serialCell.getContent().clear();
                serialCell.getContent().add(serialP);

                // 主要问题单元格
                Tc remarkCell = getTableCell(table, rowIndex, 1);
                P remarkP = factory.createP();
                R remarkR = factory.createR();

                // 设置宋体五号字体
                RPr remarkRpr = factory.createRPr();
                RFonts remarkRFonts = factory.createRFonts();
                remarkRFonts.setEastAsia("宋体");
                remarkRFonts.setHAnsi("宋体");
                remarkRpr.setRFonts(remarkRFonts);
                HpsMeasure remarkFontSizeMeasure = factory.createHpsMeasure();
                remarkFontSizeMeasure.setVal(BigInteger.valueOf(21)); // 五号字体（10.5pt = 21半磅）
                remarkRpr.setSz(remarkFontSizeMeasure);
                remarkRpr.setSzCs(remarkFontSizeMeasure);
                remarkR.setRPr(remarkRpr);

                Text remarkText = factory.createText();
                remarkText.setValue(validRemark);
                remarkR.getContent().add(remarkText);
                remarkP.getContent().add(remarkR);
                remarkCell.getContent().clear();
                remarkCell.getContent().add(remarkP);

                // 建议措施单元格 - 使用换行显示多个建议
                Tc suggestionCell = getTableCell(table, rowIndex, 2);
                P suggestionP = createFormattedSuggestionParagraph(combinedSuggestions, factory);
                suggestionCell.getContent().clear();
                suggestionCell.getContent().add(suggestionP);

                rowIndex++;
                serialNumber++;
            }


            WordDocumentUtil.setCellText(table, rowIndex, 0, this.remarkText(partName,tunnelId), 12, false, false, 1);

            // 合并备注行的所有单元格
            WordDocumentUtil.mergeRowCellsSimple(table, rowIndex, 0, 2);

            // 设置备注行为宋体小五号字体
            setRemarkRowFontToSmall5(table, rowIndex);

            return table;
        } catch (Exception e) {
            log.error("创建问题和建议表格失败", e);
            return createEmptyQuestionTable(partName, sectionNumber,tunnelId);
        }
    }



    /**
     * 生成备注文本，根据实际存在的分部数据动态分配附表编号
     * @param currentPartName 实际有数据的分部名称列表
     * @param tunnelId 当前分部名称
     * @return 备注文本
     */
    private String remarkText(String currentPartName,Long tunnelId) {
        // 根据 partNamesWithData 中的实际顺序动态分配附表编号
        String attachmentNumber = "";
        FacilityInfo facilityInfo =new FacilityInfo();
        facilityInfo.setTunnelId(tunnelId);
        List<FacilityInfo> facilityInfoList = facilityInfoMapper.selectDistinctPart(facilityInfo);
        List<String> partNamesWithData = facilityInfoList.stream().map(FacilityInfo::getPartName).distinct().collect(Collectors.toList());
        if (partNamesWithData != null && !partNamesWithData.isEmpty()) {
            // 在 partNamesWithData 中查找当前分部的索引位置
            for (int i = 0; i < partNamesWithData.size(); i++) {
                if (partNamesWithData.get(i).equals(currentPartName)) {
                    attachmentNumber = String.valueOf(i + 1); // 附表编号从1开始
                    break;
                }
            }
        }
        String noteText = "备注：测试点位及详细检测结果参见附表" + attachmentNumber;
        return noteText;
    }

    /**
     * 创建空的问题和建议表格（当没有数据时使用）
     * @param partName 分部名称
     * @param sectionNumber 章节序号
     * @return 表格对象
     */
    private Tbl createEmptyQuestionTable(String partName, int sectionNumber, Long tunnelId) {
        try {
            // 创建3列表格：序号、主要问题、建议措施，3行：表头、空行、备注行
            Tbl table = WordDocumentUtil.createTable(3, 3);

            // 设置固定列宽：序号1.2cm，主要问题和建议措施各固定为8.0cm (写死列宽)
            TblGrid tblGrid = table.getTblGrid();
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(680));   // 序号列：1.2cm = 680 twips
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(4536));  // 主要问题列：8.0cm = 4536 twips (写死固定列宽)
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(4536));  // 建议措施列：8.0cm = 4536 twips (写死固定列宽)

            // 设置表头 - 使用false参数取消背景色
            WordDocumentUtil.setCellText(table, 0, 0, "序号", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "主要问题", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "建议措施", 12, true, false, 2);

            // 优化表格样式：设置宋体五号字体和重复表头
            optimizeTableStyleWithSize5Font(table, 1);

            // 空数据行
            WordDocumentUtil.setCellText(table, 1, 0, "1", 12, false, false, 2);
            WordDocumentUtil.setCellText(table, 1, 1, "无", 12, false, false, 2);
            WordDocumentUtil.setCellText(table, 1, 2, "无", 12, false, false, 2);

            // 添加备注行
            WordDocumentUtil.setCellText(table, 2, 0, this.remarkText(partName, tunnelId), 12, false, false, 1);

            // 合并备注行的所有单元格
            WordDocumentUtil.mergeRowCellsSimple(table, 2, 0, 2);

            // 设置备注行为宋体小五号字体
            setRemarkRowFontToSmall5(table, 2);

            return table;
        } catch (Exception e) {
            log.error("创建空的问题和建议表格失败", e);
            return null;
        }
    }

    /**
     * 格式化多行文本，
     * @param text 原始文本
     * @param factory 对象工厂
     * @return 已格式化的段落对象
     */
    private P formatMultilineTextAsP(String text, ObjectFactory factory) {
        if (text == null || text.trim().isEmpty()) {
            // 创建一个简单段落，显示"无"
            P p = factory.createP();
            R r = factory.createR();

            // 设置宋体五号字体
            RPr rpr = factory.createRPr();
            RFonts rFonts = factory.createRFonts();
            rFonts.setEastAsia("宋体");
            rFonts.setHAnsi("宋体");
            rpr.setRFonts(rFonts);
            HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
            fontSizeMeasure.setVal(BigInteger.valueOf(21)); // 五号字体（10.5pt = 21半磅）
            rpr.setSz(fontSizeMeasure);
            rpr.setSzCs(fontSizeMeasure);
            r.setRPr(rpr);

            Text t = factory.createText();
            t.setValue("无");
            r.getContent().add(t);
            p.getContent().add(r);
            return p;
        }

        P paragraph = factory.createP();
        // 直接添加文本内容，不再进行分割处理
        R r = factory.createR();

        // 设置宋体五号字体
        RPr rpr = factory.createRPr();
        RFonts rFonts = factory.createRFonts();
        rFonts.setEastAsia("宋体");
        rFonts.setHAnsi("宋体");
        rpr.setRFonts(rFonts);
        HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
        fontSizeMeasure.setVal(BigInteger.valueOf(21)); // 五号字体（10.5pt = 21半磅）
        rpr.setSz(fontSizeMeasure);
        rpr.setSzCs(fontSizeMeasure);
        r.setRPr(rpr);

        Text t = factory.createText();
        t.setValue(text);
        r.getContent().add(t);
        paragraph.getContent().add(r);
        return paragraph;
    }

    /**
     * 格式化建议列表，添加段落换行
     * @param suggestions 建议列表
     * @param factory 对象工厂
     * @return 已格式化的段落对象
     */
    private P formatSuggestionsListAsP(List<String> suggestions, ObjectFactory factory) {
        if (suggestions == null || suggestions.isEmpty()) {
            // 创建一个简单段落，显示"无"
            P p = factory.createP();
            R r = factory.createR();

            // 设置宋体五号字体
            RPr rpr = factory.createRPr();
            RFonts rFonts = factory.createRFonts();
            rFonts.setEastAsia("宋体");
            rFonts.setHAnsi("宋体");
            rpr.setRFonts(rFonts);
            HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
            fontSizeMeasure.setVal(BigInteger.valueOf(21)); // 五号字体（10.5pt = 21半磅）
            rpr.setSz(fontSizeMeasure);
            rpr.setSzCs(fontSizeMeasure);
            r.setRPr(rpr);

            Text t = factory.createText();
            t.setValue("无");
            r.getContent().add(t);
            p.getContent().add(r);
            return p;
        }

        P paragraph = factory.createP();
        boolean isFirst = true;

        for (int i = 0; i < suggestions.size(); i++) {
            String suggestion = suggestions.get(i).trim();
            if (suggestion.isEmpty()) {
                continue;
            }

            if (!isFirst) {
                // 添加换行符
                R rBr = factory.createR();

                // 设置宋体五号字体（换行符也需要字体设置）
                RPr rprBr = factory.createRPr();
                RFonts rFontsBr = factory.createRFonts();
                rFontsBr.setEastAsia("宋体");
                rFontsBr.setHAnsi("宋体");
                rprBr.setRFonts(rFontsBr);
                HpsMeasure fontSizeMeasureBr = factory.createHpsMeasure();
                fontSizeMeasureBr.setVal(BigInteger.valueOf(21));
                rprBr.setSz(fontSizeMeasureBr);
                rprBr.setSzCs(fontSizeMeasureBr);
                rBr.setRPr(rprBr);

                Br br = factory.createBr();
                rBr.getContent().add(br);
                paragraph.getContent().add(rBr);
            } else {
                isFirst = false;
            }

            // 检查建议是否已经包含序号格式
            if (!suggestion.matches("^\\s*\\d+[、.].*")) {
                // 没有序号，添加序号
                suggestion = (i + 1) + "、" + suggestion;
            }

            // 添加文本
            R r = factory.createR();

            // 设置宋体五号字体
            RPr rpr = factory.createRPr();
            RFonts rFonts = factory.createRFonts();
            rFonts.setEastAsia("宋体");
            rFonts.setHAnsi("宋体");
            rpr.setRFonts(rFonts);
            HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
            fontSizeMeasure.setVal(BigInteger.valueOf(21)); // 五号字体（10.5pt = 21半磅）
            rpr.setSz(fontSizeMeasure);
            rpr.setSzCs(fontSizeMeasure);
            r.setRPr(rpr);

            Text t = factory.createText();
            t.setValue(suggestion);
            r.getContent().add(t);
            paragraph.getContent().add(r);
        }

        return paragraph;
    }

    /**
     * 创建格式化的建议措施段落，支持在Word中真正换行
     * @param suggestion 建议措施文本
     * @param factory 对象工厂
     * @return 格式化后的段落对象
     */
    private P createFormattedSuggestionParagraph(String suggestion, ObjectFactory factory) {
        P paragraph = factory.createP();

        if (suggestion == null || suggestion.trim().isEmpty() || "无".equals(suggestion.trim())) {
            R r = factory.createR();
            RPr rpr = factory.createRPr();
            RFonts rFonts = factory.createRFonts();
            rFonts.setEastAsia("宋体");
            rFonts.setHAnsi("宋体");
            rpr.setRFonts(rFonts);
            HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
            fontSizeMeasure.setVal(BigInteger.valueOf(21));
            rpr.setSz(fontSizeMeasure);
            rpr.setSzCs(fontSizeMeasure);
            r.setRPr(rpr);

            Text t = factory.createText();
            t.setValue("无");
            r.getContent().add(t);
            paragraph.getContent().add(r);
            return paragraph;
        }

        // 先清理文本
        suggestion = suggestion.trim().replaceAll("\\s+", " ");

        // 检查是否包含序号
        if (suggestion.matches(".*\\d+[、.].*")) {
            // 按序号分割文本
            String[] parts = suggestion.split("(?=\\d+[、.])");
            boolean isFirst = true;

            for (int i = 0; i < parts.length; i++) {
                String part = parts[i].trim();
                if (part.isEmpty()) {
                    continue;
                }

                // 统一序号格式
                part = part.replaceAll("(\\d+)\\.", "$1、");

                // 如果是最后一部分且不以句号结尾，添加句号
                if (i == parts.length - 1 && !part.endsWith("。") && !part.endsWith(".")) {
                    part = part + "。";
                }

                // 如果不是第一部分，添加换行
                if (!isFirst) {
                    R brR = factory.createR();
                    RPr brRpr = factory.createRPr();
                    RFonts brRFonts = factory.createRFonts();
                    brRFonts.setEastAsia("宋体");
                    brRFonts.setHAnsi("宋体");
                    brRpr.setRFonts(brRFonts);
                    HpsMeasure brFontSizeMeasure = factory.createHpsMeasure();
                    brFontSizeMeasure.setVal(BigInteger.valueOf(21));
                    brRpr.setSz(brFontSizeMeasure);
                    brRpr.setSzCs(brFontSizeMeasure);
                    brR.setRPr(brRpr);

                    Br br = factory.createBr();
                    brR.getContent().add(br);
                    paragraph.getContent().add(brR);
                } else {
                    isFirst = false;
                }

                // 添加文本内容
                R r = factory.createR();
                RPr rpr = factory.createRPr();
                RFonts rFonts = factory.createRFonts();
                rFonts.setEastAsia("宋体");
                rFonts.setHAnsi("宋体");
                rpr.setRFonts(rFonts);
                HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                fontSizeMeasure.setVal(BigInteger.valueOf(21));
                rpr.setSz(fontSizeMeasure);
                rpr.setSzCs(fontSizeMeasure);
                r.setRPr(rpr);

                Text t = factory.createText();
                t.setValue(part);
                r.getContent().add(t);
                paragraph.getContent().add(r);
            }
        } else {
            // 没有序号的情况，检查是否需要分割
            String[] items = null;
            if (suggestion.contains("；") || suggestion.contains(";")) {
                items = suggestion.split("[;；]");
            } else if (suggestion.contains("。")) {
                items = suggestion.split("。");
            } else if (suggestion.contains("，") || suggestion.contains(",")) {
                items = suggestion.split("[,，]");
            }

            if (items != null && items.length > 1) {
                // 有分隔符，添加序号
                int count = 1;
                boolean isFirst = true;

                for (int j = 0; j < items.length; j++) {
                    String item = items[j].trim();
                    if (item.isEmpty()) {
                        continue;
                    }

                    // 如果是最后一项且不以句号结尾，添加句号
                    if (j == items.length - 1 && !item.endsWith("。") && !item.endsWith(".")) {
                        item = item + "。";
                    }

                    // 如果不是第一项，添加换行
                    if (!isFirst) {
                        R brR = factory.createR();
                        RPr brRpr = factory.createRPr();
                        RFonts brRFonts = factory.createRFonts();
                        brRFonts.setEastAsia("宋体");
                        brRFonts.setHAnsi("宋体");
                        brRpr.setRFonts(brRFonts);
                        HpsMeasure brFontSizeMeasure = factory.createHpsMeasure();
                        brFontSizeMeasure.setVal(BigInteger.valueOf(21));
                        brRpr.setSz(brFontSizeMeasure);
                        brRpr.setSzCs(brFontSizeMeasure);
                        brR.setRPr(brRpr);

                        Br br = factory.createBr();
                        brR.getContent().add(br);
                        paragraph.getContent().add(brR);
                    } else {
                        isFirst = false;
                    }

                    // 添加带序号的文本
                    R r = factory.createR();
                    RPr rpr = factory.createRPr();
                    RFonts rFonts = factory.createRFonts();
                    rFonts.setEastAsia("宋体");
                    rFonts.setHAnsi("宋体");
                    rpr.setRFonts(rFonts);
                    HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                    fontSizeMeasure.setVal(BigInteger.valueOf(21));
                    rpr.setSz(fontSizeMeasure);
                    rpr.setSzCs(fontSizeMeasure);
                    r.setRPr(rpr);

                    Text t = factory.createText();
                    t.setValue(count + "、" + item);
                    r.getContent().add(t);
                    paragraph.getContent().add(r);
                    count++;
                }
            } else {
                // 没有分隔符，直接显示原文本，如果不以句号结尾则添加句号
                if (!suggestion.endsWith("。") && !suggestion.endsWith(".")) {
                    suggestion = suggestion + "。";
                }

                R r = factory.createR();
                RPr rpr = factory.createRPr();
                RFonts rFonts = factory.createRFonts();
                rFonts.setEastAsia("宋体");
                rFonts.setHAnsi("宋体");
                rpr.setRFonts(rFonts);
                HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                fontSizeMeasure.setVal(BigInteger.valueOf(21));
                rpr.setSz(fontSizeMeasure);
                rpr.setSzCs(fontSizeMeasure);
                r.setRPr(rpr);

                Text t = factory.createText();
                t.setValue(suggestion);
                r.getContent().add(t);
                paragraph.getContent().add(r);
            }
        }

        return paragraph;
    }

    /**
     * 格式化建议措施文本，支持序号换行显示
     * @param text 原始建议措施文本
     * @return 格式化后的文本，序号前换行
     */
    private String formatMultilineText(String text) {
        if (text == null || text.trim().isEmpty() || "无".equals(text.trim())) {
            return "无";
        }

        // 先清理文本，去除多余的空白字符
        text = text.trim().replaceAll("\\s+", " ");

        // 如果文本包含序号格式（如"1、"或"1."），则在序号前换行
        if (text.matches(".*\\d+[、.].*")) {
            // 在序号前添加换行符，但不包括第一个序号
            String formattedText = text.replaceAll("(?<!^)(?<!\\n)(\\d+[、.])", "\n$1");

            // 统一序号格式，将句点替换为顿号
            formattedText = formattedText.replaceAll("(\\d+)\\.", "$1、");

            return formattedText;
        } else {
            // 如果没有序号，按分隔符智能分割并添加序号
            String[] items;

            // 按优先级选择分隔符：分号 > 句号 > 逗号
            if (text.contains("；") || text.contains(";")) {
                items = text.split("[;；]");
            } else if (text.contains("。")) {
                items = text.split("。");
            } else if (text.contains("，") || text.contains(",")) {
                items = text.split("[,，]");
            } else {
                // 没有明显分隔符，直接返回原文本
                return text;
            }

            StringBuilder sb = new StringBuilder();
            int count = 1;

            for (String item : items) {
                item = item.trim();
                if (!item.isEmpty()) {
                    if (count > 1) {
                        sb.append("\n");
                    }
                    sb.append(count).append("、").append(item);
                    count++;
                }
            }

            return sb.length() > 0 ? sb.toString() : text;
        }
    }

    /**
     * 为兼容旧的调用，保留原来的方法签名，但内部实现改为创建简单的文本，使用\n换行
     * @param suggestions 建议列表
     * @return 格式化后的文本
     */
    private String formatSuggestionsList(List<String> suggestions) {
        if (suggestions == null || suggestions.isEmpty()) {
            return "无";
        }

        StringBuilder result = new StringBuilder();
        boolean isFirst = true;

        for (int i = 0; i < suggestions.size(); i++) {
            String suggestion = suggestions.get(i).trim();
            if (suggestion.isEmpty()) {
                continue;
            }

            // 如果不是第一项，添加换行
            if (!isFirst) {
                result.append("\n");
            } else {
                isFirst = false;
            }

            // 检查建议是否已经包含序号格式
            if (!suggestion.matches("^\\s*\\d+[、.].*")) {
                // 没有序号，添加序号
                suggestion = (i + 1) + "、" + suggestion;
            }

            result.append(suggestion);
        }

        return result.length() > 0 ? result.toString() : "无";
    }

    /**
     * 生成对应的分部评分和等级
     * @param wordMLPackage Word文档对象
     * @param checkEnumRelationList 检测关系数据列表
     * @param tunnelInfo 隧道信息
     */
    private void generatePartScore(WordprocessingMLPackage wordMLPackage, List<CheckEnumRelation> checkEnumRelationList, TunnelInfo tunnelInfo) {
        try {
            // 按分部代码进行分组
            Map<String, List<CheckEnumRelation>> partGroupMap = checkEnumRelationList.stream()
                    .collect(Collectors.groupingBy(CheckEnumRelation::getPartCode));

            // 计算各系统分数
            Map<String, BigDecimal> partScores = new LinkedHashMap<>(); // 使用LinkedHashMap保持顺序
            // 获取所有分部信息，以动态生成表头
            List<CheckEnum> allParts = checkEnumMapper.selectDistinctPartCodeAndName();

            // 创建分部名称映射
            Map<String, String> partCodeToNameMap = new HashMap<>();

            // 记录所有分部的代码，用于统一顺序（按partCode排序）
            List<String> allPartCodes = new ArrayList<>();

            // 处理所有分部列
            for (CheckEnum part : allParts) {
                String partCode = part.getPartCode();
                String partName = part.getPartName();

                if (partName != null && !partName.isEmpty() && !allPartCodes.contains(partCode)) {
                    partCodeToNameMap.put(partCode, partName);
                    allPartCodes.add(partCode);
                }
            }

            // 对分部代码进行排序
            Collections.sort(allPartCodes);

            // 计算每个分部的得分
            for (String partCode : allPartCodes) {
                List<CheckEnumRelation> partData = partGroupMap.get(partCode);
                if (partData == null || partData.isEmpty()) {
                    // 如果没有数据，跳过
                    continue;
                }

                // 根据分项进行分组 - 与exportDetail保持一致，使用itemCode + itemName分组
                Map<String, List<CheckEnumRelation>> itemGroupMap = partData.stream()
                        .collect(Collectors.groupingBy(r -> r.getItemCode() + "_" + r.getItemName()));

                // 使用与exportDetail方法相同的计算方式，确保分数一致
                BigDecimal itemTotalScore = BigDecimal.ZERO;
                int itemCount = 0;

                // 获取分部名称，记录日志用
                String partName = partData.get(0).getPartName();

                // 遍历并计算每个分项的分数
                for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                    List<CheckEnumRelation> itemData = itemEntry.getValue();

                    // 获取分项名称，便于日志记录
                    String itemName = "";
                    if (!itemData.isEmpty()) {
                        itemName = itemData.get(0).getItemName();
                    }
                    // 使用与Excel公式相同的计算方式
                    BigDecimal itemScore = calculateItemScore(itemData);

                    // 记录分项得分
                    log.info("隧道[{}] 分部[{}:{}] 分项[{}]得分: {}",
                            tunnelInfo.getTunnelName(), partCode, partName, itemName, itemScore);

                    // 修复计算逻辑：分项得分为"/"的不计入（即itemScore < 0），但0分需要计入分部得分计算
                    // 原来的条件只包含了分值大于0的情况，修改为分值>=0以包含0分，与exportDetail保持一致
                    if (itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                        itemTotalScore = itemTotalScore.add(itemScore);
                        itemCount++;
                    }
                }
                // 计算分项的平均值，即分部的总得分
                BigDecimal partScore = BigDecimal.ZERO;

                // 照明设施特殊处理，partCode为"2"
                if ("2".equals(partCode)) {
                    // 找到"隧道灯具"和"洞外路灯"分项
                    BigDecimal tunnelLightScore = BigDecimal.ZERO;
                    BigDecimal outsideLightScore = BigDecimal.ZERO;
                    boolean hasTunnelLight = false;
                    boolean hasOutsideLight = false;

                    // 重新遍历分项找到"隧道灯具"和"洞外路灯"
                    for (Map.Entry<String, List<CheckEnumRelation>> itemEntry : itemGroupMap.entrySet()) {
                        List<CheckEnumRelation> itemData = itemEntry.getValue();
                        if (itemData.isEmpty()) continue;

                        String itemName = itemData.get(0).getItemName();
                        BigDecimal itemScore = calculateItemScore(itemData);

                        if ("隧道灯具".equals(itemName) && itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                            tunnelLightScore = itemScore;
                            hasTunnelLight = true;
                            log.info("隧道[{}] 分部[{}:{}] 分项[隧道灯具]得分(照明设施特殊计算): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, tunnelLightScore);
                        } else if ("洞外路灯".equals(itemName) && itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                            outsideLightScore = itemScore;
                            hasOutsideLight = true;
                            log.info("隧道[{}] 分部[{}:{}] 分项[洞外路灯]得分(照明设施特殊计算): {}",
                                    tunnelInfo.getTunnelName(), partCode, partName, outsideLightScore);
                        }
                    }

                    // 特殊计算照明设施分部得分：(隧道灯具得分*5+洞外路灯得分*1)/6
                    if (hasTunnelLight && hasOutsideLight) {
                        // 计算加权平均分
                        BigDecimal weightedSum = tunnelLightScore.multiply(new BigDecimal("5"))
                                .add(outsideLightScore.multiply(new BigDecimal("1")));
                        partScore = weightedSum.divide(new BigDecimal("6"), 2, RoundingMode.HALF_EVEN);
                        log.info("隧道[{}] 分部[{}:{}]得分(特殊计算): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else if (hasTunnelLight) {
                        // 只有隧道灯具有效，使用隧道灯具分数
                        partScore = tunnelLightScore;
                        log.info("隧道[{}] 分部[{}:{}]得分(仅使用隧道灯具分数): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else if (hasOutsideLight) {
                        // 只有洞外路灯有效，使用洞外路灯分数
                        partScore = outsideLightScore;
                        log.info("隧道[{}] 分部[{}:{}]得分(仅使用洞外路灯分数): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    } else {
                        // 两个分项都没有有效分数，使用常规计算方式
                        if (itemCount > 0) {
                            partScore = itemTotalScore.divide(new BigDecimal(itemCount), 2, RoundingMode.HALF_EVEN);
                        }
                        log.info("隧道[{}] 分部[{}:{}]得分(常规计算): {}",
                                tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    }
                } else {
                    // 其他分部正常计算平均分
                    if (itemCount > 0) {
                        partScore = itemTotalScore.divide(new BigDecimal(itemCount), 2, RoundingMode.HALF_EVEN);
                        log.info("隧道[{}] 分部[{}:{}]得分: {}", tunnelInfo.getTunnelName(), partCode, partName, partScore);
                    }
                }

                // 记录分部得分
                partScores.put(partCode, partScore);
                // 记录日志
                log.info("隧道[{}] 分部[{}:{}]得分: {}", tunnelInfo.getTunnelName(), partCode, partName, partScore);
            }

            // 计算总得分 - 使用统一计算方法
            BigDecimal totalScore = calculateTotalScore(partScores);
            String category = determineCategory(totalScore);
            //替换对应的检测等级
            String rankDescription = generateRankDescription(category);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "rank", rankDescription);
            // 记录该隧道总分
            log.info("隧道[{}]总得分: {}, 类别: {}", tunnelInfo.getTunnelName(), totalScore, category);

            // 在替换占位符前添加分页符，确保表格显示在新页面上
//            insertPageBreakBeforePlaceholder(wordMLPackage, "${partList}");

            // 计算实际有数据的分部数量
            int actualPartCount = 0;
            for (String partCode : allPartCodes) {
                if (partScores.containsKey(partCode)) {
                    actualPartCount++;
                }
            }

            // 创建表格：只为实际有数据的分部创建行
            int totalRows = actualPartCount + 1; // 表头 + 实际分部行数
            int totalCols = 7; // 单位工程、序号、分部设施名称、分部设施得分、分部设施技术状况等级、技术状况评分、隧道机电设施技术状况分类

            Tbl table = WordDocumentUtil.createTable(totalRows, totalCols);

            // 设置表格列宽
            TblGrid tblGrid = table.getTblGrid();
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(1200)); // 单位工程
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(800));  // 序号
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(1500)); // 分部设施名称
            tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(1200)); // 分部设施得分
            tblGrid.getGridCol().get(4).setW(BigInteger.valueOf(1500)); // 分部设施技术状况等级
            tblGrid.getGridCol().get(5).setW(BigInteger.valueOf(1200)); // 技术状况评分
            tblGrid.getGridCol().get(6).setW(BigInteger.valueOf(1500)); // 隧道机电设施技术状况分类

            // 设置表头 - 使用false参数取消背景色
            WordDocumentUtil.setCellText(table, 0, 0, "单位工程", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "序号", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "分部设施名称", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 3, "分部设施得分", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 4, "分部设施技术状况等级", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 5, "技术状况评分 (分)", 12, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 6, "隧道机电设施技术状况分类", 12, true, false, 2);

            // 设置重复表头
            setTableHeaderRepeat(table, 1);

            // 强制设置表格字体为宋体五号（在内容填充后重新应用）
            forceApplySize5FontToAllTableContent(table);

            // 填充数据行
            int currentRow = 1;
            int partCount = 0;

            // 记录第一行的索引，用于后续合并单元格
            int firstDataRow = currentRow;

            // 按照排序后的分部代码填充数据
            for (String partCode : allPartCodes) {
                if (!partScores.containsKey(partCode)) {
                    continue; // 跳过没有得分的分部
                }

                partCount++;
                String partName = partCodeToNameMap.get(partCode);
                BigDecimal partScore = partScores.get(partCode);
                String category4Part = determineCategory(partScore);

                // 单位工程 - 只在第一行填写"隧道机电设施"
                if (partCount == 1) {
                    WordDocumentUtil.setCellText(table, currentRow, 0, "隧道机电设施", 12, false, false, 2);

                    // 第一行填写技术状况评分和分类
                    WordDocumentUtil.setCellText(table, currentRow, 5, formatScore(totalScore), 12, false, false, 2);
                    WordDocumentUtil.setCellText(table, currentRow, 6, category, 12, false, false, 2);
                }

                // 序号
                WordDocumentUtil.setCellText(table, currentRow, 1, String.valueOf(partCount), 12, false, false, 2);

                // 分部设施名称
                WordDocumentUtil.setCellText(table, currentRow, 2, partName, 12, false, false, 2);

                // 分部设施得分
                WordDocumentUtil.setCellText(table, currentRow, 3, formatScore(partScore), 12, false, false, 2);

                // 分部设施技术状况等级
                WordDocumentUtil.setCellText(table, currentRow, 4, category4Part, 12, false, false, 2);

                // 其他行的技术状况评分和分类列留空
                if (partCount > 1) {
                    WordDocumentUtil.setCellText(table, currentRow, 5, "", 12, false, false, 2);
                    WordDocumentUtil.setCellText(table, currentRow, 6, "", 12, false, false, 2);
                }

                currentRow++;
            }

            // 如果有多个分部，合并各列单元格
            if (actualPartCount > 1) {
                // 合并"单位工程"列的单元格
                mergeVerticalCells(table, 0, firstDataRow, firstDataRow + actualPartCount - 1);

                // 合并"技术状况评分 (分)"列的单元格
                mergeVerticalCells(table, 5, firstDataRow, firstDataRow + actualPartCount - 1);

                // 合并"隧道机电设施技术状况分类"列的单元格
                mergeVerticalCells(table, 6, firstDataRow, firstDataRow + actualPartCount - 1);
            }

            // 在所有内容填充完成后，最后一次强制设置表格字体为宋体五号
            forceApplySize5FontToAllTableContent(table);

            // 替换模板中的${partList}占位符
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            boolean replacementDone = false;
            List<Object> docObjects = mainDocumentPart.getContent();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains("${partList}")) {
                        // 替换占位符段落为表格
                        docObjects.set(i, table);
                        replacementDone = true;
                        log.info("成功替换${partList}占位符为分部评分表格");
                        break;
                    }
                }
            }

            if (!replacementDone) {
                log.warn("未找到${partList}占位符，尝试作为字符串替换");
                // 如果没有找到占位符段落，尝试作为普通文本替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "partList", "");
            }
        } catch (Exception e) {
            log.error("生成分部评分表格失败", e);
            // 发生异常时尝试替换占位符为空字符串
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "partList", "");
        }
    }

    /**
     * 在指定占位符前插入分页符
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符文本
     */
    private void insertPageBreakBeforePlaceholder(WordprocessingMLPackage wordMLPackage, String placeholder) {
        try {
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();

            // 查找包含占位符的段落
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains(placeholder)) {
                        // 在找到的占位符前插入分页符
                        ObjectFactory factory = new ObjectFactory();
                        P pageBreakP = factory.createP();
                        R pageBreakR = factory.createR();
                        Br pageBreak = factory.createBr();
                        pageBreak.setType(STBrType.PAGE);

                        pageBreakR.getContent().add(pageBreak);
                        pageBreakP.getContent().add(pageBreakR);

                        docObjects.add(i, pageBreakP);
                        log.info("在占位符 {} 前成功插入分页符", placeholder);
                        return;
                    }
                }
            }

            log.warn("未找到占位符 {}，无法插入分页符", placeholder);
        } catch (Exception e) {
            log.error("插入分页符失败", e);
        }
    }

    /**
     * 格式化分数显示，除了100和0显示整数外，其他都保留两位小数
     * @param score 分数
     * @return 格式化后的分数字符串
     */
    private String formatScore(BigDecimal score) {
        if (score == null) {
            return "/";
        }

        // 检查是否为100或0，如果是则显示整数
        if (score.compareTo(new BigDecimal("100")) == 0 || score.compareTo(BigDecimal.ZERO) == 0) {
            return score.setScale(0, RoundingMode.HALF_EVEN).toString();
        }

        // 其他数值都保留两位小数
        return score.setScale(2, RoundingMode.HALF_EVEN).toString();
    }

    /**
     * 计算分部平均得分
     * @param itemGroupMap 分项分组Map
     * @return 分部平均得分
     */
    private BigDecimal calculatePartScore(Map<String, List<CheckEnumRelation>> itemGroupMap) {
        BigDecimal itemTotalScore = BigDecimal.ZERO;
        int itemCount = 0;

        for (List<CheckEnumRelation> itemData : itemGroupMap.values()) {
            // 计算分项得分
            BigDecimal itemScore = calculateItemScore(itemData);

            // 只有分数不是负值（不是"/"）才计入平均分计算
            if (itemScore.compareTo(BigDecimal.ZERO) >= 0) {
                itemTotalScore = itemTotalScore.add(itemScore);
                itemCount++;
            }
        }

        // 计算平均分
        if (itemCount > 0) {
            return itemTotalScore.divide(new BigDecimal(itemCount), 2, RoundingMode.HALF_EVEN);
        } else {
            return BigDecimal.ZERO;
        }
    }




    /**
     * 生成设施表格并替换模板中的${facilityTable}占位符
     * @param wordMLPackage Word文档对象
     * @param facilityInfoList 设施信息列表
     */
    private void generateAndReplaceFacilityTable(WordprocessingMLPackage wordMLPackage, List<FacilityInfo> facilityInfoList) {
        try {
            if (CollectionUtils.isEmpty(facilityInfoList)) {
                log.warn("设施信息列表为空，无法生成表格");
                // 如果没有数据，替换为空字符串
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "facilityTable", "");
                return;
            }

            log.info("开始生成设施表格，共 {} 条设施数据", facilityInfoList.size());

            // 对数据集合按partCode和itemCode排序
            facilityInfoList.sort(Comparator
                    .comparing(FacilityInfo::getPartCode, Comparator.nullsLast(String::compareTo))
                    .thenComparing(FacilityInfo::getItemCode, Comparator.nullsLast(String::compareTo)));

            // 创建按partCode排序的有序Map，保持分部顺序
            Map<String, String> partCodeToNameMap = new LinkedHashMap<>();
            Map<String, List<FacilityInfo>> partGroupMap = new LinkedHashMap<>();

            // 首先提取所有分部编码及名称，按partCode排序
            for (FacilityInfo facility : facilityInfoList) {
                if (facility.getPartCode() != null && facility.getPartName() != null) {
                    partCodeToNameMap.putIfAbsent(facility.getPartCode(), facility.getPartName());
                }
            }

            // 根据排序后的分部编码顺序组织数据
            for (String partCode : partCodeToNameMap.keySet()) {
                final String currentPartCode = partCode;
                List<FacilityInfo> partItems = facilityInfoList.stream()
                        .filter(f -> currentPartCode.equals(f.getPartCode()))
                        .collect(Collectors.toList());

                // 确保每个分部内的分项按itemCode排序
                partItems.sort(Comparator.comparing(
                        FacilityInfo::getItemCode,
                        Comparator.nullsLast(String::compareTo)
                ));

                // 存入有序Map
                partGroupMap.put(partCode, partItems);
            }

            // 创建表格
            // 计算总行数：表头1行 + 所有分项数据行
            int totalRows = 1;
            for (List<FacilityInfo> items : partGroupMap.values()) {
                totalRows += items.size();
            }

            // 创建表格对象，4列：分部设施名称、分项设施名称、单位、设备数量
            Tbl table = WordDocumentUtil.createTable(totalRows, 4);

            // 设置表格样式和列宽
            TblGrid tblGrid = table.getTblGrid();
            // 第一列和第二列稍宽，第三列和第四列窄一些
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(1500)); // 分部设施名称列
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(1800)); // 分项设施名称列
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(1000));  // 单位列
            tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(1000)); // 设备数量列

            // 设置表头 - 使用false参数取消背景色（使用五号字体大小 = 10.5pt，但后续会被强制字体设置覆盖）
            WordDocumentUtil.setCellText(table, 0, 0, "分部设施名称", 21, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "分项设施名称", 21, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "单位", 21, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 3, "设备数量", 21, true, false, 2);

            // 优化表格样式：设置宋体五号字体和重复表头
            optimizeTableStyleWithSize5Font(table, 1);

            // 当前行索引（从表头后开始）
            int currentRow = 1;

            // 填充数据（按照排序后的partCode和itemCode）
            for (Map.Entry<String, List<FacilityInfo>> entry : partGroupMap.entrySet()) {
                String partCode = entry.getKey();
                String partName = partCodeToNameMap.get(partCode);
                List<FacilityInfo> items = entry.getValue();

                // 记录该分部的起始行
                int partStartRow = currentRow;

                // 遍历分部下的所有分项
                for (int i = 0; i < items.size(); i++) {
                    FacilityInfo item = items.get(i);

                    // 只在第一个分项填写分部名称及编码（使用五号字体大小）
                    if (i == 0) {
                        WordDocumentUtil.setCellText(table, currentRow, 0, partName, 21, false, false, 2);
                    }

                    // 填写分项信息及编码（使用五号字体大小）
                    WordDocumentUtil.setCellText(table, currentRow, 1, item.getItemName(), 21, false, false, 2);
                    WordDocumentUtil.setCellText(table, currentRow, 2, item.getUnit() != null ? item.getUnit() : "台", 21, false, false, 2);
                    WordDocumentUtil.setCellText(table, currentRow, 3, item.getNum() != null ? WordDocumentUtil.formatBigDecimal(new BigDecimal(item.getNum()), 2) : "/", 21, false, false, 2);

                    currentRow++;
                }

                // 如果该分部有多个分项，合并分部名称单元格
                if (items.size() > 1) {
                    // 垂直合并第一列单元格
                    mergeVerticalCells(table, 0, partStartRow, partStartRow + items.size() - 1);
                }
            }

            // 使用专门的设施表格字体优化方法，确保所有内容都是宋体五号字体
            optimizeFacilityTableFont(table);

            // 获取主文档部分
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();

            // 查找包含${facilityTable}占位符的段落并替换为表格
            boolean replacementDone = false;
            List<Object> docObjects = mainDocumentPart.getContent();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains("${facilityTable}")) {
                        // 替换占位符段落为表格
                        docObjects.set(i, table);
                        replacementDone = true;
                        log.info("成功替换${facilityTable}占位符为设施表格");
                        break;
                    }
                }
            }

            if (!replacementDone) {
                log.warn("未找到${facilityTable}占位符，尝试作为字符串替换");
                // 如果没有找到占位符段落，尝试作为普通文本替换（不太可能成功，但作为备选方案）
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "facilityTable", "");
            }

        } catch (Exception e) {
            log.error("生成设施表格失败", e);
            // 发生异常时，尝试替换占位符为空字符串，避免文档显示原始占位符
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "facilityTable", "");
        }
    }

    /**
     * 垂直合并表格单元格（合并列）
     * @param table 表格对象
     * @param colIndex 要合并的列索引
     * @param startRow 开始行索引
     * @param endRow 结束行索引
     */
    private void mergeVerticalCells(Tbl table, int colIndex, int startRow, int endRow) {
        try {
            if (table == null || colIndex < 0 || startRow < 0 || endRow <= startRow) {
                log.error("垂直合并单元格参数无效: colIndex={}, startRow={}, endRow={}", colIndex, startRow, endRow);
                return;
            }

            List<Object> rows = table.getContent();
            if (rows.size() <= endRow) {
                log.error("垂直合并单元格行索引超出范围: endRow={}, rows.size()={}", endRow, rows.size());
                return;
            }

            // 获取第一个单元格
            Tr firstRow = (Tr) rows.get(startRow);
            List<Object> cells = firstRow.getContent();

            if (cells.size() <= colIndex) {
                log.error("垂直合并单元格列索引超出范围: colIndex={}, cells.size()={}", colIndex, cells.size());
                return;
            }

            // 获取第一个单元格
            Tc firstCell = (Tc) cells.get(colIndex);

            // 设置垂直合并属性
            TcPr tcPr = firstCell.getTcPr();
            if (tcPr == null) {
                ObjectFactory factory = new ObjectFactory();
                tcPr = factory.createTcPr();
                firstCell.setTcPr(tcPr);
            }

            // 设置为"开始"垂直合并
            ObjectFactory factory = new ObjectFactory();
            TcPrInner.VMerge vMerge = factory.createTcPrInnerVMerge();
            vMerge.setVal("restart");
            tcPr.setVMerge(vMerge);

            // 处理后续单元格
            for (int i = startRow + 1; i <= endRow; i++) {
                Tr row = (Tr) rows.get(i);
                List<Object> rowCells = row.getContent();

                if (rowCells.size() <= colIndex) {
                    continue;
                }

                Tc cell = (Tc) rowCells.get(colIndex);
                TcPr cellPr = cell.getTcPr();

                if (cellPr == null) {
                    cellPr = factory.createTcPr();
                    cell.setTcPr(cellPr);
                }

                // 设置为"继续"垂直合并
                TcPrInner.VMerge cellVMerge = factory.createTcPrInnerVMerge();
                // 不设置val，默认为"continue"
                cellPr.setVMerge(cellVMerge);

                // 清空单元格内容，防止显示冗余内容
                cell.getContent().clear();

                // 添加一个空段落，确保单元格不为空
                org.docx4j.wml.P p = factory.createP();
                cell.getContent().add(p);
            }

            log.info("垂直合并单元格成功: colIndex={}, startRow={}, endRow={}", colIndex, startRow, endRow);
        } catch (Exception e) {
            log.error("垂直合并单元格失败", e);
        }
    }

    /**
     * 获取表格中的单元格
     * @param table 表格
     * @param rowIndex 行索引
     * @param colIndex 列索引
     * @return 单元格对象
     */
    private Tc getTableCell(Tbl table, int rowIndex, int colIndex) {
        List<Object> rows = table.getContent();
        if (rows.size() <= rowIndex) {
            return null;
        }

        Tr row = (Tr) rows.get(rowIndex);
        List<Object> cells = row.getContent();

        if (cells.size() <= colIndex) {
            return null;
        }

        return (Tc) cells.get(colIndex);
    }

    /**
     * 设置表格的表头行重复显示（跨页时重复表头）
     * @param table 表格对象
     * @param headerRowCount 表头行数
     */
    private void setTableHeaderRepeat(Tbl table, int headerRowCount) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 设置指定数量的表头行重复显示
            for (int i = 0; i < Math.min(headerRowCount, rows.size()); i++) {
                Object rowObj = rows.get(i);
                if (rowObj instanceof Tr) {
                    Tr headerRow = (Tr) rowObj;

                    // 获取或创建表格行属性
                    TrPr trPr = headerRow.getTrPr();
                    if (trPr == null) {
                        trPr = factory.createTrPr();
                        headerRow.setTrPr(trPr);
                    }

                    // 创建表头重复属性
                    BooleanDefaultTrue bdt = factory.createBooleanDefaultTrue();
                    bdt.setVal(true);

                    // 使用正确的API添加表头重复属性
                    trPr.getCnfStyleOrDivIdOrGridBefore().add(
                            factory.createCTTrPrBaseTblHeader(bdt)
                    );

                    log.debug("设置表格第{}行为重复表头", i + 1);
                }
            }

            log.info("成功设置表格重复表头，表头行数：{}", headerRowCount);
        } catch (Exception e) {
            log.error("设置表格重复表头失败", e);
        }
    }

    /**
     * 设置表格字体为宋体五号并设置重复表头
     * @param table 表格对象
     * @param headerRowCount 表头行数
     */
    private void optimizeTableStyle(Tbl table, int headerRowCount) {
        try {
            // 设置重复表头
            setTableHeaderRepeat(table, headerRowCount);

            // 设置整个表格的字体为宋体五号（10.5pt = 21半磅）
            setTableFont(table);

            log.info("成功优化表格样式：设置重复表头（{}行）和宋体五号字体", headerRowCount);
        } catch (Exception e) {
            log.error("优化表格样式失败", e);
        }
    }

    /**
     * 设置表格字体为宋体五号
     * @param table 表格对象
     */
    private void setTableFont(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            setTableCellFont(cell, factory);
                        }
                    }
                }
            }

            log.debug("成功设置表格字体为宋体五号");
        } catch (Exception e) {
            log.error("设置表格字体失败", e);
        }
    }

    /**
     * 设置表格单元格字体为宋体五号
     * @param cell 单元格对象
     * @param factory 对象工厂
     */
    private void setTableCellFont(Tc cell, ObjectFactory factory) {
        setTableCellFontWithSize(cell, factory, 21); // 五号字体（10.5pt = 21半磅）
    }

    /**
     * 设置表格单元格字体为宋体五号
     * @param cell 单元格对象
     * @param factory 对象工厂
     */
    private void setTableCellFontSize5(Tc cell, ObjectFactory factory) {
        setTableCellFontWithSize(cell, factory, 21); // 五号字体（10.5pt = 21半磅）
    }

    /**
     * 设置表格单元格字体为宋体指定大小
     * @param cell 单元格对象
     * @param factory 对象工厂
     * @param fontSize 字体大小（半磅）
     */
    private void setTableCellFontWithSize(Tc cell, ObjectFactory factory, int fontSize) {
        try {
            List<Object> cellContent = cell.getContent();

            for (Object content : cellContent) {
                if (content instanceof P) {
                    P paragraph = (P) content;
                    List<Object> paragraphContent = paragraph.getContent();

                    for (Object pContent : paragraphContent) {
                        if (pContent instanceof R) {
                            R run = (R) pContent;

                            // 获取或创建运行属性
                            RPr rpr = run.getRPr();
                            if (rpr == null) {
                                rpr = factory.createRPr();
                                run.setRPr(rpr);
                            }

                            // 设置字体为宋体
                            RFonts rFonts = rpr.getRFonts();
                            if (rFonts == null) {
                                rFonts = factory.createRFonts();
                                rpr.setRFonts(rFonts);
                            }
                            rFonts.setEastAsia("宋体");
                            rFonts.setHAnsi("宋体");

                            // 设置字号
                            HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                            fontSizeMeasure.setVal(BigInteger.valueOf(fontSize));
                            rpr.setSz(fontSizeMeasure);
                            rpr.setSzCs(fontSizeMeasure);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置单元格字体失败", e);
        }
    }

    /**
     * 设置表格字体为宋体五号（用于指定占位符的表格内容）
     * @param table 表格对象
     */
    private void setTableContentFontSize5(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            setTableCellFontSize5(cell, factory);
                        }
                    }
                }
            }

            log.debug("成功设置表格内容字体为宋体五号");
        } catch (Exception e) {
            log.error("设置表格字体失败", e);
        }
    }

    /**
     * 生成附表目录并替换${indexPartList}占位符
     * @param wordMLPackage Word文档对象
     * @param partNameList 分部名称列表
     */
    private void generateIndexPartList(WordprocessingMLPackage wordMLPackage, List<String> partNameList) {
        try {
            // 如果没有内容，直接返回
            if (partNameList == null || partNameList.isEmpty()) {
                log.warn("没有找到分部数据，附表目录生成失败");
                WordDocumentUtil.replacePlaceholder(wordMLPackage, "indexPartList", "");
                return;
            }

            log.info("开始生成附表目录，共有{}个分部", partNameList.size());

            // 获取主文档部分和占位符段落
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;

                    // 检查段落是否包含占位符
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);
                    if (paragraphText.contains("${indexPartList}")) {
                        // 清空当前段落的内容
                        paragraph.getContent().clear();

                        // 设置段落属性，包括制表位
                        PPr ppr = paragraph.getPPr();
                        if (ppr == null) {
                            ppr = factory.createPPr();
                            paragraph.setPPr(ppr);
                        }

                        // 创建制表位设置
                        Tabs tabs = factory.createTabs();
                        org.docx4j.wml.CTTabStop tabStop = factory.createCTTabStop();
                        tabStop.setVal(org.docx4j.wml.STTabJc.RIGHT); // 右对齐制表位
                        tabStop.setPos(BigInteger.valueOf(9100)); // 制表位位置调整为约15.5cm（8800 twips），与上面页码精确对齐
                        tabStop.setLeader(org.docx4j.wml.STTabTlc.DOT); // 设置引导线为点号
                        tabs.getTab().add(tabStop);
                        ppr.setTabs(tabs);

                        // 创建并添加附表目录内容
                        int startPageNum = 40; // 附表起始页码，可根据实际情况调整

                        // 设置宋体小四字号的样式
                        RPr rpr = factory.createRPr();
                        RFonts rFonts = factory.createRFonts();
                        rFonts.setEastAsia("宋体");
                        rFonts.setAscii("宋体");
                        rFonts.setHAnsi("宋体");
                        rFonts.setCs("宋体");
                        rpr.setRFonts(rFonts);

                        HpsMeasure fontSize = factory.createHpsMeasure();
                        fontSize.setVal(BigInteger.valueOf(24)); // 小四=12pt=24半磅
                        rpr.setSz(fontSize);
                        rpr.setSzCs(fontSize);

                        // 设置不加粗
                        BooleanDefaultTrue bFalse = new BooleanDefaultTrue();
                        bFalse.setVal(false);
                        rpr.setB(bFalse);

                        // 创建附表目录项
                        boolean isFirst = true;
                        for (int j = 0; j < partNameList.size(); j++) {
                            String partName = partNameList.get(j);

                            // 第一个目录项不需要添加换行
                            if (!isFirst) {
                                // 添加换行符
                                R breakRun = factory.createR();
                                breakRun.setRPr(rpr);
                                Br br = factory.createBr();
                                breakRun.getContent().add(br);
                                paragraph.getContent().add(breakRun);
                            } else {
                                isFirst = false;
                            }

                            // 添加附表目录文本内容
                            R textRun = factory.createR();
                            textRun.setRPr(rpr);
                            Text text = factory.createText();
                            text.setValue("附表 " + (j + 1) + "、" + partName + "检查结果");
                            text.setSpace("preserve");
                            textRun.getContent().add(text);
                            paragraph.getContent().add(textRun);

                            // 添加制表符
                            R tabRun = factory.createR();
                            tabRun.setRPr(rpr);
                            Text tabText = factory.createText();
                            tabText.setValue("\t");
                            tabText.setSpace("preserve");
                            tabRun.getContent().add(tabText);
                            paragraph.getContent().add(tabRun);

                            // 添加页码
                            R pageRun = factory.createR();
                            pageRun.setRPr(rpr);
                            Text pageText = factory.createText();
                            pageText.setValue(String.valueOf(startPageNum + j));
                            pageText.setSpace("preserve");
                            pageRun.getContent().add(pageText);
                            paragraph.getContent().add(pageRun);
                        }

                        log.info("附表目录生成完成，使用制表符格式");
                        return ; // 成功找到并替换占位符，不需要继续处理
                    }
                }
            }

            // 如果没有找到占位符，使用常规替换方法
            log.warn("未找到${indexPartList}占位符，使用常规替换方法");
            StringBuilder contentBuilder = new StringBuilder();
            int startPageNum = 40;

            for (int i = 0; i < partNameList.size(); i++) {
                String partName = partNameList.get(i);
                contentBuilder.append("附表 ").append(i + 1).append("、")
                        .append(partName).append("检查结果")
                        .append("\t").append(startPageNum + i);
                if (i < partNameList.size() - 1) {
                    contentBuilder.append("\r\n"); // 使用Windows换行符
                }
            }

            WordDocumentUtil.replacePlaceholder(wordMLPackage, "indexPartList", contentBuilder.toString());
            log.info("附表目录生成完成，使用文本替换");

        } catch (Exception e) {
            log.error("生成附表目录失败", e);
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "indexPartList", "");
        }
    }


    /**
     * 生成页码信息，在页脚中填充当前页/总页码
     * @param wordMLPackage Word文档对象
     */
    private void generatePageNumbers(WordprocessingMLPackage wordMLPackage) {
        try {
            log.info("开始生成页码信息");

            // 使用WordDocumentUtil的替换方法，这是最可靠的方式
            // 这种方法会在整个文档（包括页脚）中查找并替换占位符

            // 替换当前页码占位符为Word域
            replacePageNumberPlaceholderWithField(wordMLPackage, "${pageNo}", false);

            // 替换总页数占位符为Word域
            replacePageNumberPlaceholderWithField(wordMLPackage, "${pageTotal}", true);

            log.info("页码信息生成完成");

        } catch (Exception e) {
            log.error("生成页码信息失败", e);
        }
    }

    /**
     * 替换页码占位符为Word域
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符
     * @param isTotal 是否为总页数
     */
    private void replacePageNumberPlaceholderWithField(WordprocessingMLPackage wordMLPackage, String placeholder, boolean isTotal) {
        try {
            // 获取主文档部分
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();

            // 在主文档中查找并替换
            processDocumentForPageNumber(mainDocumentPart.getContent(), placeholder, isTotal);

            // 查找并处理页脚
            RelationshipsPart rp = mainDocumentPart.getRelationshipsPart();
            if (rp != null) {
                for (Relationship rel : rp.getRelationships().getRelationship()) {
                    if ("http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer".equals(rel.getType())) {
                        try {
                            FooterPart footerPart = (FooterPart) rp.getPart(rel.getId());
                            if (footerPart != null) {
                                Ftr footer = footerPart.getJaxbElement();
                                processDocumentForPageNumber(footer.getContent(), placeholder, isTotal);
                            }
                        } catch (Exception e) {
                            log.warn("处理页脚关系失败：{}", e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("替换页码占位符失败：{}", e);
        }
    }

    /**
     * 在文档内容中处理页码占位符
     * @param content 文档内容列表
     * @param placeholder 占位符
     * @param isTotal 是否为总页数
     */
    private void processDocumentForPageNumber(List<Object> content, String placeholder, boolean isTotal) {
        for (Object obj : content) {
            if (obj instanceof P) {
                P paragraph = (P) obj;
                processPageNumberInParagraph(paragraph, placeholder, isTotal);
            }
        }
    }

    /**
     * 在段落中处理页码占位符
     * @param paragraph 段落对象
     * @param placeholder 占位符
     * @param isTotal 是否为总页数
     */
    private void processPageNumberInParagraph(P paragraph, String placeholder, boolean isTotal) {
        try {
            // 获取段落文本内容
            String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

            // 检查是否包含指定的占位符
            if (paragraphText.contains(placeholder)) {
                log.info("找到页码占位符：{}，段落内容：{}", placeholder, paragraphText);

                // 创建新的段落内容
                ObjectFactory factory = Context.getWmlObjectFactory();

                // 拆分文本，将占位符前后的文本分开
                String[] parts = paragraphText.split(java.util.regex.Pattern.quote(placeholder));

                // 清空段落内容
                paragraph.getContent().clear();

                // 添加占位符前的文本
                if (parts.length > 0 && parts[0].length() > 0) {
                    R textRun = factory.createR();
                    Text text = factory.createText();
                    text.setValue(parts[0]);
                    text.setSpace("preserve");
                    textRun.getContent().add(text);
                    paragraph.getContent().add(textRun);
                }

                // 添加页码域
                addPageNumberField(paragraph, factory, isTotal);

                // 添加占位符后的文本
                if (parts.length > 1 && parts[1].length() > 0) {
                    R textRun = factory.createR();
                    Text text = factory.createText();
                    text.setValue(parts[1]);
                    text.setSpace("preserve");
                    textRun.getContent().add(text);
                    paragraph.getContent().add(textRun);
                }

                log.info("页码占位符 {} 替换完成", placeholder);
            }

        } catch (Exception e) {
            log.error("处理段落页码占位符失败：{}", e.getMessage());
        }
    }



    /**
     * 添加页码域到段落中
     * @param paragraph 段落对象
     * @param factory 对象工厂
     * @param isTotal 是否为总页数（true为总页数，false为当前页）
     */
    private void addPageNumberField(P paragraph, ObjectFactory factory, boolean isTotal) {
        try {
            // 创建运行
            R run = factory.createR();

            // 创建域开始标记
            FldChar fldCharBegin = factory.createFldChar();
            fldCharBegin.setFldCharType(STFldCharType.BEGIN);
            run.getContent().add(fldCharBegin);
            paragraph.getContent().add(run);

            // 创建域代码
            R runCode = factory.createR();
            Text instrText = factory.createText();
            if (isTotal) {
                instrText.setValue(" NUMPAGES ");  // 总页数域
            } else {
                instrText.setValue(" PAGE ");      // 当前页域
            }
            instrText.setSpace("preserve");
            runCode.getContent().add(instrText);
            paragraph.getContent().add(runCode);

            // 创建域结束标记
            R runEnd = factory.createR();
            FldChar fldCharEnd = factory.createFldChar();
            fldCharEnd.setFldCharType(STFldCharType.END);
            runEnd.getContent().add(fldCharEnd);
            paragraph.getContent().add(runEnd);

        } catch (Exception e) {
            log.error("添加页码域失败", e);
        }
    }


    /**
     * 设置备注行字体为宋体小五号
     * @param table 表格对象
     * @param rowIndex 备注行索引
     */
    private void setRemarkRowFontToSmall5(Tbl table, int rowIndex) {
        try {
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 获取备注行
            List<Object> rows = table.getContent();
            if (rowIndex >= 0 && rowIndex < rows.size()) {
                Object rowObj = rows.get(rowIndex);
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    // 处理备注行的所有单元格
                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;

                            // 获取单元格内的所有段落
                            List<Object> cellContent = cell.getContent();
                            for (Object content : cellContent) {
                                if (content instanceof P) {
                                    P paragraph = (P) content;
                                    // 使用强制设置小五号字体的方法
                                    forceApplySmall5FontToParagraph(paragraph, factory);
                                }
                            }
                        }
                    }
                }
            }

            log.debug("成功设置备注行字体为宋体小五号");
        } catch (Exception e) {
            log.error("设置备注行字体失败", e);
        }
    }

    /**
     * 设置备注行字体为宋体五号
     * @param table 表格对象
     * @param rowIndex 行索引
     */
    private void setRemarkRowFontToSize5(Tbl table, int rowIndex) {
        try {
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 获取备注行
            List<Object> rows = table.getContent();
            if (rowIndex >= 0 && rowIndex < rows.size()) {
                Object rowObj = rows.get(rowIndex);
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    // 处理备注行的所有单元格
                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            setTableCellFontSize5(cell, factory); // 使用五号字体
                        }
                    }
                }
            }

            log.debug("成功设置备注行字体为宋体五号");
        } catch (Exception e) {
            log.error("设置备注行字体失败", e);
        }
    }


    /**
     * 重新排列4.1-4.5的序号
     * @param wordMLPackage Word文档对象
     * @param partNameList 分部名称列表
     * @param detectionContentMap 检测内容映射关系
     */
    private void reorderDetectionContentNumbers(WordprocessingMLPackage wordMLPackage, List<String> partNameList,
                                                Map<String, String> detectionContentMap) {
        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();

            int newNumber = 1;

            // 遍历文档内容，重新编号4.x标题
            for (Object obj : docObjects) {
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

                    // 检查是否是4.x标题（检测内容和方法）
                    if (paragraphText.matches("^\\s*4\\.[1-5]\\s+.*检测内容和方法\\s*$")) {
                        // 检查是否在保留的分部列表中
                        boolean shouldRenumber = false;
                        for (String partName : partNameList) {
                            if (detectionContentMap.containsKey(partName) &&
                                    paragraphText.contains(detectionContentMap.get(partName))) {
                                shouldRenumber = true;
                                break;
                            }
                        }

                        if (shouldRenumber) {
                            // 重新编号
                            String newText = paragraphText.replaceFirst("^\\s*4\\.[1-5]", "4." + newNumber);
                            updateParagraphText(paragraph, newText);
                            log.info("重新编号：{} -> {}", paragraphText, newText);
                            newNumber++;
                        }
                    }

                    // 同时处理表格标题中的编号（如表 4.1、表 4.2等）
                    if (paragraphText.matches(".*表\\s*4\\.[1-5].*")) {
                        // 这里需要更复杂的逻辑来匹配对应的分部和重新编号
                        // 暂时先简单处理
                        for (int i = 1; i <= 5; i++) {
                            if (paragraphText.contains("表 4." + i) || paragraphText.contains("表4." + i)) {
                                // 找到对应的分部，重新编号
                                int targetNumber = getNewNumberForPart(paragraphText, partNameList, detectionContentMap);
                                if (targetNumber > 0) {
                                    String newText = paragraphText.replaceAll("表\\s*4\\." + i, "表 4." + targetNumber);
                                    updateParagraphText(paragraph, newText);
                                    log.info("重新编号表格标题：{} -> {}", paragraphText, newText);
                                }
                                break;
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("重新排列4.x序号失败", e);
        }
    }

    /**
     * 根据段落内容获取新的编号
     * @param paragraphText 段落文本
     * @param partNameList 分部名称列表
     * @param detectionContentMap 检测内容映射关系
     * @return 新编号，如果找不到则返回0
     */
    private int getNewNumberForPart(String paragraphText, List<String> partNameList,
                                    Map<String, String> detectionContentMap) {
        int index = 1;
        for (String partName : partNameList) {
            if (detectionContentMap.containsKey(partName)) {
                String contentName = detectionContentMap.get(partName);
                if (paragraphText.contains(partName) || paragraphText.contains(contentName)) {
                    return index;
                }
            }
            index++;
        }
        return 0;
    }

    /**
     * 生成项目范围并替换${itemRange}占位符
     * @param wordMLPackage Word文档对象
     * @param partNameList 分部名称列表
     */
    private void generateAndReplaceItemRange(WordprocessingMLPackage wordMLPackage, List<String> partNameList) {
        try {
            log.info("开始生成项目范围，分部数量：{}", partNameList.size());

            // 根据分部数量生成范围
            String itemRange;
            if (partNameList.size() == 1) {
                itemRange = "4.1";
            } else if (partNameList.size() > 1) {
                itemRange = "4.1～4." + partNameList.size();
            } else {
                itemRange = "4.1"; // 默认值，理论上不应该出现空列表
            }

            log.info("生成的项目范围：{}", itemRange);

            // 替换文档中的${itemRange}占位符
            WordDocumentUtil.replacePlaceholder(wordMLPackage, "itemRange", itemRange);

            // 如果常规替换失败，尝试段落级别的替换
            try {
                MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
                List<Object> docObjects = mainDocumentPart.getContent();
                boolean found = false;

                for (Object obj : docObjects) {
                    if (obj instanceof P) {
                        P paragraph = (P) obj;
                        String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

                        if (paragraphText.contains("${itemRange}")) {
                            log.info("找到itemRange占位符，段落内容：{}", paragraphText);
                            updateParagraphText(paragraph, paragraphText.replace("${itemRange}", itemRange));
                            found = true;
                            log.info("使用段落级替换方法成功替换itemRange占位符为：{}", itemRange);
                            break;
                        }
                    }
                }

                if (!found) {
                    log.warn("在所有段落中都未找到itemRange占位符");
                }
            } catch (Exception e) {
                log.error("段落级替换itemRange失败", e);
            }

            log.info("项目范围替换完成");

        } catch (Exception e) {
            log.error("生成和替换项目范围失败", e);
        }
    }

    /**
     * 设置表格字体为宋体五号（用于 generateAndReplaceFacilityTable 和 generateQuestionRemarkAndSuggestion 方法）
     * @param table 表格对象
     */
    private void setTableFontSize5(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            setTableCellFontSize5(cell, factory);
                        }
                    }
                }
            }

            log.debug("成功设置表格字体为宋体五号");
        } catch (Exception e) {
            log.error("设置表格字体失败", e);
        }
    }

    /**
     * 优化表格样式，使用宋体五号字体（专用于 generateAndReplaceFacilityTable 和 generateQuestionRemarkAndSuggestion）
     * @param table 表格对象
     * @param headerRowCount 表头行数
     */
    private void optimizeTableStyleWithSize5Font(Tbl table, int headerRowCount) {
        try {
            // 设置重复表头
            setTableHeaderRepeat(table, headerRowCount);

            // 设置整个表格的字体为宋体五号
            setTableFontSize5(table);

            // 强制应用宋体五号字体到所有表格内容（包括通过WordDocumentUtil.setCellText设置的内容）
            forceApplySize5FontToAllTableContent(table);

            log.info("成功优化表格样式：设置重复表头（{}行）和宋体五号字体", headerRowCount);
        } catch (Exception e) {
            log.error("优化表格样式失败", e);
        }
    }

    /**
     * 优化重点关注设施表格样式，使用宋体小五号字体（专用于 generatePartImportantTable）
     * @param table 表格对象
     * @param headerRowCount 表头行数
     */
    private void optimizeImportantTableStyle(Tbl table, int headerRowCount) {
        try {
            // 设置重复表头
            setTableHeaderRepeat(table, headerRowCount);

            // 设置整个表格的字体为宋体小五号
            setTableFont(table);

            // 强制应用宋体小五号字体到所有表格内容（包括通过WordDocumentUtil.setCellText设置的内容）
            forceApplySmall5FontToAllTableContent(table);

            log.info("成功优化重点关注设施表格样式：设置重复表头（{}行）和宋体小五号字体", headerRowCount);
        } catch (Exception e) {
            log.error("优化重点关注设施表格样式失败", e);
        }
    }

    /**
     * 强制应用宋体五号字体到表格的所有内容（包括通过WordDocumentUtil.setCellText设置的内容）
     * @param table 表格对象
     */
    private void forceApplySize5FontToAllTableContent(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            forceApplySize5FontToCell(cell, factory);
                        }
                    }
                }
            }

            log.debug("成功强制应用宋体五号字体到表格所有内容");
        } catch (Exception e) {
            log.error("强制应用宋体五号字体失败", e);
        }
    }

    /**
     * 强制应用宋体五号字体到表格内容，但排除最后一行
     * @param table 表格对象
     */
    private void forceApplySize5FontToTableContentExceptLastRow(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 处理除最后一行外的所有行
            for (int i = 0; i < rows.size() - 1; i++) {
                Object rowObj = rows.get(i);
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            forceApplySize5FontToCell(cell, factory);
                        }
                    }
                }
            }

            log.debug("成功强制应用宋体五号字体到表格内容（排除最后一行）");
        } catch (Exception e) {
            log.error("强制应用宋体五号字体失败", e);
        }
    }

    /**
     * 强制应用宋体五号字体到单个表格单元格的所有内容
     * @param cell 单元格对象
     * @param factory 对象工厂
     */
    private void forceApplySize5FontToCell(Tc cell, ObjectFactory factory) {
        try {
            List<Object> cellContent = cell.getContent();

            for (Object content : cellContent) {
                if (content instanceof P) {
                    P paragraph = (P) content;
                    forceApplySize5FontToParagraph(paragraph, factory);
                }
            }
        } catch (Exception e) {
            log.error("强制应用字体到单元格失败", e);
        }
    }

    /**
     * 强制应用宋体五号字体到段落的所有运行
     * @param paragraph 段落对象
     * @param factory 对象工厂
     */
    private void forceApplySize5FontToParagraph(P paragraph, ObjectFactory factory) {
        try {
            List<Object> paragraphContent = paragraph.getContent();

            for (Object pContent : paragraphContent) {
                if (pContent instanceof R) {
                    R run = (R) pContent;

                    // 获取或创建运行属性
                    RPr rpr = run.getRPr();
                    if (rpr == null) {
                        rpr = factory.createRPr();
                        run.setRPr(rpr);
                    }

                    // 设置字体为宋体
                    RFonts rFonts = rpr.getRFonts();
                    if (rFonts == null) {
                        rFonts = factory.createRFonts();
                        rpr.setRFonts(rFonts);
                    }
                    rFonts.setEastAsia("宋体");
                    rFonts.setHAnsi("宋体");

                    // 设置字号为五号（10.5pt = 21半磅）
                    HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                    fontSizeMeasure.setVal(BigInteger.valueOf(21));
                    rpr.setSz(fontSizeMeasure);
                    rpr.setSzCs(fontSizeMeasure);
                }
            }
        } catch (Exception e) {
            log.error("强制应用字体到段落失败", e);
        }
    }

    /**
     * 强制应用宋体小五号字体到表格的所有内容（包括通过WordDocumentUtil.setCellText设置的内容）
     * @param table 表格对象
     */
    private void forceApplySmall5FontToAllTableContent(Tbl table) {
        try {
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            forceApplySmall5FontToCell(cell, factory);
                        }
                    }
                }
            }

            log.debug("成功强制应用宋体小五号字体到表格所有内容");
        } catch (Exception e) {
            log.error("强制应用宋体小五号字体失败", e);
        }
    }

    /**
     * 强制应用宋体小五号字体到单个表格单元格的所有内容
     * @param cell 单元格对象
     * @param factory 对象工厂
     */
    private void forceApplySmall5FontToCell(Tc cell, ObjectFactory factory) {
        try {
            List<Object> cellContent = cell.getContent();

            for (Object content : cellContent) {
                if (content instanceof P) {
                    P paragraph = (P) content;
                    forceApplySmall5FontToParagraph(paragraph, factory);
                }
            }
        } catch (Exception e) {
            log.error("强制应用字体到单元格失败", e);
        }
    }

    /**
     * 强制应用宋体小五号字体到段落的所有运行
     * @param paragraph 段落对象
     * @param factory 对象工厂
     */
    private void forceApplySmall5FontToParagraph(P paragraph, ObjectFactory factory) {
        try {
            List<Object> paragraphContent = paragraph.getContent();

            for (Object pContent : paragraphContent) {
                if (pContent instanceof R) {
                    R run = (R) pContent;

                    // 获取或创建运行属性
                    RPr rpr = run.getRPr();
                    if (rpr == null) {
                        rpr = factory.createRPr();
                        run.setRPr(rpr);
                    }

                    // 设置字体为宋体
                    RFonts rFonts = rpr.getRFonts();
                    if (rFonts == null) {
                        rFonts = factory.createRFonts();
                        rpr.setRFonts(rFonts);
                    }
                    rFonts.setEastAsia("宋体");
                    rFonts.setHAnsi("宋体");

                    // 设置字号为小五号（9pt = 18半磅）
                    HpsMeasure fontSizeMeasure = factory.createHpsMeasure();
                    fontSizeMeasure.setVal(BigInteger.valueOf(18));
                    rpr.setSz(fontSizeMeasure);
                    rpr.setSzCs(fontSizeMeasure);
                }
            }
        } catch (Exception e) {
            log.error("强制应用字体到段落失败", e);
        }
    }

    /**
     * 专门为设施表格优化字体设置，确保所有内容都是宋体五号字体
     * @param table 表格对象
     */
    private void optimizeFacilityTableFont(Tbl table) {
        try {
            log.info("开始为设施表格专门优化字体设置");

            // 第一步：使用基础的五号字体设置
            setTableFontSize5(table);

            // 第二步：强制应用五号字体到所有内容
            forceApplySize5FontToAllTableContent(table);

            // 第三步：再次遍历确保没有遗漏
            List<Object> rows = table.getContent();
            ObjectFactory factory = Context.getWmlObjectFactory();

            for (Object rowObj : rows) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;

                            // 强制设置单元格字体
                            setTableCellFontSize5(cell, factory);

                            // 深度处理单元格内容
                            List<Object> cellContent = cell.getContent();
                            for (Object content : cellContent) {
                                if (content instanceof P) {
                                    P paragraph = (P) content;
                                    forceApplySize5FontToParagraph(paragraph, factory);
                                }
                            }
                        }
                    }
                }
            }

            log.info("设施表格字体优化完成，所有内容已设置为宋体五号字体");
        } catch (Exception e) {
            log.error("设施表格字体优化失败", e);
        }
    }


    /**
     * 生成动态的表格标题，根据实际的表格序号
     * @param partName 分部名称
     * @param tableIndex 表格序号
     * @return 动态生成的表格标题
     */
    private String generateDynamicTableTitle(String partName, int tableIndex) {
        return "表 6." + tableIndex + " " + partName + "主要问题及建议措施";
    }

    /**
     * 替换占位符为章节标题和表格（使用动态标题）
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符
     * @param sectionTitle 章节标题
     * @param table 表格对象
     * @param dynamicTableTitle 动态表格标题
     */
    private void replaceTablePlaceholderWithDynamicTitle(WordprocessingMLPackage wordMLPackage, String placeholder,
                                                         String sectionTitle, Tbl table, String dynamicTableTitle) {
        try {
            // 获取主文档部分
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();

            // 查找包含占位符的段落并替换
            boolean replacementDone = false;
            List<Object> docObjects = mainDocumentPart.getContent();

            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P p = (P) obj;
                    String paragraphText = p.toString();

                    if (paragraphText.contains("${" + placeholder + "}")) {
                        // 创建章节标题段落（居左，黑体小四，不加粗，可在导航视图中显示）
                        P sectionTitleParagraph = createNavigationTitleParagraph(sectionTitle);

                        // 创建动态表格标题段落
                        P tableTitleParagraph = createTableTitleParagraph(dynamicTableTitle);

                        // 替换占位符段落为章节标题，然后依次插入表格标题和表格
                        if (sectionTitleParagraph != null && tableTitleParagraph != null) {
                            docObjects.set(i, sectionTitleParagraph);      // 章节标题
                            docObjects.add(i + 1, tableTitleParagraph);    // 表格标题
                            docObjects.add(i + 2, table);                  // 表格
                            log.info("成功替换${{{}}}占位符为章节标题、动态表格标题和表格: {}", placeholder, dynamicTableTitle);
                        } else {
                            // 如果标题创建失败，直接替换为表格
                            docObjects.set(i, table);
                            log.warn("标题创建失败，直接替换${{{}}}占位符为表格", placeholder);
                        }

                        replacementDone = true;
                        break;
                    }
                }
            }

            if (!replacementDone) {
                log.warn("未找到${{{}}}占位符，尝试作为字符串替换", placeholder);
                // 如果没有找到占位符段落，尝试作为普通文本替换
                WordDocumentUtil.replacePlaceholder(wordMLPackage, placeholder, "");
            }
        } catch (Exception e) {
            log.error("替换表格占位符失败：{}", placeholder, e);
        }
    }

    /**
     * 创建章节标题段落（黑体小四字体，居左，不加粗，导航视图显示）
     * @param titleText 标题文本
     * @return 段落对象
     */
    private P createNavigationTitleParagraph(String titleText) {
        try {
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 创建段落
            P titleParagraph = factory.createP();

            // 设置段落属性
            PPr paragraphProperties = factory.createPPr();

            // 设置居左对齐
            Jc justification = factory.createJc();
            justification.setVal(JcEnumeration.LEFT);
            paragraphProperties.setJc(justification);

            // 设置为标题样式，使其在导航视图中显示
            PPrBase.PStyle pStyle = factory.createPPrBasePStyle();
            pStyle.setVal("Heading3"); // 使用标题3样式
            paragraphProperties.setPStyle(pStyle);

            // 设置大纲级别，确保在导航视图中显示
            PPrBase.OutlineLvl outlineLevel = factory.createPPrBaseOutlineLvl();
            outlineLevel.setVal(BigInteger.valueOf(2)); // 设置为第3级标题（0-based）
            paragraphProperties.setOutlineLvl(outlineLevel);

            titleParagraph.setPPr(paragraphProperties);

            // 创建运行
            R run = factory.createR();

            // 设置运行属性（黑体小四字体，不加粗）
            RPr runProperties = factory.createRPr();

            // 设置字体为黑体（包括所有字符类型）
            RFonts fonts = factory.createRFonts();
            fonts.setEastAsia("黑体");     // 东亚字符（中文）
            fonts.setHAnsi("黑体");       // 西文字符（英文字母）
            fonts.setAscii("黑体");       // ASCII字符（数字、英文等）
            fonts.setCs("黑体");          // 复杂脚本字符
            runProperties.setRFonts(fonts);

            // 设置字号为小四（12pt = 24半磅）
            HpsMeasure fontSize = factory.createHpsMeasure();
            fontSize.setVal(BigInteger.valueOf(24));
            runProperties.setSz(fontSize);
            runProperties.setSzCs(fontSize);

            // 不设置加粗（默认就是不加粗）

            run.setRPr(runProperties);

            // 添加文本
            Text text = factory.createText();
            text.setValue(titleText);
            run.getContent().add(text);

            // 将运行添加到段落
            titleParagraph.getContent().add(run);

            return titleParagraph;
        } catch (Exception e) {
            log.error("创建章节标题段落失败", e);
            return null;
        }
    }

    /**
     * 创建普通标题段落（宋体10号字体，居左，不在导航视图中显示）
     * @param titleText 标题文本
     * @return 段落对象
     */
    private P createSimpleTitleParagraph(String titleText) {
        try {
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 创建段落
            P titleParagraph = factory.createP();

            // 设置段落属性（居左）
            PPr paragraphProperties = factory.createPPr();
            Jc justification = factory.createJc();
            justification.setVal(JcEnumeration.LEFT); // 居左对齐
            paragraphProperties.setJc(justification);
            titleParagraph.setPPr(paragraphProperties);

            // 创建运行
            R run = factory.createR();

            // 设置运行属性（宋体10号字体）
            RPr runProperties = factory.createRPr();

            // 设置字体为宋体
            RFonts fonts = factory.createRFonts();
            fonts.setEastAsia("宋体");
            fonts.setHAnsi("宋体");
            runProperties.setRFonts(fonts);

            // 设置字号为五号（10.5pt = 21半磅）
            HpsMeasure fontSize = factory.createHpsMeasure();
            fontSize.setVal(BigInteger.valueOf(21));
            runProperties.setSz(fontSize);
            runProperties.setSzCs(fontSize);

            run.setRPr(runProperties);

            // 添加文本
            Text text = factory.createText();
            text.setValue(titleText);
            run.getContent().add(text);

            // 将运行添加到段落
            titleParagraph.getContent().add(run);

            return titleParagraph;
        } catch (Exception e) {
            log.error("创建简单标题段落失败", e);
            return null;
        }
    }

    /**
     * 创建表格标题段落（黑体五号字体，居中）
     * @param titleText 标题文本
     * @return 段落对象
     */
    private P createTableTitleParagraph(String titleText) {
        try {
            ObjectFactory factory = Context.getWmlObjectFactory();

            // 创建段落
            P titleParagraph = factory.createP();

            // 设置段落属性（居中）
            PPr paragraphProperties = factory.createPPr();
            Jc justification = factory.createJc();
            justification.setVal(JcEnumeration.CENTER); // 居中对齐
            paragraphProperties.setJc(justification);
            titleParagraph.setPPr(paragraphProperties);

            // 创建运行
            R run = factory.createR();

            // 设置运行属性（黑体五号字体）
            RPr runProperties = factory.createRPr();

            // 设置字体为黑体
            RFonts fonts = factory.createRFonts();
            fonts.setEastAsia("黑体");
            fonts.setHAnsi("黑体");
            runProperties.setRFonts(fonts);

            // 设置字号为五号（10.5pt = 21半磅）
            HpsMeasure fontSize = factory.createHpsMeasure();
            fontSize.setVal(BigInteger.valueOf(21));
            runProperties.setSz(fontSize);
            runProperties.setSzCs(fontSize);

            // 不设置加粗
//            BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
//            runProperties.setB(bold);
//            runProperties.setBCs(bold);

            run.setRPr(runProperties);

            // 添加文本
            Text text = factory.createText();
            text.setValue(titleText);
            run.getContent().add(text);

            // 将运行添加到段落
            titleParagraph.getContent().add(run);

            return titleParagraph;
        } catch (Exception e) {
            log.error("创建表格标题段落失败", e);
            return null;
        }
    }






    /**
     * 替换文档中的${maxNo}占位符和相关序号占位符
     * @param wordMLPackage Word文档对象
     * @param maxNo 最大编号值（分部数量+1）
     */
    private void replaceMaxNoPlaceholder(WordprocessingMLPackage wordMLPackage, int maxNo) {
        try {
            // 转换为字符串
            String maxNoStr = String.valueOf(maxNo);

            // 使用增强的占位符替换方法
            replaceAdvancedPlaceholder(wordMLPackage, "maxNo", maxNoStr);
            replaceAdvancedPlaceholder(wordMLPackage, "first", String.valueOf(maxNo));
            replaceAdvancedPlaceholder(wordMLPackage, "second", String.valueOf(maxNo + 1));
            replaceAdvancedPlaceholder(wordMLPackage, "third", String.valueOf(maxNo + 2));
            replaceAdvancedPlaceholder(wordMLPackage, "fourth", String.valueOf(maxNo + 3));

            log.info("成功替换占位符: maxNo={}, first={}, second={}, third={}, fourth={}",
                    maxNo, maxNo, maxNo + 1, maxNo + 2, maxNo + 3);
        } catch (Exception e) {
            log.error("替换占位符失败", e);
        }
    }

    /**
     * 增强的占位符替换方法，能够处理被分割的占位符
     * @param wordMLPackage Word文档对象
     * @param placeholder 占位符名称（不包含${}）
     * @param value 替换值
     */
    private void replaceAdvancedPlaceholder(WordprocessingMLPackage wordMLPackage, String placeholder, String value) {
        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();

            String targetPlaceholder = "${" + placeholder + "}";

            // 遍历所有文档对象
            for (Object obj : docObjects) {
                if (obj instanceof P) {
                    P paragraph = (P) obj;

                    // 获取段落的完整文本
                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

                    // 如果段落包含目标占位符，进行替换
                    if (paragraphText != null && paragraphText.contains(targetPlaceholder)) {
                        log.info("找到包含占位符[{}]的段落: {}", targetPlaceholder, paragraphText);

                        // 替换段落文本
                        String newText = paragraphText.replace(targetPlaceholder, value);
                        updateParagraphText(paragraph, newText);

                        log.info("成功替换占位符[{}]为[{}]: {} -> {}",
                                targetPlaceholder, value, paragraphText, newText);
                    }
                }
                // 处理表格中的占位符
                else if (obj instanceof Tbl) {
                    Tbl table = (Tbl) obj;
                    replaceTablePlaceholderAdvanced(table, targetPlaceholder, value);
                }
            }

            // 同时使用原有的WordDocumentUtil方法作为备用
            WordDocumentUtil.replacePlaceholder(wordMLPackage, placeholder, value);

        } catch (Exception e) {
            log.error("增强占位符替换失败: {}", placeholder, e);
        }
    }

    /**
     * 在表格中替换占位符
     * @param table 表格对象
     * @param targetPlaceholder 目标占位符（包含${}）
     * @param value 替换值
     */
    private void replaceTablePlaceholderAdvanced(Tbl table, String targetPlaceholder, String value) {
        try {
            List<Object> tableContent = table.getContent();

            for (Object rowObj : tableContent) {
                if (rowObj instanceof Tr) {
                    Tr row = (Tr) rowObj;
                    List<Object> cells = row.getContent();

                    for (Object cellObj : cells) {
                        if (cellObj instanceof Tc) {
                            Tc cell = (Tc) cellObj;
                            List<Object> cellContent = cell.getContent();

                            for (Object cellContentObj : cellContent) {
                                if (cellContentObj instanceof P) {
                                    P paragraph = (P) cellContentObj;
                                    String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

                                    if (paragraphText != null && paragraphText.contains(targetPlaceholder)) {
                                        log.info("在表格单元格中找到占位符[{}]: {}", targetPlaceholder, paragraphText);

                                        String newText = paragraphText.replace(targetPlaceholder, value);
                                        updateParagraphText(paragraph, newText);

                                        log.info("成功在表格中替换占位符[{}]为[{}]", targetPlaceholder, value);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("在表格中替换占位符失败: {}", targetPlaceholder, e);
        }
    }

    /**
     * 删除文档中多余的空行
     * @param wordMLPackage Word文档对象
     */
    private void removeEmptyLinesAtEndOfPages(WordprocessingMLPackage wordMLPackage) {
        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();

            // 从后往前遍历，删除多余的空段落
            for (int i = docObjects.size() - 1; i >= 0; i--) {
                Object obj = docObjects.get(i);

                if (obj instanceof P) {
                    P paragraph = (P) obj;

                    // 如果是空段落
                    if (isEmptyParagraph(paragraph)) {
                        boolean shouldRemove = false;

                        // 1. 删除文档末尾的空段落
                        if (i >= docObjects.size() - 2) {
                            shouldRemove = true;
                        }
                        // 2. 删除连续空段落中的多余部分
                        else {
                            // 检查后面的段落是否也是空的
                            int consecutiveEmptyCount = 0;
                            for (int j = i; j < docObjects.size() && j < i + 4; j++) {
                                Object nextObj = docObjects.get(j);
                                if (nextObj instanceof P && isEmptyParagraph((P)nextObj)) {
                                    consecutiveEmptyCount++;
                                } else {
                                    break;
                                }
                            }

                            // 如果连续空段落超过1个，删除多余的
                            if (consecutiveEmptyCount > 1) {
                                shouldRemove = true;
                            }
                            // 3. 检查是否是孤立的空段落，可能影响页面布局
                            else {
                                // 检查前面和后面是否都有实质内容
                                boolean hasContentBefore = false;
                                boolean hasContentAfter = false;

                                // 检查前面2个位置是否有内容
                                for (int k = i - 1; k >= Math.max(0, i - 2); k--) {
                                    Object prevObj = docObjects.get(k);
                                    if (prevObj instanceof Tbl ||
                                            (prevObj instanceof P && !isEmptyParagraph((P)prevObj))) {
                                        hasContentBefore = true;
                                        break;
                                    }
                                }

                                // 检查后面2个位置是否有内容
                                for (int k = i + 1; k <= Math.min(docObjects.size() - 1, i + 2); k++) {
                                    Object nextObj = docObjects.get(k);
                                    if (nextObj instanceof Tbl ||
                                            (nextObj instanceof P && !isEmptyParagraph((P)nextObj))) {
                                        hasContentAfter = true;
                                        break;
                                    }
                                }

                                // 如果前后都有内容，这个孤立的空行可能是多余的
                                if (hasContentBefore && hasContentAfter) {
                                    shouldRemove = true;
                                }
                            }
                        }

                        if (shouldRemove) {
                            docObjects.remove(i);
                            log.debug("删除多余的空行，位置: {}", i);
                        }
                    }
                }
            }

            log.info("完成删除文档中多余空行的处理");
        } catch (Exception e) {
            log.error("删除多余空行时发生错误", e);
        }
    }

    /**
     * 检查段落是否为空或只包含空白字符
     * @param paragraph 段落对象
     * @return true如果段落为空，false否则
     */
    private boolean isEmptyParagraph(P paragraph) {
        if (paragraph == null || paragraph.getContent() == null || paragraph.getContent().isEmpty()) {
            return true;
        }

        // 获取段落文本内容
        String paragraphText = WordDocumentUtil.getParagraphText(paragraph);

        // 检查是否为空或只包含空白字符
        return paragraphText == null || paragraphText.trim().isEmpty();
    }

    /**
     * 检查对象是否包含分页符
     * @param obj 文档对象
     * @return true如果包含分页符，false否则
     */
    private boolean containsPageBreak(Object obj) {
        if (obj instanceof P) {
            P paragraph = (P) obj;
            if (paragraph.getContent() != null) {
                for (Object content : paragraph.getContent()) {
                    if (content instanceof R) {
                        R run = (R) content;
                        if (run.getContent() != null) {
                            for (Object runContent : run.getContent()) {
                                if (runContent instanceof Br) {
                                    Br br = (Br) runContent;
                                    // 检查是否为分页符
                                    if (br.getType() != null && br.getType() == STBrType.PAGE) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

}
