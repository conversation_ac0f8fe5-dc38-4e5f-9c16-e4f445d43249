package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

/**
 * 路面跳车检测信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/tunnel/electric/chart")
@Api(tags = "图表实例代码")
public class ChartController extends BaseController {

    /**
     * 生成包含可编辑图表的Word报告
     */
    @Anonymous
    @GetMapping("/generateEditableReport")
    @ApiOperation(value = "生成可编辑图表报告", notes = "生成包含可编辑图表的Word报告")
    @Operation(summary = "生成可编辑图表报告", description = "生成包含可编辑图表的Word报告")
    public void generateEditableReport(HttpServletResponse response) {
        try {
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);
            
            // 创建Word文档
            XWPFDocument document = new XWPFDocument();

            // 添加文档标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("路面跳车检测系统分析报告");
            titleRun.setBold(true);
            titleRun.setFontSize(20);
            titleRun.setFontFamily("Microsoft YaHei");

            // 添加空行
            document.createParagraph();

            // 添加报告日期
            XWPFParagraph dateParagraph = document.createParagraph();
            dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun dateRun = dateParagraph.createRun();
            dateRun.setText("报告日期：" + java.time.LocalDate.now().toString());
            dateRun.setFontSize(12);
            dateRun.setFontFamily("Microsoft YaHei");

            // 添加空行
            document.createParagraph();

            // 添加概述部分
            XWPFParagraph overviewTitle = document.createParagraph();
            XWPFRun overviewTitleRun = overviewTitle.createRun();
            overviewTitleRun.setText("一、检测概述");
            overviewTitleRun.setBold(true);
            overviewTitleRun.setFontSize(14);
            overviewTitleRun.setFontFamily("Microsoft YaHei");

            XWPFParagraph overviewContent = document.createParagraph();
            XWPFRun overviewRun = overviewContent.createRun();
            overviewRun.setText("本报告基于智能高速公路路面跳车检测系统，对2024年上半年（1-6月）的检测数据进行统计分析。" +
                              "系统通过先进的传感器技术，实时监测路面状况，及时发现跳车现象，为道路维护提供科学依据。");
            overviewRun.setFontSize(12);
            overviewRun.setFontFamily("Microsoft YaHei");

            // 添加空行
            document.createParagraph();

            // 添加图表标题
            XWPFParagraph chartTitle = document.createParagraph();
            XWPFRun chartTitleRun = chartTitle.createRun();
            chartTitleRun.setText("二、月度检测数据雷达图分析");
            chartTitleRun.setBold(true);
            chartTitleRun.setFontSize(14);
            chartTitleRun.setFontFamily("Microsoft YaHei");

            // 添加空行
            document.createParagraph();

            try {
                // 创建图表段落
                XWPFParagraph chartParagraph = document.createParagraph();
                chartParagraph.setAlignment(ParagraphAlignment.CENTER);
                
                // 创建图表
                XWPFChart chart = document.createChart(15 * 360000, 10 * 360000); // 15cm x 10cm
                
                // 设置图表标题
                chart.setTitleText("路面跳车检测月度统计雷达图");
                chart.setTitleOverlay(false);
                
                // 创建图例
                XDDFChartLegend legend = chart.getOrAddLegend();
                legend.setPosition(LegendPosition.BOTTOM);
                
                // 创建分类轴（用于雷达图的各个维度）
                XDDFCategoryAxis categoryAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
                categoryAxis.setTitle("月份");
                
                // 创建数值轴（用于雷达图的数值）
                XDDFValueAxis valueAxis = chart.createValueAxis(AxisPosition.LEFT);
                valueAxis.setTitle("检测次数");
                
                // 创建数据源
                XDDFDataSource<String> months = XDDFDataSourcesFactory.fromArray(
                    new String[]{"1月", "2月", "3月", "4月", "5月", "6月"}
                );
                
                XDDFNumericalDataSource<Integer> severeData = XDDFDataSourcesFactory.fromArray(
                    new Integer[]{12, 8, 15, 10, 18, 22}
                );
                
                XDDFNumericalDataSource<Integer> mildData = XDDFDataSourcesFactory.fromArray(
                    new Integer[]{25, 30, 28, 35, 32, 38}
                );
                
                // 创建雷达图
                XDDFChartData data = chart.createData(ChartTypes.RADAR, categoryAxis, valueAxis);
                
                // 添加数据系列
                XDDFChartData.Series series1 = data.addSeries(months, severeData);
                series1.setTitle("严重跳车", null);
                
                XDDFChartData.Series series2 = data.addSeries(months, mildData);
                series2.setTitle("轻微跳车", null);
                
                // 绘制图表
                chart.plot(data);
                
                // 设置雷达图样式
                XDDFRadarChartData radarChart = (XDDFRadarChartData) data;
                radarChart.setStyle(RadarStyle.FILLED); // 设置为填充样式的雷达图
                
            } catch (Exception e) {
                logger.warn("创建图表失败，添加数据表格替代", e);
                
                // 如果图表创建失败，添加数据表格
                XWPFTable table = document.createTable(3, 7);
                
                // 设置表格样式
                table.setWidth("100%");
                
                // 表头
                XWPFTableRow headerRow = table.getRow(0);
                headerRow.getCell(0).setText("检测类型");
                headerRow.getCell(1).setText("1月");
                headerRow.getCell(2).setText("2月");
                headerRow.getCell(3).setText("3月");
                headerRow.getCell(4).setText("4月");
                headerRow.getCell(5).setText("5月");
                headerRow.getCell(6).setText("6月");
                
                // 严重跳车数据
                XWPFTableRow severeRow = table.getRow(1);
                severeRow.getCell(0).setText("严重跳车");
                severeRow.getCell(1).setText("12");
                severeRow.getCell(2).setText("8");
                severeRow.getCell(3).setText("15");
                severeRow.getCell(4).setText("10");
                severeRow.getCell(5).setText("18");
                severeRow.getCell(6).setText("22");
                
                // 轻微跳车数据
                XWPFTableRow mildRow = table.getRow(2);
                mildRow.getCell(0).setText("轻微跳车");
                mildRow.getCell(1).setText("25");
                mildRow.getCell(2).setText("30");
                mildRow.getCell(3).setText("28");
                mildRow.getCell(4).setText("35");
                mildRow.getCell(5).setText("32");
                mildRow.getCell(6).setText("38");
            }

            // 添加数据分析
            document.createParagraph();
            XWPFParagraph analysisTitle = document.createParagraph();
            XWPFRun analysisTitleRun = analysisTitle.createRun();
            analysisTitleRun.setText("三、数据分析");
            analysisTitleRun.setFontSize(14);
            analysisTitleRun.setBold(true);
            analysisTitleRun.setFontFamily("Microsoft YaHei");

            XWPFParagraph analysisParagraph = document.createParagraph();
            XWPFRun analysisRun = analysisParagraph.createRun();
            analysisRun.setText("通过雷达图可以直观地观察到：\n" +
                              "1. 数据分布模式：严重跳车和轻微跳车在各月份的分布呈现不同的雷达形状\n" +
                              "2. 峰值对比：6月份严重跳车达到峰值22次，4月份轻微跳车达到峰值35次\n" +
                              "3. 变化趋势：严重跳车在春夏季节呈上升趋势，轻微跳车相对稳定\n" +
                              "4. 整体评估：雷达图显示检测工作覆盖全面，各月份数据完整，便于识别异常模式");
            analysisRun.setFontSize(12);
            analysisRun.setFontFamily("Microsoft YaHei");

            // 添加结论与建议
            document.createParagraph();
            XWPFParagraph conclusionTitle = document.createParagraph();
            XWPFRun conclusionTitleRun = conclusionTitle.createRun();
            conclusionTitleRun.setText("四、结论与建议");
            conclusionTitleRun.setFontSize(14);
            conclusionTitleRun.setBold(true);
            conclusionTitleRun.setFontFamily("Microsoft YaHei");

            XWPFParagraph conclusionParagraph = document.createParagraph();
            XWPFRun conclusionRun = conclusionParagraph.createRun();
            conclusionRun.setText("基于雷达图多维度分析结果，建议：\n" +
                                "1. 重点监控：针对雷达图中显示的异常突出点（如6月严重跳车）加强监控\n" +
                                "2. 模式识别：利用雷达图的形状特征，建立检测模式库，提前预警\n" +
                                "3. 平衡调节：对雷达图中显示不平衡的月份进行针对性维护\n" +
                                "4. 持续优化：定期更新雷达图数据，动态调整检测策略和维护计划");
            conclusionRun.setFontSize(12);
            conclusionRun.setFontFamily("Microsoft YaHei");

            // 添加报告结尾
            document.createParagraph();
            XWPFParagraph endParagraph = document.createParagraph();
            endParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun endRun = endParagraph.createRun();
            endRun.setText("智能高速公路管理系统\n路面检测分析中心");
            endRun.setFontSize(12);
            endRun.setFontFamily("Microsoft YaHei");
            endRun.setItalic(true);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("路面跳车检测可编辑图表报告.docx", "UTF-8"));

            // 输出Word文档
            OutputStream outputStream = response.getOutputStream();
            document.write(outputStream);
            document.close();
            outputStream.close();

        } catch (IOException e) {
            logger.error("生成可编辑图表报告失败", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "生成报告失败");
            } catch (IOException ioException) {
                logger.error("发送错误响应失败", ioException);
            }
        }
    }


} 