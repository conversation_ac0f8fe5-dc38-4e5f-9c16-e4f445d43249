package com.tunnel.web.controller;

import cn.hutool.core.date.DateUtil;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.word.WordDocumentUtil;
import com.tunnel.domain.*;
import com.tunnel.mapper.*;

import com.tunnel.service.TunnelInfoService;
import com.tunnel.web.controller.dto.RoadReportInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.poi.xwpf.usermodel.*;

/**
 * 导出路段报告
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/tunnel/electric/roadExport")
@Slf4j
public class RoadExportController extends BaseController {

    @Autowired
    private CheckFacilityMapper checkFacilityMapper;
    @Resource
    private TunnelCheckMapper tunnelCheckMapper;
    @Resource
    private TunnelInfoService tunnelInfoService;
    @Resource
    private FacilityInfoMapper facilityInfoMapper;
    @Resource
    private TunnelInfoMapper tunnelInfoMapper;
    @Resource
    private CheckUserMapper checkUserMapper;
    @Resource
    private CheckRoadDemandMapper checkRoadDemandMapper;
    @Resource
    private TunnelInfoController tunnelInfoController;
    @Resource
    private CheckEnumRelationMapper checkEnumRelationMapper;
    @Resource
    private CheckEnumMapper checkEnumMapper;


    /**
     * 生成隧道表格并替换模板中的${roadTunnelList}占位符
     * @param wordMLPackage Word文档对象
     * @param roadTunnelList 隧道信息列表
     */
    private void generateAndReplaceRoadTunnelTable(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> roadTunnelList,String replacePlaceholder) {
        if (roadTunnelList == null || roadTunnelList.isEmpty()) {
            log.warn("隧道信息列表为空，无法生成表格");
            return;
        }

        log.info("开始生成隧道表格，共 {} 条隧道数据", roadTunnelList.size());

        // 创建表格：表头2行 + 数据行
        int totalRows = 2 + roadTunnelList.size();
        int totalCols = 7; // 序号、隧道名称、隧道类型、上行起点桩号、上行终点桩号、上行长度、下行长度

        Tbl table = WordDocumentUtil.createTable(totalRows, totalCols,1200L);

        // 设置表格列宽
        TblGrid tblGrid = table.getTblGrid();
        tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(800));  // 序号列
        tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(1400)); // 隧道名称列
        tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(1200)); // 隧道类型列
        tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(1200)); // 上行起点桩号列
        tblGrid.getGridCol().get(4).setW(BigInteger.valueOf(1200)); // 上行终点桩号列
        tblGrid.getGridCol().get(5).setW(BigInteger.valueOf(1000)); // 上行长度列
        tblGrid.getGridCol().get(6).setW(BigInteger.valueOf(1000)); // 下行长度列

        WordDocumentUtil.setCellText(table, 0, 0, "序号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 1, "隧道名称", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 2, "隧道类型", 10, true, false, 2);

        // 合并“起点桩号”
        WordDocumentUtil.setCellText(table, 0, 3, "起点桩号", 10, true, false, 2);

        // 合并“长度（m）”
        WordDocumentUtil.setCellText(table, 0, 5, "长度（m）", 10, true, false, 2);

        // 定义要合并的区域：{{起始列, 结束列}, {起始列, 结束列}}
        int[][] mergeRanges = {{3, 4},  {5, 6}};

        WordDocumentUtil.mergeMultipleCellRanges(table, 0, mergeRanges);

        // 垂直合并前三列
        mergeVerticalCells(table, 0, 0, 1);
        mergeVerticalCells(table, 1, 0, 1);
        mergeVerticalCells(table, 2, 0, 1);

        // 创建第二行表头
        WordDocumentUtil.setCellText(table, 1, 3, "上行", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 4, "下行", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 5, "上行", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 6, "下行", 10, true, false, 2);

        // 填充数据行
        for (int i = 0; i < roadTunnelList.size(); i++) {
            TunnelInfo tunnel = roadTunnelList.get(i);
            int rowIndex = i + 2; // 数据从第3行开始

            // 序号（从1开始）
            WordDocumentUtil.setCellText(table, rowIndex, 0, String.valueOf(i + 1), 10, false, false, 2);

            // 隧道名称
            String tunnelName = tunnel.getTunnelName() != null ? tunnel.getTunnelName() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 1, tunnelName, 10, false, false, 2);

            // 隧道类型
            WordDocumentUtil.setCellText(table, rowIndex, 2, tunnel.getTunnelType(), 10, false, false, 2);

            // 上行起点桩号
            String upStartCode = tunnel.getUpStartCode() != null ? tunnel.getUpStartCode() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 3, upStartCode, 10, false, false, 2);

            // 下行起点桩号
            String downStartCode = tunnel.getDownStartCode() != null ? tunnel.getDownStartCode() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 4, downStartCode, 10, false, false, 2);

            // 上行长度
            String upTunnelLength = tunnel.getUpTunnelLength() != null ? tunnel.getUpTunnelLength().toString() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 5, upTunnelLength, 10, false, false, 2);

            // 下行长度
            String downTunnelLength = tunnel.getDownTunnelLength() != null ? tunnel.getDownTunnelLength().toString() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 6, downTunnelLength, 10, false, false, 2);
        }
        //替换占位符对应的表格
        WordDocumentUtil.findAndGenerateTable(wordMLPackage,table,replacePlaceholder);
    }





    
    /**
     * 垂直合并单元格
     * @param table 表格对象
     * @param colIndex 列索引
     * @param startRow 开始行索引
     * @param endRow 结束行索引
     */
    private void mergeVerticalCells(Tbl table, int colIndex, int startRow, int endRow) {
        try {
            if (table == null || colIndex < 0 || startRow < 0 || endRow <= startRow) {
                log.error("垂直合并单元格参数无效: colIndex={}, startRow={}, endRow={}", colIndex, startRow, endRow);
                return;
            }

            List<Object> rows = table.getContent();
            if (rows.size() <= endRow) {
                log.error("垂直合并单元格行索引超出范围: endRow={}, rows.size()={}", endRow, rows.size());
                return;
            }

            // 获取第一个单元格
            Tr firstRow = (Tr) rows.get(startRow);
            List<Object> cells = firstRow.getContent();

            if (cells.size() <= colIndex) {
                log.error("垂直合并单元格列索引超出范围: colIndex={}, cells.size()={}", colIndex, cells.size());
                return;
            }

            // 获取第一个单元格
            Tc firstCell = (Tc) cells.get(colIndex);

            // 设置垂直合并属性
            TcPr tcPr = firstCell.getTcPr();
            if (tcPr == null) {
                ObjectFactory factory = new ObjectFactory();
                tcPr = factory.createTcPr();
                firstCell.setTcPr(tcPr);
            }

            // 设置为"开始"垂直合并
            ObjectFactory factory = new ObjectFactory();
            TcPrInner.VMerge vMerge = factory.createTcPrInnerVMerge();
            vMerge.setVal("restart");
            tcPr.setVMerge(vMerge);

            // 处理后续单元格
            for (int i = startRow + 1; i <= endRow; i++) {
                Tr row = (Tr) rows.get(i);
                List<Object> rowCells = row.getContent();

                if (rowCells.size() <= colIndex) {
                    continue;
                }

                Tc cell = (Tc) rowCells.get(colIndex);
                TcPr cellPr = cell.getTcPr();

                if (cellPr == null) {
                    cellPr = factory.createTcPr();
                    cell.setTcPr(cellPr);
                }

                // 设置为"继续"垂直合并
                TcPrInner.VMerge cellVMerge = factory.createTcPrInnerVMerge();
                // 不设置val，默认为"continue"
                cellPr.setVMerge(cellVMerge);

                // 清空单元格内容，防止显示冗余内容
                cell.getContent().clear();

                // 添加一个空段落，确保单元格不为空
                org.docx4j.wml.P p = factory.createP();
                cell.getContent().add(p);
            }

            log.info("垂直合并单元格成功: colIndex={}, startRow={}, endRow={}", colIndex, startRow, endRow);
        } catch (Exception e) {
            log.error("垂直合并单元格失败", e);
        }
    }


    /**
     * 导出路段报告word版本
     */
    @Log(title = "导出路段报告word版本", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRoadReportWord")
    public void exportRoadReportWord(HttpServletResponse response, Task task) {
        //隧道ID不能为空
        if (Objects.isNull(task.getRoadName())) {
            throw new ServiceException("路段名称不能为空");
        }
        //查询当前隧道所在的路段下的所有隧道
        List<TunnelInfo> roadTunnelList = tunnelInfoMapper.selectListByRoadName(task.getRoadName());
        if(CollectionUtils.isEmpty(roadTunnelList)){
            throw new ServiceException("无此路段下的隧道");
        }
        TunnelInfo tunnelInfo = new TunnelInfo();
        tunnelInfo.setRoadName(task.getRoadName());
        List<TunnelInfo> tunnelPartScoreList = tunnelInfoService.selectTunnelInfoList(tunnelInfo);
        // 使用多线程并发获取评分数据
        tunnelInfoController.calculateScoresConcurrently(tunnelPartScoreList);



        String section = roadTunnelList.get(0).getSection();
        List<Long> tunnelIds = roadTunnelList.stream().map(TunnelInfo::getId).collect(Collectors.toList());
        //查询隧道资产,根据分部分项来统计数量
        List<String> partNameList = facilityInfoMapper.selectDistinctPartList(tunnelIds);

        String facilityRange = "表4.1～4."+partNameList.size();
        String partDesc = String.join("、", partNameList);

        //查询当前路段下的所有隧道对应的检测任务 对应的检测人员
        List<CheckUser> checkUserList = checkUserMapper.selectDistinctListByTunnelIds(tunnelIds,task.getId());
        //查询当前隧道的所有检测任务
        List<TunnelCheck> tunnelCheckList = tunnelCheckMapper.selectListByTunnelIds(tunnelIds,task.getId());
        //检测条件
        TunnelCheck tunnelCheckCondition = tunnelCheckList.stream()
                .filter(v -> v.getLowTemperature() != null && v.getHighTemperature() != null && v.getLowHumidity() != null && v.getHighHumidity() != null &&  StringUtils.isNotEmpty(v.getStartTime()) && StringUtils.isNotEmpty(v.getEndTime()))
                .findFirst().orElse(null);
        //生成检测条件描述
        String checkCondition = "";
        if (tunnelCheckCondition != null) {
            String startTime = tunnelCheckCondition.getStartTime() != null ? DateUtil.format(DateUtil.parse(tunnelCheckCondition.getStartTime()), "yyyy年MM月dd日") : "";
            String endTime = tunnelCheckCondition.getEndTime() != null ? DateUtil.format(DateUtil.parse(tunnelCheckCondition.getEndTime()), "yyyy年MM月dd日") : "";
            String lowTemp = tunnelCheckCondition.getLowTemperature() != null ? tunnelCheckCondition.getLowTemperature().toString() : "";
            String highTemp = tunnelCheckCondition.getHighTemperature() != null ? tunnelCheckCondition.getHighTemperature().toString() : "";
            String lowHumidity = tunnelCheckCondition.getLowHumidity() != null ? tunnelCheckCondition.getLowHumidity().toString() : "";
            String highHumidity = tunnelCheckCondition.getHighHumidity() != null ? tunnelCheckCondition.getHighHumidity().toString() : "";
            checkCondition = String.format("（%s-%s，检测时环境状况：温度（%s-%s）℃，湿度（%s-%s）%%RH。）", startTime, endTime, lowTemp, highTemp, lowHumidity, highHumidity);
        }
        //去重当前检测设备ID
        List<String> checkFacilityIds = tunnelCheckList.stream().map(TunnelCheck::getCheckFacilityIds).distinct().collect(Collectors.toList());
        //隧道资产设备ID
        List<Long> tunnelFacilityIds = tunnelCheckList.stream().map(TunnelCheck::getFacilityId).distinct().collect(Collectors.toList());
        List<Long> checkFacilityIdList = Lists.newArrayList();
        checkFacilityIds.forEach(item -> {
            List<Long> ids = Lists.newArrayList();
            if(StringUtils.isNotEmpty(item)){
                String[] split = item.split(",");
                for (String id : split) {
                    ids.add(Long.valueOf(id));
                }
                checkFacilityIdList.addAll(ids);
            }
        });
        //检测设备
        List<CheckFacility> checkFacilityList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(checkFacilityIdList)){
            checkFacilityList = checkFacilityMapper.selectCheckFacilityByCheckFacilityIds(checkFacilityIdList);
        }
        //查询检测设备对应的路段检测要求
         List<CheckRoadDemand> checkContentList=checkRoadDemandMapper.selectListByCheckFacilityIds(tunnelFacilityIds);
        // 生成报告基础信息
        RoadReportInfo reportInfo = this.generateRoadReportInfo(task, roadTunnelList, partNameList,section);
        String pageTitle = reportInfo.getPageTitle();
        String projectName = reportInfo.getProjectName();
        String roadDescription = reportInfo.getRoadDescription();
        try {
            // 1. 读取模板文件
            InputStream is = this.getClass().getResourceAsStream("/public/word/road_check_report.docx");
            if (is == null) {
                log.error("找不到报告模板文件");
                throw new ServiceException("找不到报告模板文件");
            }
            // 2. 加载Word文档
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(is);
            // 3. 替换文档中的变量
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "projectName", projectName);
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "roadName", task.getRoadName());
            WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "section", section);
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "section", section);
            WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "roadName", task.getRoadName());
            // 特别处理表格中的日期占位符
            WordDocumentUtil.replaceTableCellPlaceholder(wordMLPackage, "checkDate", DateUtil.format(DateUtil.date(), "yyyy年MM月dd日"));
            //路段描述
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "roadDescription", roadDescription);
            //检测条件
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "checkCondition", checkCondition);
            //检测条件 表4.1～4.5
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "facilityRange", facilityRange);
            // 4. 替换页眉中的变量
            WordDocumentUtil.replaceHeaderPlaceholder(wordMLPackage, "pageTitle", pageTitle);
            // 生成隧道列表并替换${roadTunnelList}占位符
            WordDocumentUtil.generateRoadTunnelList(wordMLPackage, roadTunnelList, null);
            // 5. 生成设施表格并替换${tunnelTableList}占位符
            this.generateAndReplaceRoadTunnelTable(wordMLPackage, roadTunnelList,"tunnelTableList");
            // 6. 生成检测人员列表并替换${mainCheckerList}占位符
            this.generateAndReplaceMainCheckerList(wordMLPackage, checkUserList,"mainCheckerList");
            // 7.设备及仪器清单列表并替换${checkFacilityList}占位符
            this.generateAndReplaceCheckFacilityList(wordMLPackage, checkFacilityList,"checkFacilityList");
            // 8.路段检测方法和要求列表并替换${checkContentList}占位符
            this.generateAndReplaceCheckContentList(wordMLPackage, checkContentList,"checkContentList");
            // 生成分部名称列表并替换${partNameList}占位符（放在最后，避免被其他操作覆盖）
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "partDesc", partDesc);
            //生成整个线路商的所有隧道的各个分项的分数和描述性话语
            this.generateTunnelItemPartScoreAndDesc(wordMLPackage, tunnelPartScoreList, tunnelCheckList, "tunnelPartScoreList");
            //使用tunnelPartScoreList来生成整个路段的所有隧道的得分
            this.generateTunnelYearScore(wordMLPackage, tunnelPartScoreList, "yearTunnelList", task.getRoadName());
            //使用tunnelPartScoreList来生成整个路段的所有隧道的得分,以分部的维度统计平均分
            this.generatePartAvgScoreListScore(wordMLPackage, tunnelPartScoreList,tunnelCheckList, "partAvgScoreList");
            //使用tunnelCheckList,按照partCode+questionDesc来进行分组,统计这个维度下的隧道名称,缺陷数量,缺陷数量/总缺陷数的占比
            this.generateQuestionScoreList(wordMLPackage, tunnelCheckList, "questionDescList", task.getRoadName());
            //使用tunnelCheckList,按照partCode分组,展示不同的分部下面的缺陷和questionDesc
            this.generateQuestionScoreListByPart(wordMLPackage, tunnelCheckList, "partQuestionDescList");
            //自动生成目录
            WordDocumentUtil.generateAutoTableOfContents(wordMLPackage, "${autoTOC}");
            //使用tunnelPartScoreList参数,来生成分值的分类category的饼图,将上面得到的wordMLPackage,转换为临时文件,然后再加载临时文件,使用poi绘制饼图并直接导出
            XWPFDocument document = this.generateChartsInDocument(wordMLPackage, tunnelPartScoreList, tunnelCheckList);
            // 导出包含图表的文档
            exportDocumentWithChart(document, response, pageTitle);
        } catch (Exception e) {
            log.error("处理Word模板异常", e);
            throw new ServiceException("处理Word模板异常: " + e.getMessage());
        }
    }


    /**
     * 生成主要检测人员名单表格并替换占位符
     * @param wordMLPackage Word文档对象
     * @param checkUserList 检测人员列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateAndReplaceMainCheckerList(WordprocessingMLPackage wordMLPackage, List<CheckUser> checkUserList, String replacePlaceholder) {
        if (checkUserList == null || checkUserList.isEmpty()) {
            log.warn("检测人员列表为空，无法生成表格");
            return;
        }
        log.info("开始生成检测人员表格，共 {} 条人员数据", checkUserList.size());
        // 创建表格：表头1行 + 数据行
        int totalRows = 1 + checkUserList.size();
        int totalCols = 4; // 岗位、姓名、职业资格证书编号、职称
        Tbl table = WordDocumentUtil.createTable(totalRows, totalCols, 1200L);
        // 设置表格列宽
        TblGrid tblGrid = table.getTblGrid();
        tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(1200)); // 岗位列
        tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(1000)); // 姓名列
        tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(2000)); // 职业资格证书编号列
        tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(1200)); // 职称列
        // 设置表头
        WordDocumentUtil.setCellText(table, 0, 0, "岗位", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 1, "姓名", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 2, "职业资格证书编号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 3, "职称", 10, true, false, 2);

        // 填充数据行
        for (int i = 0; i < checkUserList.size(); i++) {
            CheckUser user = checkUserList.get(i);
            int rowIndex = i + 1; // 数据从第2行开始
            // 岗位
            String position = user.getPosition() != null ? user.getPosition() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 0, position, 10, false, false, 2);
            // 姓名
            String userName = user.getUserName() != null ? user.getUserName() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 1, userName, 10, false, false, 2);
            // 职业资格证书编号
            String certificateNo = user.getCertificateNo() != null ? user.getCertificateNo() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 2, certificateNo, 10, false, false, 2);
            // 职称
            String jobTitle = user.getTitle() != null ? user.getTitle() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 3, jobTitle, 10, false, false, 2);
        }
        // 替换占位符对应的表格
        WordDocumentUtil.findAndGenerateTable(wordMLPackage, table, replacePlaceholder);
    }

    /**
     * 生成设备及仪器清单表格并替换占位符
     * @param wordMLPackage Word文档对象
     * @param checkFacilityList 检测设备列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateAndReplaceCheckFacilityList(WordprocessingMLPackage wordMLPackage, List<CheckFacility> checkFacilityList, String replacePlaceholder) {
        if (checkFacilityList == null || checkFacilityList.isEmpty()) {
            log.warn("检测设备列表为空，无法生成表格");
            return;
        }
        log.info("开始生成检测设备表格，共 {} 条设备数据", checkFacilityList.size());
        // 创建表格：表头1行 + 数据行
        int totalRows = 1 + checkFacilityList.size();
        int totalCols = 6; // 序号、设备名称、型号、设备编号、主要用途、校准周期

        Tbl table = WordDocumentUtil.createTable(totalRows, totalCols, 1200L);
        // 设置表格列宽
        TblGrid tblGrid = table.getTblGrid();
        tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(800));  // 序号列
        tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(1400)); // 设备名称列
        tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(1200)); // 型号列
        tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(1200)); // 设备编号列
        tblGrid.getGridCol().get(4).setW(BigInteger.valueOf(1400)); // 主要用途列
        tblGrid.getGridCol().get(5).setW(BigInteger.valueOf(1200)); // 校准周期列
        // 设置表头
        WordDocumentUtil.setCellText(table, 0, 0, "序号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 1, "设备名称", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 2, "型号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 3, "设备编号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 4, "主要用途", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 5, "校准周期", 10, true, false, 2);
        // 填充数据行
        for (int i = 0; i < checkFacilityList.size(); i++) {
            CheckFacility facility = checkFacilityList.get(i);
            int rowIndex = i + 1; // 数据从第2行开始
            // 序号（从1开始）
            WordDocumentUtil.setCellText(table, rowIndex, 0, String.valueOf(i + 1), 10, false, false, 2);
            // 设备名称
            String facilityName = facility.getName() != null ? facility.getName() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 1, facilityName, 10, false, false, 2);
            // 型号
            String model = facility.getModel() != null ? facility.getModel() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 2, model, 10, false, false, 2);
            // 设备编号
            String facilityCode = facility.getCode() != null ? facility.getCode() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 3, facilityCode, 10, false, false, 2);
            // 主要用途
            String mainPurpose = facility.getMainUsage() != null ? facility.getMainUsage() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 4, mainPurpose, 10, false, false, 2);
            // 校准周期（使用起始时间和结束时间拼接）
            String startPeriod = facility.getStartPeriod() != null ? facility.getStartPeriod() : "";
            String endPeriod = facility.getEndPeriod() != null ? facility.getEndPeriod() : "";
            String calibrationPeriod = "";
            if (StringUtils.isNotEmpty(startPeriod) && StringUtils.isNotEmpty(endPeriod)) {
                calibrationPeriod = startPeriod + " - " + endPeriod;
            } else if (StringUtils.isNotEmpty(startPeriod)) {
                calibrationPeriod = startPeriod;
            } else if (StringUtils.isNotEmpty(endPeriod)) {
                calibrationPeriod = endPeriod;
            }
            WordDocumentUtil.setCellText(table, rowIndex, 5, calibrationPeriod, 10, false, false, 2);
        }
        // 替换占位符对应的表格
        WordDocumentUtil.findAndGenerateTable(wordMLPackage, table, replacePlaceholder);
    }

    /**
     * 生成路段检测方法和要求列表并替换占位符
     * @param wordMLPackage Word文档对象
     * @param checkContentList 检测内容列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateAndReplaceCheckContentList(WordprocessingMLPackage wordMLPackage, List<CheckRoadDemand> checkContentList, String replacePlaceholder) {
        if (checkContentList == null || checkContentList.isEmpty()) {
            log.warn("检测内容列表为空，无法生成表格");
            // 即使为空也要替换占位符，避免模板中留下占位符
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, replacePlaceholder, "");
            return;
        }
        log.info("开始生成检测内容表格，共 {} 条数据", checkContentList.size());
        try {
            // 找到占位符并替换为所有分部的内容
            findAndReplaceWithPartContents(wordMLPackage, checkContentList, replacePlaceholder);
        } catch (Exception e) {
            log.error("生成检测内容表格失败", e);
            // 发生异常时也要替换占位符
        }
    }

    /**
     * 查找占位符并替换为分部内容
     * 参考 WordDocumentUtil.findAndGenerateTable 方法的实现
     * @param wordMLPackage Word文档对象
     * @param sortedList 排序后的检测内容列表
     * @param replacePlaceholder 占位符
     */
    private void findAndReplaceWithPartContents(WordprocessingMLPackage wordMLPackage, List<CheckRoadDemand> sortedList, String replacePlaceholder) {
        String searchPlaceholder = "${" + replacePlaceholder + "}";
        // 获取主文档部分
        org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
        // 查找包含占位符的段落并替换为所有分部内容
        List<Object> docObjects = mainDocumentPart.getContent();
        for (int i = 0; i < docObjects.size(); i++) {
            Object obj = docObjects.get(i);
            if (obj instanceof org.docx4j.wml.P) {
                org.docx4j.wml.P paragraph = (org.docx4j.wml.P) obj;
                String paragraphText = String.valueOf(paragraph);
                log.debug("段落文本: [{}]", paragraphText);
                if (paragraphText.contains(searchPlaceholder)) {
                    log.info("找到占位符: {}", searchPlaceholder);
                    //移除占位符
                    WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, replacePlaceholder, "");
                    // 生成所有分部的内容
                    List<Object> allPartContents = generateAllPartContents(sortedList);
                    // 在原位置插入所有分部内容
                    for (int j = 0; j < allPartContents.size(); j++) {
                        docObjects.add(i + j, allPartContents.get(j));
                    }
                    log.info("成功替换 {} 占位符为检测内容表格", searchPlaceholder);
                    break;
                }
            }
        }
    }

    /**
     * 生成所有分部的内容（标题 + 表格）
     * @param sortedList 排序后的检测内容列表
     * @return 生成的所有内容对象列表
     */
    private List<Object> generateAllPartContents(List<CheckRoadDemand> sortedList) {
        List<Object> allContents = new ArrayList<>();
        String currentPartCode = null;
        int partIndex = 1;
        for (int i = 0; i < sortedList.size(); i++) {
            CheckRoadDemand current = sortedList.get(i);
            // 如果是新的分部，创建标题和表格
            if (!Objects.equals(currentPartCode, current.getPartCode())) {
                final String finalCurrentPartCode = current.getPartCode();
                currentPartCode = finalCurrentPartCode;
                // 收集当前分部的所有数据
                List<CheckRoadDemand> currentPartData = sortedList.stream()
                        .filter(item -> Objects.equals(item.getPartCode(), finalCurrentPartCode))
                        .collect(Collectors.toList());
                // 生成当前分部的内容
                List<Object> partContents = createPartContentObjects(currentPartData, partIndex, current.getPartName());
                allContents.addAll(partContents);
                partIndex++;
            }
        }
        return allContents;
    }

    /**
     * 创建单个分部的内容对象（标题 + 表格标题 + 表格 + 空行）
     * @param partData 当前分部的数据
     * @param partIndex 分部序号
     * @param partName 分部名称
     * @return 内容对象列表
     */
    private List<Object> createPartContentObjects(List<CheckRoadDemand> partData, int partIndex, String partName) {
        List<Object> contentObjects = new ArrayList<>();
        
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 1. 创建大标题段落（黑体，四号，居左，显示在大纲视图）
            String titleText = "4." + partIndex + " " + partName + "检测内容和方法";
            org.docx4j.wml.P titleParagraph = factory.createP();
            
            // 设置段落属性 - 大纲级别
            PPr titlePPr = factory.createPPr();
            titlePPr.setOutlineLvl(factory.createPPrBaseOutlineLvl());
            titlePPr.getOutlineLvl().setVal(BigInteger.valueOf(2)); // 设置为2级标题
            
            // 设置左对齐
            Jc titleJc = factory.createJc();
            titleJc.setVal(JcEnumeration.LEFT);
            titlePPr.setJc(titleJc);
            
            titleParagraph.setPPr(titlePPr);
            
            // 创建标题文本
            org.docx4j.wml.R titleRun = factory.createR();
            RPr titleRPr = factory.createRPr();
            
            // 设置黑体
            BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
            titleRPr.setB(bold);
            
            // 设置四号字体 (14pt)
            HpsMeasure fontSize = factory.createHpsMeasure();
            fontSize.setVal(BigInteger.valueOf(28)); // Word中字体大小是半点，所以14pt = 28
            titleRPr.setSz(fontSize);
            titleRPr.setSzCs(fontSize);
            
            titleRun.setRPr(titleRPr);
            
            Text titleTextElement = factory.createText();
            titleTextElement.setValue(titleText);
            titleRun.getContent().add(titleTextElement);
            titleParagraph.getContent().add(titleRun);
            
            contentObjects.add(titleParagraph);
            
            // 2. 创建表格标题段落（居中，黑体，五号）
            org.docx4j.wml.P tableTitleParagraph = factory.createP();
            PPr tableTitlePPr = factory.createPPr();
            
            // 设置居中对齐
            Jc tableTitleJc = factory.createJc();
            tableTitleJc.setVal(JcEnumeration.CENTER);
            tableTitlePPr.setJc(tableTitleJc);
            
            tableTitleParagraph.setPPr(tableTitlePPr);
            
            // 创建表格标题文本
            org.docx4j.wml.R tableTitleRun = factory.createR();
            RPr tableTitleRPr = factory.createRPr();
            
            // 设置黑体
            BooleanDefaultTrue tableTitleBold = factory.createBooleanDefaultTrue();
            tableTitleRPr.setB(tableTitleBold);
            
            // 设置五号字体 (10.5pt)
            HpsMeasure tableTitleFontSize = factory.createHpsMeasure();
            tableTitleFontSize.setVal(BigInteger.valueOf(21)); // 10.5pt = 21
            tableTitleRPr.setSz(tableTitleFontSize);
            tableTitleRPr.setSzCs(tableTitleFontSize);
            
            tableTitleRun.setRPr(tableTitleRPr);
            
            Text tableTitleTextElement = factory.createText();
            tableTitleTextElement.setValue("表 4." + partIndex + " " + partName + "检测内容及方法");
            tableTitleRun.getContent().add(tableTitleTextElement);
            tableTitleParagraph.getContent().add(tableTitleRun);
            
            contentObjects.add(tableTitleParagraph);
            // 3. 创建表格
            Tbl table = createPartTable(partData);
            contentObjects.add(table);
            
        } catch (Exception e) {
            log.error("创建分部内容对象失败：partIndex={}, partName={}", partIndex, partName, e);
        }
        
        return contentObjects;
    }



    /**
     * 创建分部表格
     * @param partData 分部数据
     * @return 表格对象
     */
    private Tbl createPartTable(List<CheckRoadDemand> partData) {
        // 计算需要的行数：表头1行 + 数据行
        int totalRows = 1 + partData.size();
        int totalCols = 3; // 分项设施名称、主要检查内容、检查方法或技术要求
        Tbl table = WordDocumentUtil.createTable(totalRows, totalCols, 1200L);
        // 设置表格列宽
        TblGrid tblGrid = table.getTblGrid();
        tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(1800)); // 分项设施名称列
        tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(2000)); // 主要检查内容列
        tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(2400)); // 检查方法或技术要求列
        // 设置表头
        WordDocumentUtil.setCellText(table, 0, 0, "分项设施名称", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 1, "主要检查内容", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 2, "检查方法或技术要求", 10, true, false, 2);
        
        // 填充数据行
        for (int i = 0; i < partData.size(); i++) {
            CheckRoadDemand data = partData.get(i);
            int rowIndex = i + 1; // 数据从第2行开始
            // 分项设施名称
            String itemName = data.getItemName() != null ? data.getItemName() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 0, itemName, 10, false, false, 2);
            // 主要检查内容
            String checkContent = data.getCheckContent() != null ? data.getCheckContent() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 1, checkContent, 10, false, false, 2);
            // 检查方法或技术要求
            String checkMethodDemand = data.getCheckMethodDemand() != null ? data.getCheckMethodDemand() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 2, checkMethodDemand, 10, false, false, 2);
        }
        // 合并相同itemName的行
        mergeRowsByItemName(table, partData);
        return table;
    }

    /**
     * 合并相同itemName的行
     * @param table 表格对象
     * @param partData 分部数据
     */
    private void mergeRowsByItemName(Tbl table, List<CheckRoadDemand> partData) {
        try {
            String currentItemName = null;
            int mergeStartRow = 1; // 数据从第2行开始（索引为1）
            for (int i = 0; i < partData.size(); i++) {
                CheckRoadDemand data = partData.get(i);
                String itemName = data.getItemName() != null ? data.getItemName() : "";
                
                if (!Objects.equals(currentItemName, itemName)) {
                    // 如果前面有需要合并的行，执行合并
                    if (currentItemName != null && i > mergeStartRow) {
                        mergeVerticalCells(table, 0, mergeStartRow, i); // 合并第一列
                    }
                    
                    // 更新当前itemName和合并起始行
                    currentItemName = itemName;
                    mergeStartRow = i + 1; // 数据行索引
                }
            }
            // 处理最后一组数据的合并
            if (currentItemName != null && partData.size() > mergeStartRow) {
                mergeVerticalCells(table, 0, mergeStartRow, partData.size()); // 合并第一列
            }
        } catch (Exception e) {
            log.error("合并相同itemName的行失败", e);
        }
    }

    /**
     * 生成隧道分部评分描述和表格
     * @param wordMLPackage Word文档对象
     * @param tunnelPartScoreList 隧道评分列表
     * @param tunnelCheckList 隧道检测任务列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateTunnelItemPartScoreAndDesc(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> tunnelPartScoreList, List<TunnelCheck> tunnelCheckList
            , String replacePlaceholder) {
        if (tunnelPartScoreList == null || tunnelPartScoreList.isEmpty()) {
            log.warn("隧道评分列表为空，无法生成描述和表格");
            return;
        }
        log.info("开始生成隧道评分描述和表格，共 {} 条隧道数据", tunnelPartScoreList.size());
        // 1. 生成描述性话语并替换${roadDesc}占位符
        String roadDesc = generateTunnelCategoryDescription(tunnelPartScoreList, tunnelCheckList);
        WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, "roadDesc", roadDesc);

        // 2. 按总分降序排列
        List<TunnelInfo> sortedList = tunnelPartScoreList.stream()
            .sorted((t1, t2) -> {
                BigDecimal score1 = t1.getTotalScore() != null ? t1.getTotalScore() : BigDecimal.ZERO;
                BigDecimal score2 = t2.getTotalScore() != null ? t2.getTotalScore() : BigDecimal.ZERO;
                return score2.compareTo(score1); // 降序排列
            })
            .collect(Collectors.toList());

        // 3. 创建评分表格并替换${tunnelPartScoreList}占位符
        generateAndReplaceTunnelScoreTable(wordMLPackage, sortedList, replacePlaceholder);
    }
    
    
    /**
     * 生成隧道类别描述
     * @param tunnelList 隧道列表
     * @param tunnelCheckList 隧道检测任务列表
     * @return 描述文本
     */
    private String generateTunnelCategoryDescription(List<TunnelInfo> tunnelList, List<TunnelCheck> tunnelCheckList) {
        // 计算总隧道数和实际检测隧道数
        int totalTunnels = tunnelList.size();
        int checkedTunnels = 0;
        if (tunnelCheckList != null && !tunnelCheckList.isEmpty()) {
            checkedTunnels = (int) tunnelCheckList.stream()
                .map(TunnelCheck::getTunnelId)
                .distinct()
                .count();
        }
        
        // 生成前置描述
        StringBuilder desc = new StringBuilder();
        String roadName= tunnelList.get(0).getRoadName();
        desc.append(roadName);
        desc.append("共有").append(totalTunnels).append("座隧道，本次检测").append(checkedTunnels).append("座隧道。");
        
        // 统计各类别隧道数量
        Map<String, Map<String, Integer>> categoryStats = new HashMap<>();
        // 初始化每个类别的统计数据
        for (String cat : Arrays.asList("1类", "2类", "3类", "4类")) {
            Map<String, Integer> typeMap = new HashMap<>();
            typeMap.put("特长", 0);
            typeMap.put("长", 0);
            typeMap.put("中", 0);
            typeMap.put("短", 0);
            typeMap.put("total", 0);
            categoryStats.put(cat, typeMap);
        }

        // 统计数据
        for (TunnelInfo tunnel : tunnelList) {
            //类别
            String category = tunnel.getCategory();
            //隧道长度类型
            String tunnelType = tunnel.getTunnelType();
            
            if (category != null && tunnelType != null && categoryStats.containsKey(category)) {
                Map<String, Integer> typeStats = categoryStats.get(category);
                // 安全地增加计数
                typeStats.put(tunnelType, typeStats.getOrDefault(tunnelType, 0) + 1);
                typeStats.put("total", typeStats.getOrDefault("total", 0) + 1);
            }
        }
        
        // 生成评定类别描述文本
        desc.append("其中隧道机电设施技术状况评定为");
        
        boolean first = true;
        for (String category : Arrays.asList("1类", "2类", "3类", "4类")) {
            Map<String, Integer> stats = categoryStats.get(category);
            int totalCount = stats.getOrDefault("total", 0);
            
            if (totalCount > 0) {
                if (!first) {
                    desc.append("、");
                }
                first = false;
                
                desc.append(category).append("类隧道").append(totalCount).append("座");
                desc.append("（");
                
                List<String> typeDescs = new ArrayList<>();
                for (String type : Arrays.asList("特长", "长", "中", "短")) {
                    int count = stats.getOrDefault(type, 0);
                    if (count > 0) {
                        typeDescs.add(type + "隧道" + count + "座");
                    }
                }
                desc.append(String.join("，", typeDescs));
                desc.append("）");
                
                // 计算占比
                double percentage = totalCount * 100.0 / tunnelList.size();
                desc.append("，占比").append(String.format("%.1f", percentage)).append("%");
            }
        }
        
        desc.append("。");
        return desc.toString();
    }
    
    /**
     * 生成隧道评分表格并替换占位符
     * @param wordMLPackage Word文档对象
     * @param sortedList 排序后的隧道列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateAndReplaceTunnelScoreTable(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> sortedList, String replacePlaceholder) {
        if (sortedList == null || sortedList.isEmpty()) {
            log.warn("隧道列表为空，无法生成评分表格");
            return;
        }
        
        log.info("开始生成隧道评分表格，共 {} 条数据", sortedList.size());
        
        // 创建表格：表头2行 + 数据行
        int totalRows = 2 + sortedList.size();
        int totalCols = 10; // 序号、隧道名称、总里程、供配电设施、照明设施、通风设施、消防设施、监控与通信设施、机电设施技术状况得分、类别
        
        Tbl table = WordDocumentUtil.createTable(totalRows, totalCols, 1200L);
        
        // 设置表格列宽
        TblGrid tblGrid = table.getTblGrid();
        tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(300));  // 序号列
        tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(800));  // 隧道名称列
        tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(600));  // 总里程列
        tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(500));  // 供配电设施列
        tblGrid.getGridCol().get(4).setW(BigInteger.valueOf(400));  // 照明设施列
        tblGrid.getGridCol().get(5).setW(BigInteger.valueOf(400));  // 通风设施列
        tblGrid.getGridCol().get(6).setW(BigInteger.valueOf(400));  // 消防设施列
        tblGrid.getGridCol().get(7).setW(BigInteger.valueOf(600));  // 监控与通信设施列
        tblGrid.getGridCol().get(8).setW(BigInteger.valueOf(600));  // 机电设施技术状况得分列
        tblGrid.getGridCol().get(9).setW(BigInteger.valueOf(300));  // 类别列
        
        // 设置第一行表头
        WordDocumentUtil.setCellText(table, 0, 0, "序号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 1, "隧道名称", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 2, "总里程（m）", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 3, "分部设施检测项目得分", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 8, "机电设施技术状况得分", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 9, "类别", 10, true, false, 2);
        
        // 合并第一行的单元格
        // 合并前三列（序号、隧道名称、总里程）
        mergeVerticalCells(table, 0, 0, 1);
        mergeVerticalCells(table, 1, 0, 1);
        mergeVerticalCells(table, 2, 0, 1);
        mergeVerticalCells(table, 8, 0, 1);
        mergeVerticalCells(table, 9, 0, 1);
        // 合并4-7列（分部设施检测项目得分）
        int[][] mergeRanges1 = {{3, 7}};
        WordDocumentUtil.mergeMultipleCellRanges(table, 0, mergeRanges1);



        // 设置第二行表头
        WordDocumentUtil.setCellText(table, 1, 3, "供配电设施", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 4, "照明设施", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 5, "通风设施", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 6, "消防设施", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 7, "监控与通信设施", 10, true, false, 2);
        
        // 填充数据行
        for (int i = 0; i < sortedList.size(); i++) {
            TunnelInfo tunnel = sortedList.get(i);
            int rowIndex = i + 2; // 数据从第3行开始
            
            // 序号（从1开始）
            WordDocumentUtil.setCellText(table, rowIndex, 0, String.valueOf(i + 1), 10, false, false, 2);
            
            // 隧道名称
            String tunnelName = tunnel.getTunnelName() != null ? tunnel.getTunnelName() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 1, tunnelName, 10, false, false, 2);
            
            // 总里程
            String totalLength = tunnel.getTotalTunnelLength() != null ? tunnel.getTotalTunnelLength().toString() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 2, totalLength, 10, false, false, 2);
            
            // 获取各分部得分
            Map<String, String> partScoreMap = getPartScoreMap(tunnel.getPartScores());
            WordDocumentUtil.setCellText(table, rowIndex, 3, partScoreMap.get("供配电设施"), 10, false, false, 2);
            WordDocumentUtil.setCellText(table, rowIndex, 4, partScoreMap.get("照明设施"), 10, false, false, 2);
            WordDocumentUtil.setCellText(table, rowIndex, 5, partScoreMap.get("通风设施"), 10, false, false, 2);
            WordDocumentUtil.setCellText(table, rowIndex, 6, partScoreMap.get("消防设施"), 10, false, false, 2);
            WordDocumentUtil.setCellText(table, rowIndex, 7, partScoreMap.get("监控与通信设施"), 10, false, false, 2);
            
            // 机电设施技术状况得分
            String totalScore = formatScore(tunnel.getTotalScore());
            WordDocumentUtil.setCellText(table, rowIndex, 8, totalScore, 10, false, false, 2);
            
            // 类别
            String category = tunnel.getCategory() != null ? tunnel.getCategory() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 9, category, 10, false, false, 2);
        }
        
        // 替换占位符对应的表格
        WordDocumentUtil.findAndGenerateTable(wordMLPackage, table, replacePlaceholder);
    }
    
    /**
     * 格式化分值为2位小数（使用四舍六入五成双）
     * @param score 原始分值
     * @return 格式化后的分值字符串，无分值时返回 "/"
     */
    private String formatScore(Object score) {
        if (score == null) {
            return "/";
        }
        
        // 检查是否为空字符串或空白字符串
        String scoreStr = score.toString().trim();
        if (scoreStr.isEmpty()) {
            return "/";
        }
        
        try {
            BigDecimal decimal;
            if (score instanceof BigDecimal) {
                decimal = (BigDecimal) score;
            } else if (score instanceof Number) {
                decimal = BigDecimal.valueOf(((Number) score).doubleValue());
            } else {
                decimal = new BigDecimal(scoreStr);
            }
            
            // 使用 ROUND_HALF_EVEN 实现四舍六入五成双
            return decimal.setScale(2, BigDecimal.ROUND_HALF_EVEN).toString();
        } catch (Exception e) {
            log.warn("格式化分值失败: {}, 使用 '/' 代替", score, e);
            return "/";
        }
    }
    
    /**
     * 获取分部得分映射
     * @param partScores 分部得分列表
     * @return 分部名称到得分的映射
     */
    private Map<String, String> getPartScoreMap(List<Map<String, Object>> partScores) {
        Map<String, String> scoreMap = new HashMap<>();
        
        // 初始化默认值
        scoreMap.put("供配电设施", "/");
        scoreMap.put("照明设施", "/");
        scoreMap.put("通风设施", "/");
        scoreMap.put("消防设施", "/");
        scoreMap.put("监控与通信设施", "/");
        
        if (partScores != null) {
            for (Map<String, Object> partScore : partScores) {
                String partName = (String) partScore.get("partName");
                Object scoreObj = partScore.get("score");
                
                if (partName != null && scoreObj != null) {
                    String scoreStr = formatScore(scoreObj);
                    scoreMap.put(partName, scoreStr);
                }
            }
        }
        
        return scoreMap;
    }

    /**
     * 生成路段报告基础信息
     * @param task 隧道信息
     * @param roadTunnelList 路段隧道列表
     * @param partNameList 分部名称列表
     * @return 报告基础信息
     */
    private RoadReportInfo generateRoadReportInfo(Task task, List<TunnelInfo> roadTunnelList, List<String> partNameList,String section) {
        // 获取当前年份
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        String pageTitle = currentYear + "年" + task.getRoadName() + "机电设施定期检查报告";
        String projectName = "湖北交投高速公路检测评定" + section + "标段";
        
        // 计算总长度、特长隧道、长隧道、中隧道、短隧道数量
        BigDecimal totalLength = BigDecimal.ZERO;
        int specialLongCount = 0;
        int longCount = 0;
        int mediumCount = 0;
        int shortCount = 0;
        
        for (TunnelInfo tunnel : roadTunnelList) {
            // 假设TunnelInfo有长度字段，如果没有需要添加或修改此逻辑
            if (tunnel.getTotalTunnelLength() != null) {
                totalLength = totalLength.add(tunnel.getTotalTunnelLength());
                //1.短,2.中,3.长,4.特长
                if (Objects.equals(tunnel.getTunnelType(), "特长")) { // 特长隧道
                    specialLongCount++;
                } else if (Objects.equals(tunnel.getTunnelType(), "长")) { // 长隧道
                    longCount++;
                } else if (Objects.equals(tunnel.getTunnelType(), "中")) { // 中隧道
                    mediumCount++;
                } else { // 短隧道
                    shortCount++;
                }
            }
        }
        
        String roadDescription = "该路段共有隧道" + roadTunnelList.size() + "座，双洞合计" + totalLength + "米，其中特长隧道" + specialLongCount + "座，长隧道" + longCount + "座，中隧道" + mediumCount + "座，短隧道" + shortCount + "座。本次检测隧道" + roadTunnelList.size() + "座，" +
                "均为上、下行分离式双洞隧道，隧道长度见表1.1所示。隧道内设置的隧道机电设施设置有" + StringUtils.join(partNameList, "、") + "。";
        
        return new RoadReportInfo(pageTitle, projectName, roadDescription);
    }

    /**
     * 生成隧道年度评分表格并替换${yearTunnelList}占位符
     * @param wordMLPackage Word文档对象
     * @param tunnelPartScoreList 隧道评分列表
     * @param replacePlaceholder 替换占位符
     * @param roadName 路段名称
     */
    private void generateTunnelYearScore(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> tunnelPartScoreList, String replacePlaceholder, String roadName) {
        if (tunnelPartScoreList == null || tunnelPartScoreList.isEmpty()) {
            log.warn("隧道评分列表为空，无法生成年度评分表格");
            return;
        }

        log.info("开始生成隧道年度评分表格，共 {} 条隧道数据", tunnelPartScoreList.size());

        // 1. 按总分降序排序
        List<TunnelInfo> sortedList = tunnelPartScoreList.stream()
            .sorted((t1, t2) -> {
                BigDecimal score1 = t1.getTotalScore() != null ? t1.getTotalScore() : BigDecimal.ZERO;
                BigDecimal score2 = t2.getTotalScore() != null ? t2.getTotalScore() : BigDecimal.ZERO;
                return score2.compareTo(score1); // 降序排列
            })
            .collect(Collectors.toList());

        // 2. 创建表标题和表格
        String tableTitle = "表5.2 " + roadName + "三年评级对比表（23、24、25）";
        
        // 创建表标题段落
        ObjectFactory factory = new ObjectFactory();
        org.docx4j.wml.P titleParagraph = factory.createP();
        
        // 设置段落属性 - 居中对齐
        PPr titlePPr = factory.createPPr();
        Jc titleJc = factory.createJc();
        titleJc.setVal(JcEnumeration.CENTER);
        titlePPr.setJc(titleJc);
        titleParagraph.setPPr(titlePPr);
        
        // 创建标题文本
        org.docx4j.wml.R titleRun = factory.createR();
        RPr titleRPr = factory.createRPr();
        
        // 设置黑体
        BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
        titleRPr.setB(bold);
        
        // 设置五号字体 (10.5pt)
        HpsMeasure fontSize = factory.createHpsMeasure();
        fontSize.setVal(BigInteger.valueOf(21)); // 10.5pt = 21
        titleRPr.setSz(fontSize);
        titleRPr.setSzCs(fontSize);
        
        titleRun.setRPr(titleRPr);
        
        Text titleTextElement = factory.createText();
        titleTextElement.setValue(tableTitle);
        titleRun.getContent().add(titleTextElement);
        titleParagraph.getContent().add(titleRun);
        
        // 创建表格：表头2行 + 数据行
        int totalRows = 2 + sortedList.size();
        int totalCols = 9; // 序号、隧道名称、隧道类别、2023分数、2023评级、2024分数、2024评级、2025分数、2025评级

        Tbl table = WordDocumentUtil.createTable(totalRows, totalCols, 1200L);

        // 设置表格列宽
        TblGrid tblGrid = table.getTblGrid();
        tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(600));  // 序号列
        tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(1200)); // 隧道名称列
        tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(800));  // 隧道类别列
        tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(600));  // 2023分数列
        tblGrid.getGridCol().get(4).setW(BigInteger.valueOf(600));  // 2023评级列
        tblGrid.getGridCol().get(5).setW(BigInteger.valueOf(600));  // 2024分数列
        tblGrid.getGridCol().get(6).setW(BigInteger.valueOf(600));  // 2024评级列
        tblGrid.getGridCol().get(7).setW(BigInteger.valueOf(600));  // 2025分数列
        tblGrid.getGridCol().get(8).setW(BigInteger.valueOf(600));  // 2025评级列

        // 设置第一行表头
        WordDocumentUtil.setCellText(table, 0, 0, "序号", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 1, "隧道名称", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 2, "隧道类别", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 3, "2023年评级", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 5, "2024年评级", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 0, 7, "2025年评级", 10, true, false, 2);

        // 垂直合并前三列（序号、隧道名称、隧道类别）
        mergeVerticalCells(table, 0, 0, 1); // 序号列
        mergeVerticalCells(table, 1, 0, 1); // 隧道名称列
        mergeVerticalCells(table, 2, 0, 1); // 隧道类别列

        // 合并第一行的年份列
        int[][] mergeRanges = {{3, 4}, {5, 6}, {7, 8}}; // 2023年评级、2024年评级、2025年评级
        WordDocumentUtil.mergeMultipleCellRanges(table, 0, mergeRanges);

        // 设置第二行表头
        WordDocumentUtil.setCellText(table, 1, 3, "分数", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 4, "评级", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 5, "分数", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 6, "评级", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 7, "分数", 10, true, false, 2);
        WordDocumentUtil.setCellText(table, 1, 8, "评级", 10, true, false, 2);

        // 填充数据行
        for (int i = 0; i < sortedList.size(); i++) {
            TunnelInfo tunnel = sortedList.get(i);
            int rowIndex = i + 2; // 数据从第3行开始

            // 序号（从1开始）
            WordDocumentUtil.setCellText(table, rowIndex, 0, String.valueOf(i + 1), 10, false, false, 2);

            // 隧道名称
            String tunnelName = tunnel.getTunnelName() != null ? tunnel.getTunnelName() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 1, tunnelName, 10, false, false, 2);

            // 隧道类别
            String tunnelType = tunnel.getTunnelType() != null ? tunnel.getTunnelType() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 2, tunnelType, 10, false, false, 2);

            // 2023年分数和评级（空白）
            WordDocumentUtil.setCellText(table, rowIndex, 3, "", 10, false, false, 2);
            WordDocumentUtil.setCellText(table, rowIndex, 4, "", 10, false, false, 2);

            // 2024年分数和评级（空白）
            WordDocumentUtil.setCellText(table, rowIndex, 5, "", 10, false, false, 2);
            WordDocumentUtil.setCellText(table, rowIndex, 6, "", 10, false, false, 2);

            // 2025年分数和评级（实际数据）
            String totalScore = formatScore(tunnel.getTotalScore());
            WordDocumentUtil.setCellText(table, rowIndex, 7, totalScore, 10, false, false, 2);

            String category = tunnel.getCategory() != null ? tunnel.getCategory() : "";
            WordDocumentUtil.setCellText(table, rowIndex, 8, category, 10, false, false, 2);
        }

        // 使用自定义方法替换占位符为标题和表格
        findAndReplaceWithTitleAndTable(wordMLPackage, titleParagraph, table, replacePlaceholder);
        
        log.info("隧道年度评分表格生成完成，共生成 {} 行数据", sortedList.size());
    }

    /**
     * 生成分部平均得分表格并替换${partAvgScoreList}占位符
     * @param wordMLPackage Word文档对象
     * @param tunnelPartScoreList 隧道评分列表
     * @param tunnelCheckList 隧道检测任务列表
     * @param replacePlaceholder 替换占位符
     */
    private void generatePartAvgScoreListScore(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> tunnelPartScoreList, List<TunnelCheck> tunnelCheckList, String replacePlaceholder) {
        if (tunnelPartScoreList == null || tunnelPartScoreList.isEmpty()) {
            log.warn("隧道评分列表为空，无法生成分部平均得分表格");
            return;
        }

        log.info("开始生成分部平均得分表格，共 {} 条隧道数据", tunnelPartScoreList.size());

        try {
            // 1. 统计各分部的平均得分和检测隧道数量
            List<PartAvgScoreInfo> partAvgScoreList = calculatePartAvgScores(tunnelPartScoreList, tunnelCheckList);
            
            if (partAvgScoreList.isEmpty()) {
                log.warn("没有有效的分部得分数据");
                return;
            }

            // 2. 创建表标题
            String tableTitle = "表5.3分部设施平均得分表";
            
            // 创建表标题段落
            ObjectFactory factory = new ObjectFactory();
            org.docx4j.wml.P titleParagraph = factory.createP();
            
            // 设置段落属性 - 居中对齐
            PPr titlePPr = factory.createPPr();
            Jc titleJc = factory.createJc();
            titleJc.setVal(JcEnumeration.CENTER);
            titlePPr.setJc(titleJc);
            titleParagraph.setPPr(titlePPr);
            
            // 创建标题文本
            org.docx4j.wml.R titleRun = factory.createR();
            RPr titleRPr = factory.createRPr();
            
            // 设置黑体
            BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
            titleRPr.setB(bold);
            
            // 设置五号字体 (10.5pt)
            HpsMeasure fontSize = factory.createHpsMeasure();
            fontSize.setVal(BigInteger.valueOf(21)); // 10.5pt = 21
            titleRPr.setSz(fontSize);
            titleRPr.setSzCs(fontSize);
            
            titleRun.setRPr(titleRPr);
            
            Text titleTextElement = factory.createText();
            titleTextElement.setValue(tableTitle);
            titleRun.getContent().add(titleTextElement);
            titleParagraph.getContent().add(titleRun);

            // 3. 创建表格：表头1行 + 数据行
            int totalRows = 1 + partAvgScoreList.size();
            int totalCols = 4; // 序号、分部工程名称、参与检测隧道数量、平均得分

            Tbl table = WordDocumentUtil.createTable(totalRows, totalCols, 1200L);

            // 设置表格列宽
            TblGrid tblGrid = table.getTblGrid();
            tblGrid.getGridCol().get(0).setW(BigInteger.valueOf(800));  // 序号列
            tblGrid.getGridCol().get(1).setW(BigInteger.valueOf(1400)); // 分部工程名称列
            tblGrid.getGridCol().get(2).setW(BigInteger.valueOf(1200)); // 参与检测隧道数量列
            tblGrid.getGridCol().get(3).setW(BigInteger.valueOf(1000)); // 平均得分列

            // 设置表头
            WordDocumentUtil.setCellText(table, 0, 0, "序号", 10, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 1, "分部工程名称", 10, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 2, "参与检测隧道数量", 10, true, false, 2);
            WordDocumentUtil.setCellText(table, 0, 3, "平均得分", 10, true, false, 2);

            // 填充数据行
            for (int i = 0; i < partAvgScoreList.size(); i++) {
                PartAvgScoreInfo partInfo = partAvgScoreList.get(i);
                int rowIndex = i + 1; // 数据从第2行开始

                // 序号（从1开始）
                WordDocumentUtil.setCellText(table, rowIndex, 0, String.valueOf(i + 1), 10, false, false, 2);

                // 分部工程名称
                WordDocumentUtil.setCellText(table, rowIndex, 1, partInfo.getPartName(), 10, false, false, 2);

                // 参与检测隧道数量
                WordDocumentUtil.setCellText(table, rowIndex, 2, String.valueOf(partInfo.getTunnelCount()), 10, false, false, 2);

                // 平均得分
                String avgScore = formatScore(partInfo.getAvgScore());
                WordDocumentUtil.setCellText(table, rowIndex, 3, avgScore, 10, false, false, 2);
            }

            // 使用自定义方法替换占位符为标题和表格
            findAndReplaceWithTitleAndTable(wordMLPackage, titleParagraph, table, replacePlaceholder);
            
            log.info("分部平均得分表格生成完成，共生成 {} 行数据", partAvgScoreList.size());

        } catch (Exception e) {
            log.error("生成分部平均得分表格失败", e);
        }
    }

    /**
     * 计算各分部的平均得分和检测隧道数量
     * @param tunnelPartScoreList 隧道评分列表
     * @param tunnelCheckList 隧道检测任务列表
     * @return 分部平均得分信息列表
     */
    private List<PartAvgScoreInfo> calculatePartAvgScores(List<TunnelInfo> tunnelPartScoreList, List<TunnelCheck> tunnelCheckList) {
        List<PartAvgScoreInfo> result = new ArrayList<>();
        
        try {
            // 统计各分部的得分和隧道数量
            Map<String, List<BigDecimal>> partScoreMap = new HashMap<>();
            Map<String, Set<Long>> partTunnelCountMap = new HashMap<>();
            
            // 遍历隧道评分列表，统计各分部得分
            for (TunnelInfo tunnel : tunnelPartScoreList) {
                if (tunnel.getPartScores() != null) {
                    for (Map<String, Object> partScore : tunnel.getPartScores()) {
                        String partName = (String) partScore.get("partName");
                        Object scoreObj = partScore.get("score");
                        
                        if (partName != null && scoreObj != null) {
                            BigDecimal score = null;
                            if (scoreObj instanceof BigDecimal) {
                                score = (BigDecimal) scoreObj;
                            } else if (scoreObj instanceof Number) {
                                score = BigDecimal.valueOf(((Number) scoreObj).doubleValue());
                            } else {
                                try {
                                    score = new BigDecimal(scoreObj.toString());
                                } catch (Exception e) {
                                    log.warn("无法解析分部得分: {}", scoreObj);
                                    continue;
                                }
                            }
                            
                            // 统计得分
                            partScoreMap.computeIfAbsent(partName, k -> new ArrayList<>()).add(score);
                            
                            // 统计隧道数量（使用Set去重）
                            partTunnelCountMap.computeIfAbsent(partName, k -> new HashSet<>()).add(tunnel.getId());
                        }
                    }
                }
            }
            
            // 计算平均得分
            for (Map.Entry<String, List<BigDecimal>> entry : partScoreMap.entrySet()) {
                String partName = entry.getKey();
                List<BigDecimal> scores = entry.getValue();
                
                if (!scores.isEmpty()) {
                    // 计算平均分
                    BigDecimal sum = scores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal avgScore = sum.divide(BigDecimal.valueOf(scores.size()), 2, BigDecimal.ROUND_HALF_EVEN);
                    
                    // 获取隧道数量
                    int tunnelCount = partTunnelCountMap.getOrDefault(partName, new HashSet<>()).size();
                    
                    PartAvgScoreInfo partInfo = new PartAvgScoreInfo();
                    partInfo.setPartName(partName);
                    partInfo.setAvgScore(avgScore);
                    partInfo.setTunnelCount(tunnelCount);
                    
                    result.add(partInfo);
                }
            }
            
            // 按分部名称排序（可选）
            result.sort((a, b) -> a.getPartName().compareTo(b.getPartName()));
            
            log.info("计算分部平均得分完成，共 {} 个分部", result.size());
            
        } catch (Exception e) {
            log.error("计算分部平均得分失败", e);
        }
        
        return result;
    }

    /**
     * 分部平均得分信息类
     */
        private static class PartAvgScoreInfo {
        private String partName;      // 分部名称
        private BigDecimal avgScore;  // 平均得分
        private int tunnelCount;      // 隧道数量

        public String getPartName() {
            return partName;
        }

        public void setPartName(String partName) {
            this.partName = partName;
        }

        public BigDecimal getAvgScore() {
            return avgScore;
        }

        public void setAvgScore(BigDecimal avgScore) {
            this.avgScore = avgScore;
        }

        public int getTunnelCount() {
            return tunnelCount;
        }

        public void setTunnelCount(int tunnelCount) {
            this.tunnelCount = tunnelCount;
        }
    }

    /**
     * 缺陷统计信息数据模型
     */
    private static class QuestionScoreInfo {
        private String partName;        // 分部名称
        private String itemName;        // 分项名称
        private String unit;           // 单位
        private String questionDesc;   // 缺陷描述
        private String tunnelNames;    // 隧道名称（多个用顿号拼接）
        private int defectCount;       // 缺陷数量
        private int totalCount;        // 设备总数量
        private BigDecimal ratio;      // 占比

        public String getPartName() {
            return partName;
        }

        public void setPartName(String partName) {
            this.partName = partName;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getQuestionDesc() {
            return questionDesc;
        }

        public void setQuestionDesc(String questionDesc) {
            this.questionDesc = questionDesc;
        }

        public String getTunnelNames() {
            return tunnelNames;
        }

        public void setTunnelNames(String tunnelNames) {
            this.tunnelNames = tunnelNames;
        }

        public int getDefectCount() {
            return defectCount;
        }

        public void setDefectCount(int defectCount) {
            this.defectCount = defectCount;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public BigDecimal getRatio() {
            return ratio;
        }

        public void setRatio(BigDecimal ratio) {
            this.ratio = ratio;
        }
    }

    /**
     * 生成缺陷统计表格并替换占位符
     * @param wordMLPackage Word文档对象
     * @param tunnelCheckList 隧道检测数据列表
     * @param replacePlaceholder 替换占位符
     * @param roadName 路段名称
     */
    private void generateQuestionScoreList(WordprocessingMLPackage wordMLPackage, List<TunnelCheck> tunnelCheckList, String replacePlaceholder, String roadName) {
        if (tunnelCheckList == null || tunnelCheckList.isEmpty()) {
            log.warn("隧道检测数据列表为空，无法生成缺陷统计表格");
            return;
        }

        log.info("开始生成缺陷统计表格，数据量: {}", tunnelCheckList.size());

        // 1. 按 partCode+questionDesc+unit 分组统计
        List<QuestionScoreInfo> questionScoreList = calculateQuestionScores(tunnelCheckList, roadName);

        // 2. 按缺陷占比倒排序，展示所有数据
        List<QuestionScoreInfo> sortedList = questionScoreList.stream()
            .sorted((a, b) -> b.getRatio().compareTo(a.getRatio()))
            .collect(Collectors.toList());

        log.info("缺陷统计完成，总分组数: {}, 排序后数据: {}", questionScoreList.size(), sortedList.size());

        // 3. 创建表格
        if (!sortedList.isEmpty()) {
            generateAndReplaceQuestionScoreTable(wordMLPackage, sortedList, replacePlaceholder);
        } else {
            log.warn("没有有效的缺陷统计数据");
            WordDocumentUtil.replaceTextPlaceholderV2(wordMLPackage, replacePlaceholder, "暂无缺陷统计数据");
        }
    }

    /**
     * 计算缺陷统计数据
     * @param tunnelCheckList 隧道检测数据列表
     * @param roadName 路段名称
     * @return 缺陷统计信息列表
     */
    private List<QuestionScoreInfo> calculateQuestionScores(List<TunnelCheck> tunnelCheckList, String roadName) {
        // 按 partCode+itemCode+questionDesc+unit 分组
        Map<String, List<TunnelCheck>> groupedData = tunnelCheckList.stream()
            .filter(check -> StringUtils.isNotBlank(check.getPartCode()) &&
                           StringUtils.isNotBlank(check.getItemCode()) &&
                           StringUtils.isNotBlank(check.getQuestionDesc()) &&
                           StringUtils.isNotBlank(check.getUnit()))
            .collect(Collectors.groupingBy(check ->
                check.getPartCode() + "|" + check.getItemCode() + "|" + check.getQuestionDesc() + "|" + check.getUnit()));

        log.info("分组完成，共 {} 个分组", groupedData.size());

        // 批量查询路段下各分部分项的设备数量，避免在循环中多次查询
        List<FacilityPartCount> facilityPartCounts = facilityInfoMapper.countFacilityByRoadNameGroupByPart(roadName);
        Map<String, Integer> partItemCountMap = facilityPartCounts.stream()
            .collect(Collectors.toMap(
                count -> count.getPartName() + "|" + count.getItemName(), 
                FacilityPartCount::getCount
            ));

        log.info("批量查询分部分项设备数量完成，共 {} 个分部分项组合", partItemCountMap.size());

        // 计算总缺陷数量用于占比计算
        int totalDefectCount = tunnelCheckList.size();

        List<QuestionScoreInfo> resultList = new ArrayList<>();
        
        for (Map.Entry<String, List<TunnelCheck>> entry : groupedData.entrySet()) {
            String[] keyParts = entry.getKey().split("\\|");
            if (keyParts.length != 4) {
                continue;
            }
            
            // keyParts[0] 是 partCode，keyParts[1] 是 itemCode，用于分组但不直接使用
            String questionDesc = keyParts[2];
            String unit = keyParts[3];
            List<TunnelCheck> checkList = entry.getValue();
            
            QuestionScoreInfo info = new QuestionScoreInfo();
            // 直接使用第一条记录的partName和itemName，因为同一分组的应该是相同的
            info.setPartName(checkList.get(0).getPartName());
            info.setItemName(checkList.get(0).getItemName());
            info.setUnit(unit);
            info.setQuestionDesc(questionDesc);
            
            // 统计隧道名称（去重并用顿号拼接）
            Set<String> tunnelNameSet = checkList.stream()
                .map(TunnelCheck::getTunnelName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
            info.setTunnelNames(String.join("、", tunnelNameSet));
            
            // 缺陷数量 = 该分组的记录数
            info.setDefectCount(checkList.size());
            
            // 设备总数量 = 从预先查询的映射中获取，使用partName+itemName组合键
            String partName = checkList.get(0).getPartName();
            String itemName = checkList.get(0).getItemName();
            String partItemKey = partName + "|" + itemName;
            int totalFacilityCount = partItemCountMap.getOrDefault(partItemKey, 0);
            info.setTotalCount(totalFacilityCount);
            
            // 计算占比
            BigDecimal ratio = totalDefectCount > 0 ? 
                new BigDecimal(checkList.size()).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(totalDefectCount), 2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            info.setRatio(ratio);
            
            resultList.add(info);
            
            log.debug("分组统计 - 分部: {}, 单位: {}, 缺陷描述: {}, 缺陷数量: {}, 隧道数: {}, 占比: {}%", 
                info.getPartName(), unit, questionDesc, info.getDefectCount(), 
                info.getTotalCount(), info.getRatio());
        }
        
        return resultList;
    }



    /**
     * 生成缺陷统计表格并替换占位符
     * @param wordMLPackage Word文档对象
     * @param questionScoreList 缺陷统计数据列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateAndReplaceQuestionScoreTable(WordprocessingMLPackage wordMLPackage, List<QuestionScoreInfo> questionScoreList, String replacePlaceholder) {
        try {
            // 创建表格：9列（序号、分部设施名称、分项设施名称、单位、缺陷描述、缺陷分布、缺陷数量、设备总数量、占比）
            int rows = questionScoreList.size() + 1; // +1 为表头
            int cols = 9;
            
            // 创建表格并设置优化的列宽
            Tbl table = createOptimizedQuestionScoreTable(rows, cols);
            
            // 创建表头 - 使用宋体五号字体（10.5磅）
            String[] headers = {"序号", "分部设施名称", "分项设施名称", "单位", "缺陷描述", "缺陷分布", "缺陷数量", "设备总数量", "占比（%）"};
            for (int i = 0; i < headers.length; i++) {
                setCellTextWithFont(table, 0, i, headers[i], 10.5f, true, true, 2); // 表头居中，宋体五号
            }
            
            // 填充数据行 - 使用宋体五号字体（10.5磅）
            for (int i = 0; i < questionScoreList.size(); i++) {
                QuestionScoreInfo info = questionScoreList.get(i);
                int rowIndex = i + 1;
                
                setCellTextWithFont(table, rowIndex, 0, String.valueOf(i + 1), 10.5f, false, false, 2); // 序号
                setCellTextWithFont(table, rowIndex, 1, info.getPartName(), 10.5f, false, false, 1); // 分部设施名称
                setCellTextWithFont(table, rowIndex, 2, info.getItemName(), 10.5f, false, false, 1); // 分项设施名称
                setCellTextWithFont(table, rowIndex, 3, info.getUnit(), 10.5f, false, false, 2); // 单位
                setCellTextWithFont(table, rowIndex, 4, info.getQuestionDesc(), 10.5f, false, false, 1); // 缺陷描述
                setCellTextWithFont(table, rowIndex, 5, info.getTunnelNames(), 10.5f, false, false, 1); // 缺陷分布（隧道名称）
                setCellTextWithFont(table, rowIndex, 6, String.valueOf(info.getDefectCount()), 10.5f, false, false, 2); // 缺陷数量
                setCellTextWithFont(table, rowIndex, 7, String.valueOf(info.getTotalCount()), 10.5f, false, false, 2); // 设备总数量
                setCellTextWithFont(table, rowIndex, 8, info.getRatio().toString(), 10.5f, false, false, 2); // 占比
            }
            
            log.info("缺陷统计表格创建完成，行数: {}, 列数: {}", rows, cols);
            
            // 在占位符位置替换为表格
            WordDocumentUtil.findAndGenerateTable(wordMLPackage, table, replacePlaceholder);
            
        } catch (Exception e) {
            log.error("生成缺陷统计表格时发生异常", e);
            throw new ServiceException("生成缺陷统计表格失败: " + e.getMessage());
        }
    }

    /**
     * 创建优化列宽的缺陷统计表格
     * @param rows 行数
     * @param cols 列数
     * @return 表格对象
     */
    private Tbl createOptimizedQuestionScoreTable(int rows, int cols) {
        ObjectFactory factory = new ObjectFactory();
        
        Tbl table = factory.createTbl();
        
        // 设置表格基本属性
        TblPr tblPr = factory.createTblPr();
        
        // 设置表格宽度为100%
        TblWidth tblWidth = factory.createTblWidth();
        tblWidth.setType("pct");
        tblWidth.setW(BigInteger.valueOf(5000)); // 5000表示100%
        tblPr.setTblW(tblWidth);
        
        // 设置表格边框
        TblBorders borders = factory.createTblBorders();
        CTBorder solidBorder = factory.createCTBorder();
        solidBorder.setVal(STBorder.SINGLE);
        solidBorder.setColor("000000");
        
        borders.setTop(solidBorder);
        borders.setBottom(solidBorder);
        borders.setLeft(solidBorder);
        borders.setRight(solidBorder);
        borders.setInsideH(solidBorder);
        borders.setInsideV(solidBorder);
        
        tblPr.setTblBorders(borders);
        
        // 设置表格布局为固定布局
        CTTblLayoutType layout = factory.createCTTblLayoutType();
        layout.setType(STTblLayoutType.FIXED);
        tblPr.setTblLayout(layout);
        
        table.setTblPr(tblPr);
        
        // 创建表格网格（优化的列宽）
        TblGrid tblGrid = factory.createTblGrid();
        // 定义各列的宽度：序号、分部设施名称、分项设施名称、单位、缺陷描述、缺陷分布、缺陷数量、设备总数量、占比
        long[] columnWidths = {600, 1400, 1400, 600, 1800, 1800, 1000, 1000, 900}; // 单位：1/20磅
        
        for (int i = 0; i < cols; i++) {
            TblGridCol gridCol = factory.createTblGridCol();
            gridCol.setW(BigInteger.valueOf(columnWidths[i]));
            tblGrid.getGridCol().add(gridCol);
        }
        table.setTblGrid(tblGrid);
        
        // 创建行和单元格
        for (int i = 0; i < rows; i++) {
            Tr row = factory.createTr();
            for (int j = 0; j < cols; j++) {
                Tc cell = factory.createTc();
                
                // 设置单元格属性
                TcPr tcPr = factory.createTcPr();
                
                // 设置单元格宽度
                TblWidth cellWidth = factory.createTblWidth();
                cellWidth.setType("dxa");
                cellWidth.setW(BigInteger.valueOf(columnWidths[j]));
                tcPr.setTcW(cellWidth);
                
                // 垂直居中
                CTVerticalJc verticalJc = factory.createCTVerticalJc();
                verticalJc.setVal(STVerticalJc.CENTER);
                tcPr.setVAlign(verticalJc);
                
                cell.setTcPr(tcPr);
                
                // 添加一个空段落
                P p = factory.createP();
                cell.getContent().add(p);
                
                row.getContent().add(cell);
            }
            table.getContent().add(row);
        }
        
        return table;
    }

    /**
     * 设置单元格文本（指定宋体字体）
     * @param table 表格
     * @param rowIndex 行索引
     * @param colIndex 列索引
     * @param text 文本内容
     * @param fontSize 字体大小（磅）
     * @param isBold 是否加粗
     * @param isHeader 是否为表头
     * @param alignment 对齐方式
     */
    private void setCellTextWithFont(Tbl table, int rowIndex, int colIndex, String text, float fontSize, boolean isBold, boolean isHeader, int alignment) {
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 获取行
            List<Object> rows = table.getContent();
            if (rowIndex >= rows.size()) {
                throw new IndexOutOfBoundsException("行索引超出范围");
            }
            
            Tr row = (Tr) rows.get(rowIndex);
            
            // 获取单元格
            List<Object> cells = row.getContent();
            if (colIndex >= cells.size()) {
                throw new IndexOutOfBoundsException("列索引超出范围");
            }
            
            Tc cell = (Tc) cells.get(colIndex);
            
            // 清空单元格内容
            cell.getContent().clear();
            
            // 创建段落
            P p = factory.createP();
            
            // 设置段落对齐方式
            PPr ppr = factory.createPPr();
            Jc jc = factory.createJc();
            
            if (alignment == 1) {
                jc.setVal(JcEnumeration.LEFT);
            } else if (alignment == 2) {
                jc.setVal(JcEnumeration.CENTER);
            } else if (alignment == 3) {
                jc.setVal(JcEnumeration.RIGHT);
            } else {
                jc.setVal(JcEnumeration.LEFT);
            }
            
            ppr.setJc(jc);
            p.setPPr(ppr);
            
            // 创建文本运行
            R r = factory.createR();
            Text t = factory.createText();
            t.setValue(text);
            t.setSpace("preserve");
            r.getContent().add(t);
            
            // 设置字体样式
            RPr rpr = factory.createRPr();
            
            // 设置字体为宋体
            RFonts fonts = factory.createRFonts();
            fonts.setAscii("宋体");
            fonts.setHAnsi("宋体");
            fonts.setCs("宋体");
            fonts.setEastAsia("宋体");
            rpr.setRFonts(fonts);
            
            // 设置字体大小
            HpsMeasure sz = factory.createHpsMeasure();
            sz.setVal(BigInteger.valueOf((long)(fontSize * 2))); // Word中字号是磅值的两倍
            rpr.setSz(sz);
            rpr.setSzCs(sz);
            
            // 设置字体加粗
            if (isBold) {
                BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
                b.setVal(true);
                rpr.setB(b);
                rpr.setBCs(b);
            }
            
            r.setRPr(rpr);
            p.getContent().add(r);
            
            // 如果是表头，设置灰色背景
            if (isHeader) {
                TcPr tcPr = cell.getTcPr();
                if (tcPr == null) {
                    tcPr = factory.createTcPr();
                    cell.setTcPr(tcPr);
                }
                
                CTShd shd = factory.createCTShd();
                shd.setVal(STShd.CLEAR);
                shd.setColor("auto");
                shd.setFill("DDDDDD"); // 灰色背景
                tcPr.setShd(shd);
            }
            
            cell.getContent().add(p);
            
        } catch (Exception e) {
            log.error("设置单元格文本失败", e);
            throw new RuntimeException("设置单元格文本失败", e);
        }
    }

    /**
     * 按分部生成缺陷统计表格并替换占位符
     * @param wordMLPackage Word文档对象
     * @param tunnelCheckList 隧道检测数据列表
     * @param replacePlaceholder 替换占位符
     */
    private void generateQuestionScoreListByPart(WordprocessingMLPackage wordMLPackage, List<TunnelCheck> tunnelCheckList, String replacePlaceholder) {
        if (tunnelCheckList == null || tunnelCheckList.isEmpty()) {
            log.warn("隧道检测数据列表为空，无法生成分部缺陷统计表格");
            return;
        }

        log.info("开始生成分部缺陷统计表格，数据量: {}", tunnelCheckList.size());

        // 1. 查询sc_check_enum_relation表中fcous=1的数据，根据part_code+item_code+check_content匹配
        List<CheckEnumRelation> focusedRelations = checkEnumRelationMapper.selectFocusedByPartItemContent(tunnelCheckList);
        log.info("查询到关注的检测项目数量: {}", focusedRelations.size());

        // 2. 创建关注项目的映射，用于快速查找
        Set<String> focusedKeys = focusedRelations.stream()
            .map(relation -> relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent())
            .collect(Collectors.toSet());

        // 3. 过滤tunnelCheckList，只保留在关注列表中的数据
        List<TunnelCheck> filteredTunnelCheckList = tunnelCheckList.stream()
            .filter(check -> StringUtils.isNotBlank(check.getPartCode()) &&
                           StringUtils.isNotBlank(check.getItemCode()) &&
                           StringUtils.isNotBlank(check.getCheckContent()) &&
                           StringUtils.isNotBlank(check.getQuestionDesc()))
            .filter(check -> {
                String key = check.getPartCode() + "_" + check.getItemCode() + "_" + check.getCheckContent();
                return focusedKeys.contains(key);
            })
            .collect(Collectors.toList());

        log.info("过滤后的数据量: {}", filteredTunnelCheckList.size());

        if (filteredTunnelCheckList.isEmpty()) {
            log.warn("过滤后无数据，无法生成分部缺陷统计表格");
            return;
        }

        // 4. 按 partCode 分组
        Map<String, List<TunnelCheck>> partGroupedData = filteredTunnelCheckList.stream()
            .collect(Collectors.groupingBy(TunnelCheck::getPartCode));

        log.info("按分部分组完成，共 {} 个分部", partGroupedData.size());

        // 2. 创建所有分部的内容
        List<Object> allPartContents = new ArrayList<>();
        int partIndex = 1;

        for (Map.Entry<String, List<TunnelCheck>> partEntry : partGroupedData.entrySet()) {
            String partCode = partEntry.getKey();
            List<TunnelCheck> partCheckList = partEntry.getValue();
            
            // 获取分部名称
            String partName = partCheckList.get(0).getPartName();
            
            log.info("处理分部: {} ({}), 数据量: {}", partName, partCode, partCheckList.size());

            // 创建分部标题（二级标题）
            P partTitle = createPartTitle(partIndex, partName);
            allPartContents.add(partTitle);

            // 按 questionDesc 分组
            Map<String, List<TunnelCheck>> questionGroupedData = partCheckList.stream()
                .collect(Collectors.groupingBy(TunnelCheck::getQuestionDesc));

            // 创建表格标题段落
            P tableTitle = createTableTitleParagraph(partIndex, partName);
            allPartContents.add(tableTitle);

            // 创建表格
            Tbl partTable = createPartQuestionTable(questionGroupedData, partIndex, partName, focusedRelations);
            allPartContents.add(partTable);

            partIndex++;
        }

        // 3. 替换占位符
        replaceWithMultipleContents(wordMLPackage, replacePlaceholder, allPartContents);
    }

    /**
     * 创建分部标题（二级标题，大纲视图）
     * @param partIndex 分部序号
     * @param partName 分部名称
     * @return 标题段落
     */
    private P createPartTitle(int partIndex, String partName) {
        ObjectFactory factory = new ObjectFactory();
        
        P titleP = factory.createP();
        
        // 设置段落属性
        PPr ppr = factory.createPPr();
        
        // 设置大纲级别（二级标题）
        PPrBase.OutlineLvl outlineLvl = factory.createPPrBaseOutlineLvl();
        outlineLvl.setVal(BigInteger.valueOf(1)); // 1表示二级标题
        ppr.setOutlineLvl(outlineLvl);
        
        // 设置居左对齐
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.LEFT);
        ppr.setJc(jc);
        
        titleP.setPPr(ppr);
        
        // 创建文本运行
        R r = factory.createR();
        Text t = factory.createText();
        t.setValue("7." + partIndex + " " + partName + "主要问题、成因分析、修复及养护建议");
        t.setSpace("preserve");
        r.getContent().add(t);
        
        // 设置字体样式（黑体四号）
        RPr rpr = factory.createRPr();
        
        // 设置字体为黑体
        RFonts fonts = factory.createRFonts();
        fonts.setAscii("黑体");
        fonts.setHAnsi("黑体");
        fonts.setCs("黑体");
        fonts.setEastAsia("黑体");
        rpr.setRFonts(fonts);
        
        // 设置字体大小（四号 = 14磅）
        HpsMeasure sz = factory.createHpsMeasure();
        sz.setVal(BigInteger.valueOf(28)); // 14 * 2
        rpr.setSz(sz);
        rpr.setSzCs(sz);
        
        // 设置加粗
        BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
        b.setVal(true);
        rpr.setB(b);
        rpr.setBCs(b);
        
        r.setRPr(rpr);
        titleP.getContent().add(r);
        
        return titleP;
    }

    /**
     * 创建表格标题段落（位于表格上方）
     * @param partIndex 分部序号
     * @param partName 分部名称
     * @return 表格标题段落
     */
    private P createTableTitleParagraph(int partIndex, String partName) {
        ObjectFactory factory = new ObjectFactory();
        
        P titleP = factory.createP();
        
        // 设置段落属性（居中对齐）
        PPr ppr = factory.createPPr();
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.CENTER);
        ppr.setJc(jc);
        titleP.setPPr(ppr);
        
        // 创建文本运行
        R r = factory.createR();
        Text t = factory.createText();
        t.setValue("表 7." + partIndex + " " + partName + "主要问题、成因分析、修复及养护建议");
        t.setSpace("preserve");
        r.getContent().add(t);
        
        // 设置字体样式（黑体五号加粗）
        RPr rpr = factory.createRPr();
        
        // 设置字体为黑体
        RFonts fonts = factory.createRFonts();
        fonts.setAscii("黑体");
        fonts.setHAnsi("黑体");
        fonts.setCs("黑体");
        fonts.setEastAsia("黑体");
        rpr.setRFonts(fonts);
        
        // 设置字体大小（五号 = 10.5磅）
        HpsMeasure sz = factory.createHpsMeasure();
        sz.setVal(BigInteger.valueOf(21)); // 10.5 * 2
        rpr.setSz(sz);
        rpr.setSzCs(sz);
        
        // 设置加粗
        BooleanDefaultTrue b = factory.createBooleanDefaultTrue();
        b.setVal(true);
        rpr.setB(b);
        rpr.setBCs(b);
        
        r.setRPr(rpr);
        titleP.getContent().add(r);
        
        return titleP;
    }

    /**
     * 创建分部问题表格
     * @param questionGroupedData 按问题描述分组的数据
     * @param partIndex 分部序号
     * @param partName 分部名称
     * @param focusedRelations 关注的检测项目列表
     * @return 表格对象
     */
    private Tbl createPartQuestionTable(Map<String, List<TunnelCheck>> questionGroupedData, int partIndex, String partName, List<CheckEnumRelation> focusedRelations) {
        ObjectFactory factory = new ObjectFactory();

        // 创建关注项目的映射，用于获取单位信息
        Map<String, String> unitMap = new HashMap<>();
        for (CheckEnumRelation relation : focusedRelations) {
            String key = relation.getPartCode() + "_" + relation.getItemCode() + "_" + relation.getCheckContent();
            // 从CheckEnum表中获取单位信息
            CheckEnum checkEnum = new CheckEnum();
            checkEnum.setPartCode(relation.getPartCode());
            checkEnum.setItemCode(relation.getItemCode());
            checkEnum.setCheckContent(relation.getCheckContent());
            CheckEnum enumResult = checkEnumMapper.selectByParams(checkEnum);
            if (enumResult != null && StringUtils.isNotBlank(enumResult.getUnit())) {
                unitMap.put(key, enumResult.getUnit());
            }
        }

        Tbl table = factory.createTbl();
        
        // 设置表格属性
        TblPr tblPr = factory.createTblPr();
        
        // 设置表格宽度
        TblWidth tblWidth = factory.createTblWidth();
        tblWidth.setType("pct");
        tblWidth.setW(BigInteger.valueOf(5000));
        tblPr.setTblW(tblWidth);
        
        // 设置表格边框
        TblBorders borders = factory.createTblBorders();
        CTBorder solidBorder = factory.createCTBorder();
        solidBorder.setVal(STBorder.SINGLE);
        solidBorder.setColor("000000");
        
        borders.setTop(solidBorder);
        borders.setBottom(solidBorder);
        borders.setLeft(solidBorder);
        borders.setRight(solidBorder);
        borders.setInsideH(solidBorder);
        borders.setInsideV(solidBorder);
        
        tblPr.setTblBorders(borders);
        
        table.setTblPr(tblPr);
        
        // 创建表格网格（3列）
        TblGrid tblGrid = factory.createTblGrid();
        long[] columnWidths = {1400, 600, 3000}; // 主要问题、数量、所在位置
        
        for (long width : columnWidths) {
            TblGridCol gridCol = factory.createTblGridCol();
            gridCol.setW(BigInteger.valueOf(width));
            tblGrid.getGridCol().add(gridCol);
        }
        table.setTblGrid(tblGrid);
        
        // 1. 创建表头行
        Tr headerRow = createTableHeaderRow(factory);
        table.getContent().add(headerRow);
        
        // 3. 为每个问题创建4行内容
        for (Map.Entry<String, List<TunnelCheck>> questionEntry : questionGroupedData.entrySet()) {
            String questionDesc = questionEntry.getKey();
            List<TunnelCheck> questionCheckList = questionEntry.getValue();

            // 统计隧道信息
            Map<String, Integer> tunnelCountMap = questionCheckList.stream()
                .collect(Collectors.groupingBy(TunnelCheck::getTunnelName,
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)));

            // 构建所在位置文本
            String locationText = tunnelCountMap.entrySet().stream()
                .map(entry -> entry.getKey() + "（" + entry.getValue() + "处）")
                .collect(Collectors.joining("、"));

            // 获取单位信息
            String unit = "";
            if (!questionCheckList.isEmpty()) {
                TunnelCheck firstCheck = questionCheckList.get(0);
                String key = firstCheck.getPartCode() + "_" + firstCheck.getItemCode() + "_" + firstCheck.getCheckContent();
                unit = unitMap.getOrDefault(key, "");
            }

            // 构建带单位的数量文本
            String quantityText = questionCheckList.size() + (StringUtils.isNotBlank(unit) ? unit : "");

            // 第一行：问题数据行
            Tr questionRow = createQuestionDataRowWithUnit(factory, questionDesc, quantityText, locationText);
            table.getContent().add(questionRow);
            
            // 第二行：成因分析行
            Tr causeRow = createFixedContentRow(factory, "成因分析");
            table.getContent().add(causeRow);
            
            // 第三行：修复建议行
            Tr repairRow = createFixedContentRow(factory, "修复建议");
            table.getContent().add(repairRow);
        }
        
        // 4. 创建分部养护建议行
        Tr maintenanceRow = createMaintenanceRow(factory, partName);
        table.getContent().add(maintenanceRow);
        
        // 5. 创建备注行
        Tr noteRow = createNoteRow(factory);
        table.getContent().add(noteRow);
        
        return table;
    }



    /**
     * 创建表头行
     */
    private Tr createTableHeaderRow(ObjectFactory factory) {
        Tr headerRow = factory.createTr();
        String[] headers = {"主要问题", "数量", "所在位置"};
        long[] columnWidths = {1400, 600, 3000}; // 与表格网格保持一致
        
        for (int i = 0; i < headers.length; i++) {
            Tc headerCell = factory.createTc();
            
            // 设置表头背景色和单元格宽度
            TcPr tcPr = factory.createTcPr();
            
            // 设置单元格宽度
            TblWidth cellWidth = factory.createTblWidth();
            cellWidth.setType("dxa");
            cellWidth.setW(BigInteger.valueOf(columnWidths[i]));
            tcPr.setTcW(cellWidth);
            
            // 设置背景色
            CTShd shd = factory.createCTShd();
            shd.setVal(STShd.CLEAR);
            shd.setColor("auto");
            shd.setFill("DDDDDD");
            tcPr.setShd(shd);
            
            headerCell.setTcPr(tcPr);
            
            P headerP = factory.createP();
            PPr ppr = factory.createPPr();
            Jc jc = factory.createJc();
            jc.setVal(JcEnumeration.CENTER);
            ppr.setJc(jc);
            headerP.setPPr(ppr);
            
            R headerR = factory.createR();
            Text headerText = factory.createText();
            headerText.setValue(headers[i]);
            headerText.setSpace("preserve");
            headerR.getContent().add(headerText);
            
            // 设置宋体五号加粗
            RPr headerRpr = factory.createRPr();
            RFonts headerFonts = factory.createRFonts();
            headerFonts.setAscii("宋体");
            headerFonts.setHAnsi("宋体");
            headerFonts.setCs("宋体");
            headerFonts.setEastAsia("宋体");
            headerRpr.setRFonts(headerFonts);
            
            HpsMeasure headerSz = factory.createHpsMeasure();
            headerSz.setVal(BigInteger.valueOf(21));
            headerRpr.setSz(headerSz);
            headerRpr.setSzCs(headerSz);
            
            BooleanDefaultTrue headerBold = factory.createBooleanDefaultTrue();
            headerBold.setVal(true);
            headerRpr.setB(headerBold);
            headerRpr.setBCs(headerBold);
            
            headerR.setRPr(headerRpr);
            headerP.getContent().add(headerR);
            headerCell.getContent().add(headerP);
            headerRow.getContent().add(headerCell);
        }
        
        return headerRow;
    }

    /**
     * 创建问题数据行（带单位）
     */
    private Tr createQuestionDataRowWithUnit(ObjectFactory factory, String questionDesc, String quantityText, String locationText) {
        Tr dataRow = factory.createTr();
        String[] cellTexts = {questionDesc, quantityText, locationText};
        long[] columnWidths = {1400, 600, 3000}; // 与表格网格保持一致

        for (int i = 0; i < cellTexts.length; i++) {
            Tc dataCell = factory.createTc();

            // 设置单元格宽度
            TcPr tcPr = factory.createTcPr();
            TblWidth cellWidth = factory.createTblWidth();
            cellWidth.setType("dxa");
            cellWidth.setW(BigInteger.valueOf(columnWidths[i]));
            tcPr.setTcW(cellWidth);
            dataCell.setTcPr(tcPr);

            P dataP = factory.createP();
            PPr ppr = factory.createPPr();
            Jc jc = factory.createJc();
            jc.setVal(i == 1 ? JcEnumeration.CENTER : JcEnumeration.LEFT); // 数量居中，其他左对齐
            ppr.setJc(jc);
            dataP.setPPr(ppr);

            R dataR = factory.createR();
            Text dataText = factory.createText();
            dataText.setValue(cellTexts[i]);
            dataText.setSpace("preserve");
            dataR.getContent().add(dataText);

            // 设置宋体五号
            RPr dataRpr = factory.createRPr();
            RFonts dataFonts = factory.createRFonts();
            dataFonts.setAscii("宋体");
            dataFonts.setHAnsi("宋体");
            dataFonts.setCs("宋体");
            dataFonts.setEastAsia("宋体");
            dataRpr.setRFonts(dataFonts);

            HpsMeasure dataSz = factory.createHpsMeasure();
            dataSz.setVal(BigInteger.valueOf(21));
            dataRpr.setSz(dataSz);
            dataRpr.setSzCs(dataSz);

            dataR.setRPr(dataRpr);
            dataP.getContent().add(dataR);
            dataCell.getContent().add(dataP);
            dataRow.getContent().add(dataCell);
        }

        return dataRow;
    }

    /**
     * 创建问题数据行
     */
    private Tr createQuestionDataRow(ObjectFactory factory, String questionDesc, int count, String locationText) {
        Tr dataRow = factory.createTr();
        String[] cellTexts = {questionDesc, String.valueOf(count), locationText};
        long[] columnWidths = {1400, 600, 3000}; // 与表格网格保持一致
        
        for (int i = 0; i < cellTexts.length; i++) {
            Tc dataCell = factory.createTc();
            
            // 设置单元格宽度
            TcPr tcPr = factory.createTcPr();
            TblWidth cellWidth = factory.createTblWidth();
            cellWidth.setType("dxa");
            cellWidth.setW(BigInteger.valueOf(columnWidths[i]));
            tcPr.setTcW(cellWidth);
            dataCell.setTcPr(tcPr);
            
            P dataP = factory.createP();
            PPr ppr = factory.createPPr();
            Jc jc = factory.createJc();
            jc.setVal(i == 1 ? JcEnumeration.CENTER : JcEnumeration.LEFT); // 数量居中，其他左对齐
            ppr.setJc(jc);
            dataP.setPPr(ppr);
            
            R dataR = factory.createR();
            Text dataText = factory.createText();
            dataText.setValue(cellTexts[i]);
            dataText.setSpace("preserve");
            dataR.getContent().add(dataText);
            
            // 设置宋体五号
            RPr dataRpr = factory.createRPr();
            RFonts dataFonts = factory.createRFonts();
            dataFonts.setAscii("宋体");
            dataFonts.setHAnsi("宋体");
            dataFonts.setCs("宋体");
            dataFonts.setEastAsia("宋体");
            dataRpr.setRFonts(dataFonts);
            
            HpsMeasure dataSz = factory.createHpsMeasure();
            dataSz.setVal(BigInteger.valueOf(21));
            dataRpr.setSz(dataSz);
            dataRpr.setSzCs(dataSz);
            
            dataR.setRPr(dataRpr);
            dataP.getContent().add(dataR);
            dataCell.getContent().add(dataP);
            dataRow.getContent().add(dataCell);
        }
        
        return dataRow;
    }

    /**
     * 创建固定内容行（成因分析、修复建议）
     */
    private Tr createFixedContentRow(ObjectFactory factory, String content) {
        Tr fixedRow = factory.createTr();
        long[] columnWidths = {1400, 600, 3000}; // 与表格网格保持一致
        
        // 第一列：固定内容
        Tc firstCell = factory.createTc();
        
        // 设置第一列宽度
        TcPr firstTcPr = factory.createTcPr();
        TblWidth firstCellWidth = factory.createTblWidth();
        firstCellWidth.setType("dxa");
        firstCellWidth.setW(BigInteger.valueOf(columnWidths[0]));
        firstTcPr.setTcW(firstCellWidth);
        firstCell.setTcPr(firstTcPr);
        
        P firstP = factory.createP();
        PPr ppr = factory.createPPr();
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.LEFT);
        ppr.setJc(jc);
        firstP.setPPr(ppr);
        
        R firstR = factory.createR();
        Text firstText = factory.createText();
        firstText.setValue(content);
        firstText.setSpace("preserve");
        firstR.getContent().add(firstText);
        
        // 设置宋体五号
        RPr firstRpr = factory.createRPr();
        RFonts firstFonts = factory.createRFonts();
        firstFonts.setAscii("宋体");
        firstFonts.setHAnsi("宋体");
        firstFonts.setCs("宋体");
        firstFonts.setEastAsia("宋体");
        firstRpr.setRFonts(firstFonts);
        
        HpsMeasure firstSz = factory.createHpsMeasure();
        firstSz.setVal(BigInteger.valueOf(21));
        firstRpr.setSz(firstSz);
        firstRpr.setSzCs(firstSz);
        
        firstR.setRPr(firstRpr);
        firstP.getContent().add(firstR);
        firstCell.getContent().add(firstP);
        fixedRow.getContent().add(firstCell);
        
        // 第二、三列：合并的空单元格
        Tc mergedCell = factory.createTc();
        TcPr tcPr = factory.createTcPr();
        
        // 设置合并列的总宽度（第二列 + 第三列）
        TblWidth mergedCellWidth = factory.createTblWidth();
        mergedCellWidth.setType("dxa");
        mergedCellWidth.setW(BigInteger.valueOf(columnWidths[1] + columnWidths[2])); // 1300 + 2500 = 3800
        tcPr.setTcW(mergedCellWidth);
        
        // 设置跨列
        TcPrInner.GridSpan gridSpan = factory.createTcPrInnerGridSpan();
        gridSpan.setVal(BigInteger.valueOf(2));
        tcPr.setGridSpan(gridSpan);
        
        mergedCell.setTcPr(tcPr);
        
        P emptyP = factory.createP();
        mergedCell.getContent().add(emptyP);
        fixedRow.getContent().add(mergedCell);
        
        return fixedRow;
    }

    /**
     * 创建分部养护建议行
     * @param factory ObjectFactory
     * @param partName 分部名称
     * @return 养护建议行
     */
    private Tr createMaintenanceRow(ObjectFactory factory, String partName) {
        Tr maintenanceRow = factory.createTr();
        long[] columnWidths = {1400, 600, 3000}; // 与表格网格保持一致
        
        // 第一列：分部名称 + 养护建议
        Tc firstCell = factory.createTc();
        
        // 设置第一列宽度
        TcPr firstTcPr = factory.createTcPr();
        TblWidth firstCellWidth = factory.createTblWidth();
        firstCellWidth.setType("dxa");
        firstCellWidth.setW(BigInteger.valueOf(columnWidths[0]));
        firstTcPr.setTcW(firstCellWidth);
        firstCell.setTcPr(firstTcPr);
        
        P firstP = factory.createP();
        PPr ppr = factory.createPPr();
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.LEFT);
        ppr.setJc(jc);
        firstP.setPPr(ppr);
        
        R firstR = factory.createR();
        Text firstText = factory.createText();
        firstText.setValue(partName + "养护建议");
        firstText.setSpace("preserve");
        firstR.getContent().add(firstText);
        
        // 设置宋体五号
        RPr firstRpr = factory.createRPr();
        RFonts firstFonts = factory.createRFonts();
        firstFonts.setAscii("宋体");
        firstFonts.setHAnsi("宋体");
        firstFonts.setCs("宋体");
        firstFonts.setEastAsia("宋体");
        firstRpr.setRFonts(firstFonts);
        
        HpsMeasure firstSz = factory.createHpsMeasure();
        firstSz.setVal(BigInteger.valueOf(21)); // 五号 = 10.5磅 * 2
        firstRpr.setSz(firstSz);
        firstRpr.setSzCs(firstSz);
        
        firstR.setRPr(firstRpr);
        firstP.getContent().add(firstR);
        firstCell.getContent().add(firstP);
        maintenanceRow.getContent().add(firstCell);
        
        // 第二、三列：合并的空单元格
        Tc mergedCell = factory.createTc();
        TcPr tcPr = factory.createTcPr();
        
        // 设置合并列的总宽度（第二列 + 第三列）
        TblWidth mergedCellWidth = factory.createTblWidth();
        mergedCellWidth.setType("dxa");
        mergedCellWidth.setW(BigInteger.valueOf(columnWidths[1] + columnWidths[2])); // 600 + 3000 = 3600
        tcPr.setTcW(mergedCellWidth);
        
        // 设置跨列
        TcPrInner.GridSpan gridSpan = factory.createTcPrInnerGridSpan();
        gridSpan.setVal(BigInteger.valueOf(2));
        tcPr.setGridSpan(gridSpan);
        
        mergedCell.setTcPr(tcPr);
        
        P emptyP = factory.createP();
        mergedCell.getContent().add(emptyP);
        maintenanceRow.getContent().add(mergedCell);
        
        return maintenanceRow;
    }

    /**
     * 创建备注行
     */
    private Tr createNoteRow(ObjectFactory factory) {
        Tr noteRow = factory.createTr();
        Tc noteCell = factory.createTc();
        long[] columnWidths = {1400, 600, 3000}; // 与表格网格保持一致
        
        // 设置单元格属性（合并3列）
        TcPr tcPr = factory.createTcPr();
        
        // 设置合并列的总宽度（所有列的宽度之和）
        TblWidth noteCellWidth = factory.createTblWidth();
        noteCellWidth.setType("dxa");
        noteCellWidth.setW(BigInteger.valueOf(columnWidths[0] + columnWidths[1] + columnWidths[2])); // 1200 + 1300 + 2500 = 5000
        tcPr.setTcW(noteCellWidth);
        
        // 设置跨列
        TcPrInner.GridSpan gridSpan = factory.createTcPrInnerGridSpan();
        gridSpan.setVal(BigInteger.valueOf(3));
        tcPr.setGridSpan(gridSpan);
        
        noteCell.setTcPr(tcPr);
        
        P noteP = factory.createP();
        PPr ppr = factory.createPPr();
        Jc jc = factory.createJc();
        jc.setVal(JcEnumeration.LEFT);
        ppr.setJc(jc);
        noteP.setPPr(ppr);
        
        R noteR = factory.createR();
        Text noteText = factory.createText();
        noteText.setValue("备注：详细检测结果参见各隧道报告");
        noteText.setSpace("preserve");
        noteR.getContent().add(noteText);
        
        // 设置宋体小五号
        RPr noteRpr = factory.createRPr();
        RFonts noteFonts = factory.createRFonts();
        noteFonts.setAscii("宋体");
        noteFonts.setHAnsi("宋体");
        noteFonts.setCs("宋体");
        noteFonts.setEastAsia("宋体");
        noteRpr.setRFonts(noteFonts);
        
        HpsMeasure noteSz = factory.createHpsMeasure();
        noteSz.setVal(BigInteger.valueOf(18)); // 小五号 = 9磅 * 2
        noteRpr.setSz(noteSz);
        noteRpr.setSzCs(noteSz);
        
        noteR.setRPr(noteRpr);
        noteP.getContent().add(noteR);
        noteCell.getContent().add(noteP);
        noteRow.getContent().add(noteCell);
        
        return noteRow;
    }

    /**
     * 替换占位符为多个内容对象
     */
    private void replaceWithMultipleContents(WordprocessingMLPackage wordMLPackage, String replacePlaceholder, List<Object> contents) {
        String searchPlaceholder = "${" + replacePlaceholder + "}";
        
        try {
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            
            // 查找包含占位符的段落并替换
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof P) {
                    P paragraph = (P) obj;
                    String paragraphText = String.valueOf(paragraph);
                    
                    if (paragraphText.contains(searchPlaceholder)) {
                        log.info("找到占位符: {} 在段落 {} 中", searchPlaceholder, i);
                        
                        // 移除占位符段落
                        docObjects.remove(i);
                        
                        // 在原位置插入所有内容
                        for (int j = 0; j < contents.size(); j++) {
                            docObjects.add(i + j, contents.get(j));
                        }
                        
                        log.info("成功替换 {} 占位符为 {} 个内容对象", searchPlaceholder, contents.size());
                        return;
                    }
                }
            }
            
            log.warn("未找到占位符: {}", searchPlaceholder);
            
        } catch (Exception e) {
            log.error("替换占位符为多个内容失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 查找占位符并替换为标题段落和表格
     * @param wordMLPackage Word文档对象
     * @param titleParagraph 标题段落
     * @param table 表格
     * @param replacePlaceholder 占位符
     */
    private void findAndReplaceWithTitleAndTable(WordprocessingMLPackage wordMLPackage, org.docx4j.wml.P titleParagraph, Tbl table, String replacePlaceholder) {
        String searchPlaceholder = "${" + replacePlaceholder + "}";
        
        try {
            // 获取主文档部分
            org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
            List<Object> docObjects = mainDocumentPart.getContent();
            
            // 查找包含占位符的段落并替换
            for (int i = 0; i < docObjects.size(); i++) {
                Object obj = docObjects.get(i);
                if (obj instanceof org.docx4j.wml.P) {
                    org.docx4j.wml.P paragraph = (org.docx4j.wml.P) obj;
                    String paragraphText = String.valueOf(paragraph);
                    
                    if (paragraphText.contains(searchPlaceholder)) {
                        log.info("找到占位符: {} 在段落 {} 中", searchPlaceholder, i);
                        
                        // 移除占位符段落
                        docObjects.remove(i);
                        
                        // 在原位置插入标题段落和表格
                        docObjects.add(i, titleParagraph);
                        docObjects.add(i + 1, table);
                        
                        log.info("成功替换 {} 占位符为标题和表格", searchPlaceholder);
                        return;
                    }
                }
            }
            
            log.warn("未找到占位符: {}", searchPlaceholder);
            
        } catch (Exception e) {
            log.error("替换占位符为标题和表格失败: {}", replacePlaceholder, e);
        }
    }

    /**
     * 在文档中生成图表（饼图和折线图）
     * @param wordMLPackage Word文档对象
     * @param tunnelPartScoreList 隧道评分列表
     * @param tunnelCheckList 隧道检测任务列表
     * @return 包含图表的XWPFDocument，失败时返回null
     */
    private XWPFDocument generateChartsInDocument(WordprocessingMLPackage wordMLPackage, List<TunnelInfo> tunnelPartScoreList, List<TunnelCheck> tunnelCheckList) {
        if (tunnelPartScoreList == null || tunnelPartScoreList.isEmpty()) {
            log.warn("隧道评分列表为空，无法生成图表");
            return null;
        }
        
        try {
            log.info("开始生成图表，共 {} 条隧道数据", tunnelPartScoreList.size());
            // 1. 统计各类别隧道数量
            Map<String, Integer> categoryStats = new HashMap<>();
            categoryStats.put("1类", 0);
            categoryStats.put("2类", 0);
            categoryStats.put("3类", 0);
            categoryStats.put("4类", 0);
            for (TunnelInfo tunnel : tunnelPartScoreList) {
                String category = tunnel.getCategory();
                if (category != null && categoryStats.containsKey(category)) {
                    categoryStats.put(category, categoryStats.get(category) + 1);
                }
            }
            
            // 2. 计算各分部的平均得分（折线图数据）
            List<PartAvgScoreInfo> partAvgScoreList = calculatePartAvgScores(tunnelPartScoreList, tunnelCheckList);
            
            // 检查是否有有效数据
            boolean hasPieData = categoryStats.values().stream().anyMatch(count -> count > 0);
            boolean hasLineData = !partAvgScoreList.isEmpty();
            
            if (!hasPieData && !hasLineData) {
                log.warn("没有有效的图表数据");
                return null;
            }
            
            log.info("类别统计结果: {}", categoryStats);
            log.info("分部平均得分数据: {} 个分部", partAvgScoreList.size());
            // 3. 将docx4j文档转换为临时文件
            File tempFile = File.createTempFile("tunnel_report_" + System.currentTimeMillis() + "_", ".docx");
            try {
                // 保存原文档到临时文件
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    wordMLPackage.save(fos);
                }
                log.info("文档已保存到临时文件: {}", tempFile.getAbsolutePath());
                
                // 4. 使用POI加载临时文件并处理图表
                try (FileInputStream fis = new FileInputStream(tempFile)) {
                    XWPFDocument document = new XWPFDocument(fis);
                    
                    // 解决POI安全限制
                    org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);
                    
                    boolean chartsInserted = true;
                    
                    // 5. 插入饼图
                    if (hasPieData) {
                        boolean pieChartInserted = insertPieChart(document, categoryStats, "${chartOne}");
                        if (!pieChartInserted) {
                            log.warn("饼图插入失败");
                            chartsInserted = false;
                        } else {
                            log.info("饼图插入成功");
                        }
                    }
                    
                    // 6. 插入折线图
                    if (hasLineData) {
                        boolean lineChartInserted = insertLineChart(document, partAvgScoreList, "${chartTwo}");
                        if (!lineChartInserted) {
                            log.warn("折线图插入失败");
                            chartsInserted = false;
                        } else {
                            log.info("折线图插入成功");
                        }
                    }
                    
                    if (chartsInserted || hasPieData || hasLineData) {
                        log.info("图表插入完成");
                        return document;
                    } else {
                        log.warn("所有图表插入失败");
                        document.close();
                        return null;
                    }
                }
            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    boolean deleted = tempFile.delete();
                    if (!deleted) {
                        log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("生成图表失败", e);
            return null;
        }
    }
    
    /**
     * 导出包含图表的Word文档
     * @param document XWPFDocument对象
     * @param response HTTP响应对象
     * @param pageTitle 页面标题
     */
    private void exportDocumentWithChart(XWPFDocument document, HttpServletResponse response, String pageTitle) {
        try {
            // 使用POI导出Word文档
            exportDocumentWithPOI(document, response, pageTitle);
        } catch (Exception e) {
            log.error("导出包含图表的文档失败", e);
            throw new ServiceException("导出包含图表的文档失败: " + e.getMessage());
        } finally {
            try {
                document.close();
            } catch (Exception e) {
                log.warn("关闭文档时发生异常: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 使用POI导出Word文档
     * @param document XWPFDocument对象
     * @param response HTTP响应对象
     * @param pageTitle 页面标题
     */
    private void exportDocumentWithPOI(XWPFDocument document, HttpServletResponse response, String pageTitle) {
        try {
            log.info("开始使用POI导出Word文档");
            log.info("文档信息 - 段落数: {}, 表格数: {}", 
                document.getParagraphs().size(), 
                document.getTables().size());
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(pageTitle, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
            
            log.info("响应头设置完成，文件名: {}", fileName);
            
            // 先保存到字节数组以获取大小信息
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.write(baos);
            byte[] documentBytes = baos.toByteArray();
            
            log.info("文档写入字节数组完成，大小: {} bytes", documentBytes.length);
            
            // 设置内容长度
            response.setHeader("Content-Length", String.valueOf(documentBytes.length));
            
            // 输出到响应流
            response.getOutputStream().write(documentBytes);
            response.getOutputStream().flush();
            
            log.info("使用POI成功导出Word文档: {}，文件大小: {} bytes", pageTitle, documentBytes.length);
        } catch (Exception e) {
            log.error("使用POI导出Word文档失败", e);
            throw new ServiceException("导出Word文档失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出原始文档（fallback方法）
     * @param wordMLPackage 原始文档对象
     * @param response HTTP响应对象
     * @param pageTitle 页面标题
     */
    private void exportOriginalDocument(WordprocessingMLPackage wordMLPackage, HttpServletResponse response, String pageTitle) {
        try {
            // 先将文档保存到字节数组，以便获取文件大小
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            wordMLPackage.save(baos);
            byte[] documentBytes = baos.toByteArray();
            
            // 设置响应头，包含文件大小信息
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(pageTitle, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
            response.setHeader("Content-Length", String.valueOf(documentBytes.length));
            
            // 将字节数组写入响应流
            response.getOutputStream().write(documentBytes);
            response.getOutputStream().flush();
            
            log.info("使用docx4j成功导出原始Word文档: {}", pageTitle);
        } catch (Exception e) {
            log.error("导出原始Word文档失败", e);
            throw new ServiceException("导出Word文档失败: " + e.getMessage());
        }
    }
    
    /**
     * 在文档中插入饼图
     * @param document XWPFDocument对象
     * @param categoryStats 类别统计数据
     * @param placeholder 占位符
     * @return 是否成功插入
     */
    private boolean insertPieChart(XWPFDocument document, Map<String, Integer> categoryStats, String placeholder) {
        try {
            log.info("开始查找占位符: {} 在文档中的位置", placeholder);
            
            // 查找包含占位符的段落
            XWPFParagraph targetParagraph = findParagraphByPlaceholder(document, placeholder);
            if (targetParagraph == null) {
                log.warn("未在任何段落中找到占位符: {}", placeholder);
                return false;
            }
            
            // 准备数据 - 只包含非零数据
            List<String> categories = new ArrayList<>();
            List<Integer> values = new ArrayList<>();
            
            for (Map.Entry<String, Integer> entry : categoryStats.entrySet()) {
                if (entry.getValue() > 0) {
                    categories.add(entry.getKey());
                    values.add(entry.getValue());
                }
            }
            
            log.info("饼图数据准备完成 - 类别: {}, 数值: {}", categories, values);
            
            if (!categories.isEmpty()) {
                try {
                    // 使用通用方法创建饼图 - 调整为文档宽度100%
                    XWPFChart chart = WordDocumentUtil.createPieChart(document, categories, values,
                        "机电设施技术状况评定类别占比", "隧道类别分布", 5486400, 3600000);
                    
                    if (chart != null) {
                        // 尝试移动图表到正确位置
                        try {
                            // 先清除占位符内容
                            clearParagraphAndCenter(targetParagraph);
                            
                            moveChartToTargetParagraph(document, chart, targetParagraph);
                            log.info("饼图移动到目标位置成功！类别数量: {}, 总数值: {}", 
                                categories.size(), 
                                values.stream().mapToInt(Integer::intValue).sum());
                        } catch (Exception moveEx) {
                            log.warn("移动图表到目标位置失败，图表将显示在文档末尾: {}", moveEx.getMessage());
                            // 在占位符位置添加说明文本
                            clearParagraphAndCenter(targetParagraph);
                            XWPFRun placeholderRun = targetParagraph.createRun();
                            placeholderRun.setText("【饼图已生成，请查看文档末尾】");
                            placeholderRun.setBold(true);
                            placeholderRun.setColor("0066CC");
                        }
                        
                        return true;
                    } else {
                        log.error("创建饼图失败");
                        // 插入错误提示文本
                        clearParagraphAndCenter(targetParagraph);
                        XWPFRun run = targetParagraph.createRun();
                        run.setText("图表创建失败");
                        run.setFontSize(12);
                        run.setColor("FF0000"); // 红色文本
                        return false;
                    }
                } catch (Exception chartException) {
                    log.error("创建图表时发生异常", chartException);
                    // 插入错误提示文本
                    clearParagraphAndCenter(targetParagraph);
                    XWPFRun run = targetParagraph.createRun();
                    run.setText("图表创建失败: " + chartException.getMessage());
                    run.setFontSize(12);
                    run.setColor("FF0000"); // 红色文本
                    return false;
                }
            } else {
                log.warn("没有有效的类别数据，无法创建饼图");
                // 插入提示文本
                clearParagraphAndCenter(targetParagraph);
                XWPFRun run = targetParagraph.createRun();
                run.setText("暂无类别分布数据");
                run.setFontSize(12);
                run.setFontFamily("Microsoft YaHei");
                return false;
            }
            
        } catch (Exception e) {
            log.error("插入饼图时发生异常", e);
            return false;
        }
    }
    
    /**
     * 在文档中插入折线图
     * @param document XWPFDocument对象
     * @param partAvgScoreList 分部平均得分数据
     * @param placeholder 占位符
     * @return 是否成功插入
     */
    private boolean insertLineChart(XWPFDocument document, List<PartAvgScoreInfo> partAvgScoreList, String placeholder) {
        try {
            log.info("开始查找占位符: {} 在文档中的位置", placeholder);
            
            // 查找包含占位符的段落
            XWPFParagraph targetParagraph = findParagraphByPlaceholder(document, placeholder);
            if (targetParagraph == null) {
                log.warn("未在任何段落中找到占位符: {}", placeholder);
                return false;
            }
            
            // 准备数据
            List<String> categories = new ArrayList<>();
            List<Double> values = new ArrayList<>();
            
            for (PartAvgScoreInfo partInfo : partAvgScoreList) {
                categories.add(partInfo.getPartName());
                values.add(partInfo.getAvgScore().doubleValue());
            }
            
            log.info("折线图数据准备完成 - 类别: {}, 数值: {}", categories, values);
            
            if (!categories.isEmpty()) {
                try {
                    // 使用通用方法创建折线图 - 调整为文档宽度100%
                    XWPFChart chart = WordDocumentUtil.createLineChart(document, categories, values, "分部工程平均得分", "分部得分", 5486400, 3600000);
                    
                    if (chart != null) {
                        // 尝试移动图表到正确位置
                        try {
                            // 先清除占位符内容
                            clearParagraphAndCenter(targetParagraph);
                            
                            moveChartToTargetParagraph(document, chart, targetParagraph);
                            log.info("折线图移动到目标位置成功！分部数量: {}, 数据点: {}", 
                                categories.size(), values.size());
                        } catch (Exception moveEx) {
                            log.warn("移动图表到目标位置失败，图表将显示在文档末尾: {}", moveEx.getMessage());
                            // 在占位符位置添加说明文本
                            clearParagraphAndCenter(targetParagraph);
                            XWPFRun placeholderRun = targetParagraph.createRun();
                            placeholderRun.setText("【折线图已生成，请查看文档末尾】");
                            placeholderRun.setBold(true);
                            placeholderRun.setColor("0066CC");
                        }
                        
                        return true;
                    } else {
                        log.error("创建折线图失败");
                        // 插入错误提示文本
                        clearParagraphAndCenter(targetParagraph);
                        XWPFRun run = targetParagraph.createRun();
                        run.setText("折线图创建失败");
                        run.setFontSize(12);
                        run.setColor("FF0000"); // 红色文本
                        return false;
                    }
                } catch (Exception chartException) {
                    log.error("创建折线图时发生异常", chartException);
                    // 插入错误提示文本
                    clearParagraphAndCenter(targetParagraph);
                    XWPFRun run = targetParagraph.createRun();
                    run.setText("折线图创建失败: " + chartException.getMessage());
                    run.setFontSize(12);
                    run.setColor("FF0000"); // 红色文本
                    return false;
                }
            } else {
                log.warn("没有有效的分部得分数据，无法创建折线图");
                // 插入提示文本
                clearParagraphAndCenter(targetParagraph);
                XWPFRun run = targetParagraph.createRun();
                run.setText("暂无分部得分数据");
                run.setFontSize(12);
                run.setFontFamily("Microsoft YaHei");
                return false;
            }
            
        } catch (Exception e) {
            log.error("插入折线图时发生异常", e);
            return false;
        }
    }
    
    /**
     * 尝试将图表移动到目标段落位置
     * @param document Word文档对象
     * @param chart 图表对象
     * @param targetParagraph 目标段落
     */
    private void moveChartToTargetParagraph(XWPFDocument document, XWPFChart chart, XWPFParagraph targetParagraph) {
        try {
            log.info("开始尝试移动图表到目标段落");
            
            // 获取文档中最后一个段落（图表通常在这里）
            List<XWPFParagraph> allParagraphs = document.getParagraphs();
            XWPFParagraph lastParagraph = allParagraphs.get(allParagraphs.size() - 1);
            
            // 检查最后一个段落是否包含图表
            if (lastParagraph.getRuns() != null && !lastParagraph.getRuns().isEmpty()) {
                for (XWPFRun run : lastParagraph.getRuns()) {
                    // 检查Run是否包含图表相关内容 (使用新的API)
                    if (run.getCTR() != null && run.getCTR().sizeOfDrawingArray() > 0) {
                        log.info("在最后一个段落中找到图表Run");
                        
                        // 将图表Run移动到目标段落
                        XWPFRun targetRun = targetParagraph.createRun();
                        
                        // 复制图表Drawing内容
                        if (run.getCTR().sizeOfDrawingArray() > 0) {
                            // 复制所有Drawing元素
                            for (int drawingIndex = 0; drawingIndex < run.getCTR().sizeOfDrawingArray(); drawingIndex++) {
                                targetRun.getCTR().addNewDrawing().set(run.getCTR().getDrawingArray(drawingIndex));
                            }
                            log.info("图表Drawing内容已复制到目标段落");
                            
                            // 清除原位置的图表
                            while (run.getCTR().sizeOfDrawingArray() > 0) {
                                run.getCTR().removeDrawing(0);
                            }
                            
                            // 如果原段落现在为空，删除它
                            if (lastParagraph.getText().trim().isEmpty()) {
                                document.removeBodyElement(document.getPosOfParagraph(lastParagraph));
                                log.info("已删除空的原图表段落");
                            }
                            
                            log.info("图表移动完成");
                            return;
                        }
                    }
                }
            }
            
            // 如果上述方法失败，尝试查找图表关系
            log.warn("在最后段落中未找到图表内容，尝试其他方法");
            
            // 方法2：在目标段落添加图表引用
            XWPFRun chartRun = targetParagraph.createRun();
            chartRun.setText("【图表位置 - 由于技术限制，图表显示在文档末尾】");
            chartRun.setBold(true);
            chartRun.setColor("FF6600");
            
        } catch (Exception e) {
            log.error("移动图表过程中发生异常", e);
            throw e;
        }
    }
    
    // ================================== 图表创建通用方法 ==================================
    // 注意：以下方法建议移动到WordDocumentUtil类中，以便其他地方复用
    
    /**
     * 查找包含指定占位符的段落
     * 建议移动到WordDocumentUtil中
     * @param document XWPFDocument对象
     * @param placeholder 占位符
     * @return 包含占位符的段落，未找到返回null
     */
    private XWPFParagraph findParagraphByPlaceholder(XWPFDocument document, String placeholder) {
        List<XWPFParagraph> allParagraphs = document.getParagraphs();
        log.info("文档总段落数: {}", allParagraphs.size());
        
        for (int i = 0; i < allParagraphs.size(); i++) {
            XWPFParagraph paragraph = allParagraphs.get(i);
            String paragraphText = paragraph.getText();
            log.debug("段落 {} 内容: [{}]", i, paragraphText);
            
            if (paragraphText != null && paragraphText.contains(placeholder)) {
                log.info("在段落 {} 中找到占位符: {}", i, placeholder);
                return paragraph;
            }
        }
        
        return null;
    }
    
    /**
     * 清空段落内容并设置居中对齐
     * 建议移动到WordDocumentUtil中
     * @param paragraph 要清空的段落
     */
    private void clearParagraphAndCenter(XWPFParagraph paragraph) {
        // 清空段落内容
        for (int j = paragraph.getRuns().size() - 1; j >= 0; j--) {
            paragraph.removeRun(j);
        }
        
        // 设置段落居中
        paragraph.setAlignment(ParagraphAlignment.CENTER);
    }

}
