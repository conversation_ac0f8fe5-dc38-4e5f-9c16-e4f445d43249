package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.CheckUser;
import com.tunnel.service.CheckUserService;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.core.page.TableDataInfo;

/**
 * 检测人员信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/electric/checkUser")
public class CheckUserController extends BaseController {
    @Autowired
    private CheckUserService checkUserService;

    /**
     * 查询检测人员信息列表
     */
//    @PreAuthorize("@ss.hasPermi('electric:checkUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckUser checkUser) {
        startPage();
        List<CheckUser> list = checkUserService.selectCheckUserList(checkUser);
        return getDataTable(list);
    }

    /**
     * 导出检测人员信息列表
     */
//    @PreAuthorize("@ss.hasPermi('electric:checkUser:export')")
    @Log(title = "检测人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckUser checkUser) {
        List<CheckUser> list = checkUserService.selectCheckUserList(checkUser);
        ExcelUtil<CheckUser> util = new ExcelUtil<CheckUser>(CheckUser.class);
        util.exportExcel(response, list, "检测人员信息数据");
    }

    /**
     * 获取检测人员信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('electric:checkUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkUserService.selectCheckUserById(id));
    }

    /**
     * 新增检测人员信息
     */
//    @PreAuthorize("@ss.hasPermi('electric:checkUser:add')")
    @Log(title = "检测人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckUser checkUser) {
        return toAjax(checkUserService.insertCheckUser(checkUser));
    }

    /**
     * 修改检测人员信息
     */
//    @PreAuthorize("@ss.hasPermi('electric:checkUser:edit')")
    @Log(title = "检测人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckUser checkUser) {
        return toAjax(checkUserService.updateCheckUser(checkUser));
    }

    /**
     * 删除检测人员信息
     */
//    @PreAuthorize("@ss.hasPermi('electric:checkUser:remove')")
    @Log(title = "检测人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkUserService.deleteCheckUserByIds(ids));
    }
} 