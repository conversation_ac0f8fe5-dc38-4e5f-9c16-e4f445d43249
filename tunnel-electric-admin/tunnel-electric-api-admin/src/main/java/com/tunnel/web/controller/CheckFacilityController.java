package com.tunnel.web.controller;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.CheckFacilityRecord;
import com.tunnel.service.TunnelInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.CheckFacility;
import com.tunnel.service.CheckFacilityService;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.core.page.TableDataInfo;

/**
 * 检测设备Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/tunnel/electric/checkFacility")
public class CheckFacilityController extends BaseController {
    @Autowired
    private CheckFacilityService checkFacilityService;
    @Resource
    private TunnelInfoService tunnelInfoService;

    /**
     * 查询检测设备列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CheckFacility checkFacility) {
        startPage();
        List<CheckFacility> list = checkFacilityService.selectScCheckFacilityList(checkFacility);
        return getDataTable(list);
    }



    @GetMapping("/listAllCheckFacility")
    public TableDataInfo listAllCheckFacility(CheckFacility checkFacility) {
        List<CheckFacility> list = checkFacilityService.selectScCheckFacilityList(checkFacility);
        return getDataTable(list);
    }

    /**
     * 导出检测设备列表
     */
    @PreAuthorize("@ss.hasPermi('electric:scCheckFacility:export')")
    @Log(title = "检测设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckFacility checkFacility) {
        List<CheckFacility> list = checkFacilityService.selectScCheckFacilityList(checkFacility);
        ExcelUtil<CheckFacility> util = new ExcelUtil<CheckFacility>(CheckFacility.class);
        util.exportExcel(response, list, "检测设备数据");
    }

    /**
     * 获取检测设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('electric:scCheckFacility:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkFacilityService.selectScCheckFacilityById(id));
    }

    /**
     * 新增检测设备
     */
    @PreAuthorize("@ss.hasPermi('electric:scCheckFacility:add')")
    @Log(title = "检测设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckFacility checkFacility) {
        return toAjax(checkFacilityService.insertScCheckFacility(checkFacility));
    }

    /**
     * 修改检测设备
     */
    @PreAuthorize("@ss.hasPermi('electric:scCheckFacility:edit')")
    @Log(title = "检测设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckFacility checkFacility) {
        return toAjax(checkFacilityService.updateScCheckFacility(checkFacility));
    }

    /**
     * 删除检测设备
     */
    @PreAuthorize("@ss.hasPermi('electric:scCheckFacility:remove')")
    @Log(title = "检测设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkFacilityService.deleteScCheckFacilityByIds(ids));
    }

    /**
     * 根据设备编号查询检测设备
     */
    @PostMapping("/selectByCode")
    public AjaxResult selectByCode(@RequestBody CheckFacility checkFacility) {
        CheckFacility facility = checkFacilityService.selectScCheckFacilityByCode(checkFacility.getCode());
        return AjaxResult.success(facility);
    }

    /**
     * 查询所有在库设备
     */
    @GetMapping("/selectInStockList")
    public AjaxResult selectInStockList() {
        CheckFacility query = new CheckFacility();
        query.setFlag(1); // 1=在库
        List<CheckFacility> list = checkFacilityService.selectScCheckFacilityList(query);
        return AjaxResult.success(list);
    }

    /**
     * 导入检测设备数据
     */
    @Log(title = "检测设备", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('electric:scCheckFacility:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<CheckFacility> util = new ExcelUtil<CheckFacility>(CheckFacility.class);
        List<CheckFacility> facilityList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = checkFacilityService.importCheckFacility(facilityList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载检测设备导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/checkFacilityTemplate.xlsx");
        InputStream inputStream = resource.getInputStream();
        String fileName = "检测设备导入模板.xlsx";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    /**
     * 获取当前用户对应任务的检测设备
     */
    @PostMapping("/getCheckFacilitiesByUser")
    public AjaxResult getCheckFacilitiesByUser(@RequestBody Map<String, Object> params) {
        Long tunnelId = Long.valueOf(params.get("tunnelId").toString());
        Long userId = SecurityUtils.getUserId();
        List<CheckFacilityRecord> facilities = tunnelInfoService.getCheckFacilitiesByUser(tunnelId, userId);
        return AjaxResult.success(facilities);
    }
}