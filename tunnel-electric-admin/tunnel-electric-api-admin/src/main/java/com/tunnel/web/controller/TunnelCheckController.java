package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.domain.TunnelCheck;
import com.tunnel.service.TunnelCheckService;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.List;
import java.util.Objects;

/**
 * 机电设施检测Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/tunnel/electric/check")
public class TunnelCheckController extends BaseController {
    @Autowired
    private TunnelCheckService tunnelCheckService;

    /**
     * 查询机电设施检测列表
     */
    @PreAuthorize("@ss.hasPermi('system:check:list')")
    @GetMapping("/list")
    public TableDataInfo list(TunnelCheck tunnelCheck) {
        startPage();
        tunnelCheck.setGroupBy(1);
        List<TunnelCheck> list = tunnelCheckService.selectTunnelCheckList(tunnelCheck);
        Long total = tunnelCheckService.selectTunnelCheckListCount(tunnelCheck);
        return getDataTable(list, total);
    }

    /**
     * 导出机电设施检测列表
     */
    @PreAuthorize("@ss.hasPermi('system:check:export')")
    @Log(title = "机电设施检测", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TunnelCheck tunnelCheck) throws IOException {
        if(StringUtils.isEmpty(tunnelCheck.getCompanyName()) && StringUtils.isEmpty(tunnelCheck.getRoadName()) && Objects.isNull(tunnelCheck.getTunnelId())){
            throw new ServerException("运营公司,路段名称,隧道名称不能同时为空");
        }
        tunnelCheck.setGroupBy(1);
        tunnelCheck.setExport(1);
        List<TunnelCheck> list = tunnelCheckService.selectTunnelCheckList(tunnelCheck);
        if(CollectionUtils.isEmpty(list)){
            throw new ServerException("未查询到检测数据");
        }
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "隧道机电设施缺陷汇总表";
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
        
        // 创建工作簿和工作表
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("缺陷汇总表");
        
        // 设置标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        titleStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        titleStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        titleStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleStyle.setFont(titleFont);
        
        // 创建标题行
        Row titleRow = sheet.createRow(0);
        // 标题行保留固定高度以保证美观
        titleRow.setHeight((short) 500);
        Cell titleCell = titleRow.createCell(0);
        
        // 获取运营公司和路段信息，实际项目中可能需要从tunnelCheck或其他服务中获取
        String companyName = list.get(0).getCompanyName();
        String sectionName = list.get(0).getSection();
        titleCell.setCellValue(companyName + sectionName + "隧道机电设施缺陷汇总表");
        titleCell.setCellStyle(titleStyle);
        
        // 合并标题单元格 (跨越所有列)
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
        
        // 设置表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setWrapText(true); // 自动换行
        headerStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        
        // 创建表头行
        Row headerRow = sheet.createRow(1);
        // 表头行保留固定高度以保证美观
        headerRow.setHeight((short) 400);
        
        // 定义列标题
        String[] columnHeaders = {"序号", "隧道名称", "分部工程", "分项工程", "缺陷描述", "缺陷详情", "缺陷设备数量", "设备总量", "单位", "修复建议", "备注"};
        
        // 设置每列的宽度（单位：字符数*256）
        int[] columnWidths = {8*256, 15*256, 15*256, 15*256, 25*256, 30*256, 12*256, 12*256, 8*256, 30*256, 15*256};
        
        // 创建表头单元格
        for (int i = 0; i < columnHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columnHeaders[i]);
            cell.setCellStyle(headerStyle);
            sheet.setColumnWidth(i, columnWidths[i]);
        }
        
        // 设置内容样式 - 基本样式
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setWrapText(true); // 自动换行 - 关键设置，使内容可以在单元格内自动换行
        contentStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        contentStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        contentStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        contentStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        
        // 设置内容样式 - 红色文字(用于缺陷信息)
        CellStyle redContentStyle = workbook.createCellStyle();
        redContentStyle.cloneStyleFrom(contentStyle);
        Font redFont = workbook.createFont();
        redFont.setColor(org.apache.poi.ss.usermodel.IndexedColors.RED.getIndex());
        redContentStyle.setFont(redFont);
        
        // 写入数据
        int rowNum = 2; // 从第3行开始(索引为2)
        
        for (int i = 0; i < list.size(); i++) {
            TunnelCheck check = list.get(i);
            Row row = sheet.createRow(rowNum);
            // 不设置固定行高，使用自适应高度
            
            // 设置序号
            createCell(row, 0, String.valueOf(i + 1), contentStyle);
            
            // 设置隧道名称 - 不合并单元格
            createCell(row, 1, check.getTunnelName(), contentStyle);
            
            // 设置分部名称 - 不合并单元格
            createCell(row, 2, check.getPartName(), contentStyle);
            
            // 设置分项名称 - 不合并单元格
            createCell(row, 3, check.getItemName(), contentStyle);
            
            // 设置缺陷描述(红色)
            createCell(row, 4, check.getQuestionDesc(), redContentStyle);
            
            // 设置缺陷详情(红色) - 过滤掉null字符串
            String checkContent = filterNullString(check.getCheckContent());
            if(Objects.equals(check.getQuestionDesc(),"灯具无法点亮")){
                checkContent = check.getRemark();
                check.setRemark("");
            }
            createCell(row, 5, checkContent, redContentStyle);
            
            // 设置缺陷设备数量(红色)
            createCell(row, 6, check.getQuestionNum() != null ? check.getQuestionNum().toString() : "", redContentStyle);
            
            // 设置设备总量
            createCell(row, 7, check.getFacilityNum() != null ? check.getFacilityNum().toString() : "", contentStyle);
            
            // 设置单位
            createCell(row, 8, check.getUnit(), contentStyle);
            
            // 设置修复建议
            createCell(row, 9, check.getSuggestion(), contentStyle);
            
            // 设置备注 - 过滤掉null字符串
            String remark = filterNullString(check.getRemark());
            createCell(row, 10, remark, contentStyle);
            
            rowNum++;
        }
        
        workbook.write(response.getOutputStream());
        workbook.close();
    }
    
    /**
     * 过滤掉字符串中的"null"值
     * @param input 输入字符串
     * @return 过滤后的字符串
     */
    private String filterNullString(String input) {
        if (input == null) {
            return "";
        }
        return input;
    }
    
    private void createCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }

    /**
     * 获取机电设施检测详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:check:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tunnelCheckService.selectTunnelCheckById(id));
    }

    /**
     * 新增机电设施检测
     */
    @PreAuthorize("@ss.hasPermi('system:check:add')")
    @Log(title = "机电设施检测", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TunnelCheck tunnelCheck) {
        return toAjax(tunnelCheckService.insertTunnelCheck(tunnelCheck));
    }

    /**
     * 修改机电设施检测
     */
    @PreAuthorize("@ss.hasPermi('system:check:edit')")
    @Log(title = "机电设施检测", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TunnelCheck tunnelCheck) {
        return toAjax(tunnelCheckService.updateTunnelCheck(tunnelCheck));
    }

    /**
     * 删除机电设施检测
     */
    @PreAuthorize("@ss.hasPermi('system:check:remove')")
    @Log(title = "机电设施检测", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tunnelCheckService.deleteTunnelCheckByIds(ids));
    }


    /**
     * 新增机电设施检测
     */
    @PostMapping("/saveOrUpdateTunnelCheck")
    public AjaxResult saveOrUpdateTunnelCheck(@RequestBody List<TunnelCheck> list) throws IOException {
        tunnelCheckService.saveOrUpdateTunnelCheck(list);
        return AjaxResult.success();
    }

    @ApiOperation(value = "批量导入新增")
    @RequestMapping(value = "/batchImport", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImport(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(tunnelCheckService.batchImport(file));
    }


    @ApiOperation(value = "批量导入新增--车检")
    @RequestMapping(value = "/batchImportCar", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImportCar(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(tunnelCheckService.batchImportCar(file));
    }

    /**
     * 查询机电设施检测明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:check:list')")
    @GetMapping("/detail/list")
    public TableDataInfo detailList(TunnelCheck tunnelCheck) {
        startPage();
        List<TunnelCheck> list = tunnelCheckService.selectTunnelCheckDetailList(tunnelCheck);
        Long total = tunnelCheckService.selectTunnelCheckDetailCount(tunnelCheck);
        return getDataTable(list, total);
    }

    /**
     * 批量复核检测记录
     */
    @PreAuthorize("@ss.hasPermi('system:check:edit')")
    @Log(title = "批量复核检测记录", businessType = BusinessType.UPDATE)
    @PostMapping("/batchReview")
    public AjaxResult batchReview(@RequestBody TunnelCheck tunnelCheck) {
        if (tunnelCheck.getIds() == null || tunnelCheck.getIds().isEmpty()) {
            return AjaxResult.error("请选择要复核的记录");
        }
        
        int result = tunnelCheckService.batchReview(tunnelCheck.getIds(), tunnelCheck.getCheckRemark());
        return AjaxResult.success(result);
    }

    /**
     * 一键复检检测记录
     */
    @PreAuthorize("@ss.hasPermi('system:check:edit')")
    @Log(title = "一键复检检测记录", businessType = BusinessType.INSERT)
    @PostMapping("/oneClickRecheck")
    public AjaxResult oneClickRecheck(@RequestBody TunnelCheck tunnelCheck) {
        if (tunnelCheck.getIds() == null || tunnelCheck.getIds().isEmpty()) {
            return AjaxResult.error("请选择要复检的记录");
        }
        
        int result = tunnelCheckService.oneClickRecheck(tunnelCheck.getIds());
        return AjaxResult.success(result);
    }

}
