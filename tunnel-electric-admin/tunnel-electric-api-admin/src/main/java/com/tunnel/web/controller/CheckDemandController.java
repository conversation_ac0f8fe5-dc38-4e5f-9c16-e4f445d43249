package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckDemand;
import com.tunnel.service.CheckDemandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测内容技术要求Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/tunnel/electric/checkDemand")
public class CheckDemandController extends BaseController {
    @Autowired
    private CheckDemandService checkDemandService;

    /**
     * 查询检测内容技术要求列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CheckDemand checkDemand) {
        startPage();
        List<CheckDemand> list = checkDemandService.selectCheckDemandList(checkDemand);
        return getDataTable(list);
    }

    /**
     * 导出检测内容技术要求列表
     */
    @Log(title = "检测内容技术要求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckDemand checkDemand) {
        List<CheckDemand> list = checkDemandService.selectCheckDemandList(checkDemand);
        ExcelUtil<CheckDemand> util = new ExcelUtil<CheckDemand>(CheckDemand.class);
        util.exportExcel(response, list, "检测内容技术要求数据");
    }

    /**
     * 获取检测内容技术要求详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkDemandService.selectCheckDemandById(id));
    }

    /**
     * 新增检测内容技术要求
     */
    @Log(title = "检测内容技术要求", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckDemand checkDemand) {
        return toAjax(checkDemandService.insertCheckDemand(checkDemand));
    }

    /**
     * 修改检测内容技术要求
     */
    @Log(title = "检测内容技术要求", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckDemand checkDemand) {
        return toAjax(checkDemandService.updateCheckDemand(checkDemand));
    }

    /**
     * 删除检测内容技术要求
     */
    @Log(title = "检测内容技术要求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkDemandService.deleteCheckDemandByIds(ids));
    }
} 