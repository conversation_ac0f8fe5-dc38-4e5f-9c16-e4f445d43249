package com.tunnel.web.controller.common;

import com.tunnel.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * 导入模板下载
 */
@Api("导入模板下载")
@RestController
@Slf4j
@RequestMapping("electric/excel/template")
public class ExcelTemplateDownloadController extends BaseController {

    /*
    下载导入模板
     */
    @ApiOperation(value = "下载导入模板", produces = MediaType.APPLICATION_JSON_VALUE)
    @GetMapping("/download")
    public void downloadFile(HttpServletResponse response, String filePath) throws Exception {
        FileInputStream fis = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource("static/" + filePath);
            InputStream inputStream = classPathResource.getInputStream();
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("", "UTF-8"));
            response.addHeader("Content-Type","application/octet-stream;charset=utf-8");
            try {
                IOUtils.copy(inputStream, response.getOutputStream());
                response.flushBuffer();
            } finally {
                IOUtils.closeQuietly(inputStream);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
