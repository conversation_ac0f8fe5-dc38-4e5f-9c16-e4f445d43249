package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.FacilityInfo;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.service.TunnelInfoService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 隧道信息详情Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/tunnel/electric/tunnelInfo")
@Slf4j
public class TunnelInfoController extends BaseController {
    @Autowired
    private TunnelInfoService tunnelInfoService;
    
    @Autowired
    private CheckEnumRelationController checkEnumRelationController;

    // 创建固定大小的线程池，用于并发处理评分计算
    private final ThreadPoolExecutor scoreCalculationExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(10);

    /**
     * 应用关闭时优雅关闭线程池
     */
    @PreDestroy
    public void destroy() {
        log.info("开始关闭隧道评分计算线程池...");
        scoreCalculationExecutor.shutdown();
        try {
            // 等待60秒让正在执行的任务完成
            if (!scoreCalculationExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                log.warn("线程池在60秒内未能正常关闭，强制关闭...");
                scoreCalculationExecutor.shutdownNow();
                // 再等待30秒
                if (!scoreCalculationExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.error("线程池强制关闭失败");
                }
            }
        } catch (InterruptedException e) {
            log.error("线程池关闭过程中被中断", e);
            scoreCalculationExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("隧道评分计算线程池已关闭");
    }

    /**
     * 查询隧道信息详情列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TunnelInfo tunnelInfo, @RequestParam(required = false) Boolean needScore) {
        startPage();
        List<TunnelInfo> list = tunnelInfoService.selectTunnelInfoList(tunnelInfo);

        // 只有在请求参数中明确指定needScore=true时，才获取评分数据
        if (Boolean.TRUE.equals(needScore)) {
            // 使用多线程并发获取评分数据
            calculateScoresConcurrently(list);
        }
        
        return getDataTable(list);
    }

    /**
     * 使用多线程并发计算隧道评分
     * @param tunnelList 隧道列表
     */
    public void calculateScoresConcurrently(List<TunnelInfo> tunnelList) {
        if (tunnelList == null || tunnelList.isEmpty()) {
            return;
        }

        // 创建StopWatch监控整体性能
        org.springframework.util.StopWatch stopWatch = new org.springframework.util.StopWatch("calculateScoresConcurrently");
        stopWatch.start("并发计算评分");

        // 创建CompletableFuture列表来处理并发任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (TunnelInfo info : tunnelList) {
            if (info == null || info.getId() == null) {
                continue;
            }

            // 为每个隧道创建异步任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 调用评分详情接口
                    AjaxResult scoreResult = checkEnumRelationController.getTunnelScoreDetail(info.getId());
                    if (scoreResult == null) {
                        return;
                    }
                    
                    // 检查结果状态码
                    Object codeObj = scoreResult.get("code");
                    if (codeObj == null || !Integer.valueOf(200).equals(codeObj)) {
                        return;
                    }
                    
                    // 获取数据部分
                    Object dataObj = scoreResult.get("data");
                    if (!(dataObj instanceof Map)) {
                        return;
                    }
                    
                    Map<String, Object> scoreData = (Map<String, Object>) dataObj;
                    
                    // 设置总分
                    if (scoreData.containsKey("totalScore")) {
                        Object totalScoreObj = scoreData.get("totalScore");
                        if (totalScoreObj instanceof BigDecimal) {
                            info.setTotalScore((BigDecimal) totalScoreObj);
                        } else if (totalScoreObj instanceof Number) {
                            info.setTotalScore(new BigDecimal(totalScoreObj.toString()));
                        }
                    }
                    
                    // 设置评定等级
                    if (scoreData.containsKey("category")) {
                        Object categoryObj = scoreData.get("category");
                        if (categoryObj instanceof String) {
                            info.setCategory((String) categoryObj);
                        }
                    }
                    
                    // 设置分部分数
                    if (scoreData.containsKey("partScores")) {
                        Object partScoresObj = scoreData.get("partScores");
                        if (partScoresObj instanceof List) {
                            info.setPartScores((List<Map<String, Object>>) partScoresObj);
                        }
                    }
                    
                    log.debug("隧道[{}]评分计算完成，总分: {}, 等级: {}", 
                        info.getId(), info.getTotalScore(), info.getCategory());
                        
                } catch (Exception e) {
                    log.error("获取隧道[{}]评分详情失败: {}", info.getId(), e.getMessage());
                    // 记录异常但继续处理
                }
            }, scoreCalculationExecutor);

            futures.add(future);
        }

        try {
            // 等待所有任务完成，设置超时时间为60秒
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );
            
            // 等待所有任务完成，最多等待60秒
            allFutures.get(60, TimeUnit.SECONDS);
            
            stopWatch.stop();
            log.info("并发计算{}个隧道评分完成，总耗时: {}ms, 线程池状态: 活跃线程数={}, 队列大小={}", 
                tunnelList.size(), 
                stopWatch.getTotalTimeMillis(),
                scoreCalculationExecutor.getActiveCount(),
                scoreCalculationExecutor.getQueue().size());
                
        } catch (Exception e) {
            stopWatch.stop();
            log.error("并发计算评分时发生异常，已完成耗时: {}ms, 异常信息: {}", 
                stopWatch.getTotalTimeMillis(), e.getMessage());
            
            // 取消未完成的任务
            for (CompletableFuture<Void> future : futures) {
                if (!future.isDone()) {
                    future.cancel(true);
                }
            }
        }
    }

    @GetMapping("/listDistinctRoad")
    public AjaxResult listDistinctRoad(TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.listDistinctRoad(tunnelInfo);
        return AjaxResult.success(list);
    }


    /**
     * 导出隧道信息详情列表
     */
    @Log(title = "隧道信息详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectTunnelInfoList(tunnelInfo);
        ExcelUtil<TunnelInfo> util = new ExcelUtil<TunnelInfo>(TunnelInfo.class);
        util.exportExcel(response, list, "隧道信息详情数据");
    }

    /**
     * 获取隧道信息详情详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tunnelInfoService.selectTunnelInfoById(id));
    }

    /**
     * 新增隧道信息详情
     */
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "隧道信息详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TunnelInfo tunnelInfo) {
        return toAjax(tunnelInfoService.insertTunnelInfo(tunnelInfo));
    }


    @ApiOperation(value = "批量导入新增")
    @RequestMapping(value = "/batchImport", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImport(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(tunnelInfoService.batchImport(file));
    }

    /**
     * 修改隧道信息详情
     */
    @Log(title = "隧道信息详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TunnelInfo tunnelInfo) {
        return toAjax(tunnelInfoService.updateTunnelInfo(tunnelInfo));
    }

    /**
     * 删除隧道信息详情
     */
    @Log(title = "隧道信息详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tunnelInfoService.deleteTunnelInfoByIds(ids));
    }


    /**
     * 查询隧道编码
     */
    @PostMapping("/selectDistinctTunnelCode")
    public AjaxResult selectDistinctTunnelCode(@RequestBody TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctTunnelCode(tunnelInfo);
        return AjaxResult.success(list);
    }


    @PostMapping("/selectDistinctCompany")
    public AjaxResult selectDistinctCompany(@RequestBody TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctCompany(tunnelInfo);
        return AjaxResult.success(list);
    }

    @PostMapping("/selectDistinctRoad")
    public AjaxResult selectDistinctRoad (@RequestBody TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctRoad(tunnelInfo);
        //根据road_name进行去重
        if (list != null && !list.isEmpty()) {
            Map<String, TunnelInfo> uniqueMap = new HashMap<>();
            for (TunnelInfo info : list) {
                // 使用roadName作为键来实现去重
                if (info.getRoadName() != null) {
                    uniqueMap.put(info.getRoadName(), info);
                }
            }
            // 将去重后的结果转回List
            list = new ArrayList<>(uniqueMap.values());
        }
        return AjaxResult.success(list);
    }

    /**
     * 更新隧道复核状态
     */
    @PostMapping("/review")
    public AjaxResult updateReviewStatus(@RequestBody Map<String, Object> params) {
        if(params == null || !params.containsKey("id")){
            throw new ServiceException("id不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        tunnelInfoService.updateReviewStatus(id);
        return AjaxResult.success();
    }

    @PostMapping("/listAll")
    public List<TunnelInfo> listAll(FacilityInfo facilityInfo) {
        List<TunnelInfo> list = tunnelInfoService.listAll();
        return list;
    }

    /**
     * 获取运营公司列表（用于级联下拉框）
     */
    @GetMapping("/getCompanyList")
    public AjaxResult getCompanyList() {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctCompany(new TunnelInfo());
        return AjaxResult.success(list);
    }

    /**
     * 根据运营公司获取路段列表（用于级联下拉框）
     */
    @GetMapping("/getRoadListByCompany")
    public AjaxResult getRoadListByCompany(@RequestParam(required = false) String companyName) {
        TunnelInfo tunnelInfo = new TunnelInfo();
        if (companyName != null && !companyName.trim().isEmpty()) {
            tunnelInfo.setCompanyName(companyName);
        }
        List<TunnelInfo> list = tunnelInfoService.selectDistinctRoad(tunnelInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据运营公司和路段获取线路列表（用于级联下拉框）
     */
    @GetMapping("/getLineListByCompanyAndRoad")
    public AjaxResult getLineListByCompanyAndRoad(@RequestParam(required = false) String companyName, 
                                                @RequestParam(required = false) String roadName) {
        TunnelInfo tunnelInfo = new TunnelInfo();
        if (companyName != null && !companyName.trim().isEmpty()) {
            tunnelInfo.setCompanyName(companyName);
        }
        if (roadName != null && !roadName.trim().isEmpty()) {
            tunnelInfo.setRoadName(roadName);
        }
        List<TunnelInfo> list = tunnelInfoService.selectDistinctRoadCode(tunnelInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据运营公司、路段和线路获取隧道列表（用于级联下拉框）
     */
    @GetMapping("/getTunnelListByParams")
    public AjaxResult getTunnelListByParams(@RequestParam(required = false) String companyName, 
                                          @RequestParam(required = false) String roadName, 
                                          @RequestParam(required = false) String roadCode) {
        TunnelInfo tunnelInfo = new TunnelInfo();
        if (companyName != null && !companyName.trim().isEmpty()) {
            tunnelInfo.setCompanyName(companyName);
        }
        if (roadName != null && !roadName.trim().isEmpty()) {
            tunnelInfo.setRoadName(roadName);
        }
        if (roadCode != null && !roadCode.trim().isEmpty()) {
            tunnelInfo.setRoadCode(roadCode);
        }
        List<TunnelInfo> list = tunnelInfoService.selectDistinctTunnelName(tunnelInfo);
        return AjaxResult.success(list);
    }

}
