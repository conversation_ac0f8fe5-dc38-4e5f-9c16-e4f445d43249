package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckRoadDemand;
import com.tunnel.service.CheckRoadDemandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路段-检测内容技术要求Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/tunnel/electric/checkRoadDemand")
public class CheckRoadDemandController extends BaseController {
    @Autowired
    private CheckRoadDemandService checkRoadDemandService;

    /**
     * 查询路段-检测内容技术要求列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CheckRoadDemand checkRoadDemand) {
        startPage();
        List<CheckRoadDemand> list = checkRoadDemandService.selectCheckRoadDemandList(checkRoadDemand);
        return getDataTable(list);
    }

    /**
     * 导出路段-检测内容技术要求列表
     */
    @Log(title = "路段-检测内容技术要求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckRoadDemand checkRoadDemand) {
        List<CheckRoadDemand> list = checkRoadDemandService.selectCheckRoadDemandList(checkRoadDemand);
        ExcelUtil<CheckRoadDemand> util = new ExcelUtil<CheckRoadDemand>(CheckRoadDemand.class);
        util.exportExcel(response, list, "路段-检测内容技术要求数据");
    }

    /**
     * 获取路段-检测内容技术要求详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkRoadDemandService.selectCheckRoadDemandById(id));
    }

    /**
     * 新增路段-检测内容技术要求
     */
    @Log(title = "路段-检测内容技术要求", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckRoadDemand checkRoadDemand) {
        return toAjax(checkRoadDemandService.insertCheckRoadDemand(checkRoadDemand));
    }

    /**
     * 修改路段-检测内容技术要求
     */
    @Log(title = "路段-检测内容技术要求", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckRoadDemand checkRoadDemand) {
        return toAjax(checkRoadDemandService.updateCheckRoadDemand(checkRoadDemand));
    }

    /**
     * 删除路段-检测内容技术要求
     */
    @Log(title = "路段-检测内容技术要求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkRoadDemandService.deleteCheckRoadDemandByIds(ids));
    }
}