package com.tunnel.web.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckFacilityApply;
import com.tunnel.domain.dto.CheckFacilityApplyQueryDTO;
import com.tunnel.domain.dto.CheckFacilityApplyListDTO;
import com.tunnel.domain.dto.CheckFacilityApplyDetailDTO;
import com.tunnel.domain.dto.CheckFacilityApplyRequestDTO;
import com.tunnel.domain.dto.CheckFacilityApplyApprovalDTO;
import com.tunnel.domain.dto.CheckFacilityApplyApprovalRequestDTO;
import com.tunnel.service.CheckFacilityApplyService;

/**
 * 检测设备申请Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/tunnel/electric/checkFacilityApply")
public class CheckFacilityApplyController extends BaseController {
    @Autowired
    private CheckFacilityApplyService checkFacilityApplyService;

    /**
     * 查询检测设备申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CheckFacilityApplyQueryDTO queryDTO) {
        startPage();
        Long userId = getUserId();
        queryDTO.setLoginUserId(userId);
        List<CheckFacilityApplyListDTO> list = checkFacilityApplyService.selectCheckFacilityApplyList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 导出检测设备申请列表
     */
    @Log(title = "检测设备申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckFacilityApplyQueryDTO queryDTO) {
        Long userId = getUserId();
        queryDTO.setLoginUserId(userId);
        List<CheckFacilityApplyListDTO> list = checkFacilityApplyService.selectCheckFacilityApplyList(queryDTO);
        ExcelUtil<CheckFacilityApplyListDTO> util = new ExcelUtil<CheckFacilityApplyListDTO>(CheckFacilityApplyListDTO.class);
        util.exportExcel(response, list, "检测设备申请数据");
    }

    /**
     * 获取检测设备申请详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        CheckFacilityApplyDetailDTO detailDTO = checkFacilityApplyService.selectCheckFacilityApplyById(id);
        return AjaxResult.success(detailDTO);
    }

    /**
     * 根据任务ID和类型查询申请记录
     */
    @GetMapping("/getByTaskId")
    public AjaxResult getByTaskId(@RequestParam("taskId") Long taskId, @RequestParam("type") Integer type) {
        CheckFacilityApplyDetailDTO detailDTO = checkFacilityApplyService.selectCheckFacilityApplyByTaskIdAndType(taskId, type);
        return AjaxResult.success(detailDTO);
    }

    /**
     * 获取审批详情信息
     */
    @GetMapping("/approval/{id}")
    public AjaxResult getApprovalInfo(@PathVariable("id") Long id) {
        CheckFacilityApplyApprovalDTO approvalDTO = checkFacilityApplyService.selectCheckFacilityApplyApprovalById(id);
        return AjaxResult.success(approvalDTO);
    }

    /**
     * 审批检测设备申请
     */
    @Log(title = "检测设备申请审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approval")
    @PreAuthorize("@ss.hasPermi('electric:checkFacilityApply:approval')")
    public AjaxResult approve(@Valid @RequestBody CheckFacilityApplyApprovalRequestDTO requestDTO) {
        Long userId = getUserId();
        return toAjax(checkFacilityApplyService.approveCheckFacilityApply(requestDTO, userId));
    }

    /**
     * 新增检测设备申请
     */
    @Log(title = "检测设备申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody CheckFacilityApplyRequestDTO requestDTO) {
        Long userId = getUserId();
        return toAjax(checkFacilityApplyService.insertCheckFacilityApply(requestDTO, userId));
    }

    /**
     * 修改检测设备申请
     */
    @Log(title = "检测设备申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody CheckFacilityApplyRequestDTO requestDTO) {
        Long userId = getUserId();
        return toAjax(checkFacilityApplyService.updateCheckFacilityApply(requestDTO, userId));
    }

    /**
     * 删除检测设备申请
     */
    @Log(title = "检测设备申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkFacilityApplyService.deleteCheckFacilityApplyByIds(ids));
    }
}