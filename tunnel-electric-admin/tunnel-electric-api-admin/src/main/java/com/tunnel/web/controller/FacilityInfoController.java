package com.tunnel.web.controller;

import com.google.common.collect.Lists;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.enums.LocationVO;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.*;
import com.tunnel.mapper.CheckEnumMapper;
import com.tunnel.mapper.FacilityInfoMapper;
import com.tunnel.service.FacilityInfoService;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 隧道资产信息详情Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/tunnel/electric/facilityInfo")
public class FacilityInfoController extends BaseController {
    @Autowired
    private FacilityInfoService facilityInfoService;
    @Resource
    private FacilityInfoMapper facilityInfoMapper;
    @Resource
    private CheckEnumMapper checkEnumMapper;

    /**
     * 导出隧道信息详情列表
     */
    @PostMapping("/exportTotalScore")
    public void export(HttpServletResponse response, TunnelInfo tunnelInfo) {
        List<FacilityInfo> list = facilityInfoService.selectListByTunnelId(tunnelInfo.getTunnelId());
        ExcelUtil<FacilityInfo> util = new ExcelUtil<>(FacilityInfo.class);
        util.exportExcel(response, list, "检测结果汇总表");
    }

    /**
     * 查询隧道资产信息详情列表
     */
    @GetMapping("/selectFacilityInfoDetailByPage")
    public List<FacilityInfoDetail> selectFacilityInfoDetailByPage(FacilityInfoDetail facilityInfoDetail) {
        List<FacilityInfoDetail> list = facilityInfoService.selectFacilityInfoDetailByPage(facilityInfoDetail);
        return list;
    }

    /**
     * 查询隧道资产信息详情列表
     */
    @GetMapping("/distinctList")
    public TableDataInfo distinctList(FacilityInfo facilityInfo) {
        startPage();
        List<FacilityInfo> list = facilityInfoService.selectDistinctFacilityInfoList(facilityInfo);
        return getDataTable(list);
    }

    @GetMapping("/list")
    public List<FacilityInfo> list(FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoService.selectFacilityInfoList(facilityInfo);
        return list;
    }

    /**
     * 导出隧道资产信息详情列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "隧道资产信息详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoService.selectFacilityInfoList(facilityInfo);
        ExcelUtil<FacilityInfo> util = new ExcelUtil<FacilityInfo>(FacilityInfo.class);
        util.exportExcel(response, list, "隧道资产信息详情数据");
    }

    /**
     * 导出设备明细数据，支持按筛选条件导出，并排序合并单元格
     */
    @PostMapping("/exportFacilityDetail")
    public void exportFacilityDetail(HttpServletResponse response, FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoService.selectFacilityInfoList(facilityInfo);
        
        // 按隧道名称、分部、分项、设备位置排序
        list.sort((a, b) -> {
            // 先按隧道名称排序
            int tunnelNameCompare = compareNullable(a.getTunnelName(), b.getTunnelName());
            if (tunnelNameCompare != 0) return tunnelNameCompare;
            
            // 再按分部排序
            int partNameCompare = compareNullable(a.getPartName(), b.getPartName());
            if (partNameCompare != 0) return partNameCompare;
            
            // 再按分项排序
            int itemNameCompare = compareNullable(a.getItemName(), b.getItemName());
            if (itemNameCompare != 0) return itemNameCompare;

            // 再按位置名称排序
            int locationNameCompare = compareNullable(a.getLocationName(), b.getLocationName());
            if (locationNameCompare != 0) return locationNameCompare;
            
            // 最后按设备位置排序
            return compareNullable(a.getLocation(), b.getLocation());
        });
        
        try {
            // 创建工作簿
            SXSSFWorkbook wb = new SXSSFWorkbook(500);
            // 创建工作表
            Sheet sheet = wb.createSheet("设备明细数据");
            
            // 创建标题行样式
            CellStyle headerStyle = wb.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setWrapText(true);
            
            Font headerFont = wb.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerStyle.setFont(headerFont);
            
            // 创建数据行样式
            CellStyle dataStyle = wb.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setWrapText(true);
            
            // 定义列名
            String[] headers = {"隧道名称", "分部", "分项", "设备位置", "编码", "设备编码", "设备名称", "设备桩号", "录入类型", "备注"};
            
            // 创建表头行
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 400);
            
            // 设置表头
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 5000);
            }
            
            // 填充数据并实现单元格合并
            String lastTunnelName = null, lastPartName = null, lastItemName = null, lastLocationName = null;
            int tunnelNameStartRow = 1, partNameStartRow = 1, itemNameStartRow = 1, locationNameStartRow = 1;
            
            for (int i = 0; i < list.size(); i++) {
                FacilityInfo item = list.get(i);
                Row dataRow = sheet.createRow(i + 1);
                dataRow.setHeight((short) 400);
                
                // 处理隧道名称列的合并
                if (lastTunnelName != null && !lastTunnelName.equals(item.getTunnelName())) {
                    if (i > tunnelNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(tunnelNameStartRow, i, 0, 0));
                    }
                    tunnelNameStartRow = i + 1;
                }
                
                // 处理分部列的合并
                if (lastPartName != null && (!lastPartName.equals(item.getPartName()) || !lastTunnelName.equals(item.getTunnelName()))) {
                    if (i > partNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(partNameStartRow, i, 1, 1));
                    }
                    partNameStartRow = i + 1;
                }
                
                // 处理分项列的合并
                if (lastItemName != null && (!lastItemName.equals(item.getItemName()) || !lastPartName.equals(item.getPartName()) || !lastTunnelName.equals(item.getTunnelName()))) {
                    if (i > itemNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(itemNameStartRow, i, 2, 2));
                    }
                    itemNameStartRow = i + 1;
                }
                
                // 处理设备位置列的合并
                if (lastLocationName != null && (!lastLocationName.equals(item.getLocationName()) || !lastItemName.equals(item.getItemName()) || !lastPartName.equals(item.getPartName()) || !lastTunnelName.equals(item.getTunnelName()))) {
                    if (i > locationNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(locationNameStartRow, i, 3, 3));
                    }
                    locationNameStartRow = i + 1;
                }
                
                // 填充数据
                Cell cellTunnelName = dataRow.createCell(0);
                cellTunnelName.setCellValue(item.getTunnelName());
                cellTunnelName.setCellStyle(dataStyle);
                
                Cell cellPartName = dataRow.createCell(1);
                cellPartName.setCellValue(item.getPartName());
                cellPartName.setCellStyle(dataStyle);
                
                Cell cellItemName = dataRow.createCell(2);
                cellItemName.setCellValue(item.getItemName());
                cellItemName.setCellStyle(dataStyle);
                
                Cell cellLocationName = dataRow.createCell(3);
                cellLocationName.setCellValue(item.getLocationName());
                cellLocationName.setCellStyle(dataStyle);
                
                Cell cellCode = dataRow.createCell(4);
                cellCode.setCellValue(item.getCode());
                cellCode.setCellStyle(dataStyle);
                
                Cell cellEquipCode = dataRow.createCell(5);
                cellEquipCode.setCellValue(item.getEquipCode());
                cellEquipCode.setCellStyle(dataStyle);
                
                Cell cellName = dataRow.createCell(6);
                cellName.setCellValue(item.getName());
                cellName.setCellStyle(dataStyle);
                
                Cell cellCheckCode = dataRow.createCell(7);
                cellCheckCode.setCellValue(item.getCheckCode());
                cellCheckCode.setCellStyle(dataStyle);
                
                Cell cellType = dataRow.createCell(8);
                cellType.setCellValue(item.getType() != null ? (item.getType() == 1 ? "人工" : item.getType() == 2 ? "自动化" : "") : "");
                cellType.setCellStyle(dataStyle);
                
                Cell cellRemark = dataRow.createCell(9);
                cellRemark.setCellValue(item.getRemark());
                cellRemark.setCellStyle(dataStyle);
                
                // 更新上一行的值，用于比较
                lastTunnelName = item.getTunnelName();
                lastPartName = item.getPartName();
                lastItemName = item.getItemName();
                lastLocationName = item.getLocationName();
                
                // 处理最后一行的合并
                if (i == list.size() - 1) {
                    if (i >= tunnelNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(tunnelNameStartRow, i + 1, 0, 0));
                    }
                    if (i >= partNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(partNameStartRow, i + 1, 1, 1));
                    }
                    if (i >= itemNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(itemNameStartRow, i + 1, 2, 2));
                    }
                    if (i >= locationNameStartRow) {
                        sheet.addMergedRegion(new CellRangeAddress(locationNameStartRow, i + 1, 3, 3));
                    }
                }
            }
            
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "设备明细数据_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            
            wb.write(response.getOutputStream());
            wb.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 比较两个字符串，处理null情况
     */
    private int compareNullable(String str1, String str2) {
        if (str1 == null && str2 == null) return 0;
        if (str1 == null) return -1;
        if (str2 == null) return 1;
        return str1.compareTo(str2);
    }

    /**
     * 获取隧道资产信息详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(facilityInfoService.selectFacilityInfoById(id));
    }

    /**
     * 新增隧道资产信息详情
     */
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "隧道资产信息详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FacilityInfo facilityInfo) {
        return toAjax(facilityInfoService.insertFacilityInfo(facilityInfo));
    }

    /**
     * 修改隧道资产信息详情
     */
    @PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "隧道资产信息详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FacilityInfo facilityInfo) {
        return toAjax(facilityInfoService.updateFacilityInfo(facilityInfo));
    }

    /**
     * 删除隧道资产信息详情
     */
    @PostMapping("/deleteFacilityInfoByList")
    public AjaxResult deleteFacilityInfoByList(@RequestBody List<FacilityInfo> facilityList) {
        return toAjax(facilityInfoService.deleteFacilityInfoByList(facilityList));
    }


    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(facilityInfoService.deleteFacilityInfoByIds(ids));
    }


    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @ApiOperation(value = "批量导入新增")
    @RequestMapping(value = "/batchImport", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImport(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(facilityInfoService.batchImport(file));
    }


    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @ApiOperation(value = "批量导入新增-车检")
    @RequestMapping(value = "/batchImportCar", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchImportCar(@RequestPart(value = "file") MultipartFile file) {
        return AjaxResult.success(facilityInfoService.batchImportCar(file));
    }


//
//    /**
//     * 根据隧道编码,分部code,分项code查询位置
//     */
//    @PostMapping("/selectDistinctLocation")
//    public AjaxResult selectDistinctLocation(@RequestBody FacilityInfo facilityInfo) {
//        List<FacilityInfo> list = facilityInfoMapper.selectDistinctLocation(facilityInfo);
//        return AjaxResult.success(list);
//    }


    /**
     * 根据隧道编码查询分部名称
     */
    @PostMapping("/selectDistinctPart")
    public AjaxResult selectDistinctPart(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectDistinctPart(facilityInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码和分部code查询分项
     */
    @PostMapping("/selectDistinctItem")
    public AjaxResult selectDistinctItem(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectDistinctItem(facilityInfo);
        return AjaxResult.success(list);
    }



    @PostMapping("/selectDistinctName")
    public AjaxResult selectDistinctName(@RequestBody FacilityInfo facilityInfo) {
        FacilityInfo temp = facilityInfoMapper.selectDistinctName(facilityInfo);
        if(Objects.isNull(temp)){
            throw new ServiceException("当前位置没有此隧道的任何设备资产");
        }
        int count = facilityInfoMapper.selectCountDistinctName(facilityInfo);
        temp.setNum(count);
        return AjaxResult.success(temp);
    }


    /**
     * 根据隧道编码,分部code,分项code查询位置
     */
    @PostMapping("/selectDistinctLocation")
    public AjaxResult selectDistinctLocation() {
        List<FacilityInfo> list= Lists.newArrayList();
        for (LocationVO value : LocationVO.values()) {
            FacilityInfo facilityInfo = new FacilityInfo();
            facilityInfo.setLocation(value.getCode());
            facilityInfo.setLocationName(value.getDesc());
            list.add(facilityInfo);
        }
        return AjaxResult.success(list);
    }



    @PostMapping("/selectHoleLocationByTunnelId")
    public AjaxResult selectHoleLocationByTunnelId(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectHoleLocationByTunnelId(facilityInfo);
        return AjaxResult.success(list);
    }


    /**
     * 根据隧道编码,分部code,分项code,位置查询code
     */
    @PostMapping("/selectDistinctCode")
    public AjaxResult selectDistinctCode(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectDistinctCode(facilityInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码,分部code,分项code查询检测内容
     */
    @PostMapping("/selectDistinctCheckContent")
    public AjaxResult selectDistinctCheckContent(@RequestBody CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumMapper.selectDistinctCheckContent(checkEnum);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码,分部code,分项code,检测内容查询缺陷描述
     */
    @PostMapping("/selectDistinctQuestionDesc")
    public AjaxResult selectDistinctQuestionDesc(@RequestBody CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumMapper.selectDistinctQuestionDesc(checkEnum);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码,分部code,分项code,检测内容,缺陷描述查询建议措施
     */
    @PostMapping("/selectByParams")
    public AjaxResult selectByParams(@RequestBody CheckEnum checkEnum) {
        CheckEnum check = checkEnumMapper.selectByParams(checkEnum);
        return AjaxResult.success(check);
    }


    /**
     * 保存或更新资产设备
     * @param facilityInfo
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody @Validated List<FacilityInfo> facilityInfo) {
        facilityInfoService.saveOrUpdate(facilityInfo);
        return AjaxResult.success();
    }

    /**
     * 保存或更新资产设备
     * @param facilityInfo
     * @return
     */
    @PostMapping("/deleteById")
    public AjaxResult deleteById(@RequestBody FacilityInfo facilityInfo) {
        facilityInfoService.deleteFacilityInfoById(facilityInfo.getId());
        return AjaxResult.success();
    }

    /**
     * 查询所有位置的枚举
     * @return
     */
    @PostMapping("/selectAllLocationCode")
    public AjaxResult selectAllLocationCode() {
        //将LocationVO枚举转换为实体集合
        List<Location> codes = Lists.newArrayList();
        for (LocationVO value : LocationVO.values()) {
            Location location = new Location();
            location.setCode(value.getCode());
            location.setDesc(value.getDesc());
            codes.add(location);
        }
        return AjaxResult.success(codes);
    }


    /**
     * 查询隧道资产信息详情列表
     * @param facilityInfo
     * @return
     */
    @PostMapping("/selectFacilityInfoList")
    public AjaxResult selectFacilityInfoList(@RequestBody FacilityInfo facilityInfo) {
        //只能查自己录入的
        facilityInfo.setCreator(SecurityUtils.getUserId());
        List<FacilityInfo> list = facilityInfoService.selectFacilityInfoList(facilityInfo);
        for (FacilityInfo info : list) {
            info.setDirection(LocationVO.getDirectionByCode(info.getLocation()));
        }
        return AjaxResult.success(list);
    }
}
