package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.CheckFacility;
import com.tunnel.domain.Task;
import com.tunnel.domain.TaskRequest;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.mapper.TunnelInfoMapper;
import com.tunnel.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 任务Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/tunnel/electric/task")
public class TaskController extends BaseController {
    @Autowired
    private TaskService taskService;
    @Resource
    private TunnelInfoMapper tunnelInfoMapper;

    /**
     * 查询任务列表
     */
//    @PreAuthorize("@ss.hasPermi('tunnel:electric:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(Task task) {
        startPage();
        Long userId = getUserId();
        task.setLoginUserId(userId);
        List<Task> list = taskService.selectTaskList(task);
        return getDataTable(list);
    }

    /**
     * 导出任务列表
     */
//    @PreAuthorize("@ss.hasPermi('tunnel:electric:task:export')")
    @Log(title = "任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Task task) {
        Long userId = getUserId();
        task.setLoginUserId(userId);
        List<Task> list = taskService.selectTaskList(task);
        ExcelUtil<Task> util = new ExcelUtil<Task>(Task.class);
        util.exportExcel(response, list, "任务数据");
    }

    /**
     * 获取任务详细信息
     */
//    @PreAuthorize("@ss.hasPermi('tunnel:electric:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(taskService.selectTaskById(id));
    }

    /**
     * 新增任务
     */
//    @PreAuthorize("@ss.hasPermi('tunnel:electric:task:add')")
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody TaskRequest taskRequest) {
        Task task = new Task();
        task.setRoadName(taskRequest.getRoadName());
        task.setTunnelIds(taskRequest.getTunnelIds());
        task.setRemark(taskRequest.getRemark());
        task.setCreator(getUserId());

        return toAjax(taskService.insertTask(task, taskRequest.getTaskUsers()));
    }

    /**
     * 修改任务
     */
//    @PreAuthorize("@ss.hasPermi('tunnel:electric:task:edit')")
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody TaskRequest taskRequest) {
        Task task = new Task();
        task.setId(taskRequest.getId());
        // 修改时只允许修改备注和状态，不允许修改路段和隧道
        task.setStatus(taskRequest.getStatus());
        task.setRemark(taskRequest.getRemark());
        task.setModifier(getUserId());

        return toAjax(taskService.updateTask(task, taskRequest.getTaskUsers()));
    }

    /**
     * 删除任务
     */
//    @PreAuthorize("@ss.hasPermi('tunnel:electric:task:remove')")
    @Log(title = "任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskService.deleteTaskByIds(ids));
    }

    /**
     * 查询路段列表
     */
    @GetMapping("/roads")
    public AjaxResult getRoadList() {
        List<TunnelInfo> list = taskService.selectRoadList();
        return AjaxResult.success(list);
    }

    /**
     * 根据路段编号查询隧道列表
     */
    @GetMapping("/tunnels/{roadName}")
    public AjaxResult getTunnelListByRoadName(@PathVariable String roadName) {
        List<TunnelInfo> list = taskService.selectTunnelListByRoadCode(roadName);
        return AjaxResult.success(list);
    }

    /**
     * 查询用户列表
     */
    @GetMapping("/users")
    public AjaxResult getUserList() {
        List<SysUser> list = taskService.selectUserList();
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道ID查询隧道详情
     */
    @GetMapping("/tunnelDetails/{tunnelIds}")
    public AjaxResult getTunnelDetails(@PathVariable String tunnelIds) {
        List<TunnelInfo> list = taskService.selectTunnelDetailsByIds(tunnelIds);
        return AjaxResult.success(list);
    }

    /**
     * 获取出库设备列表
     */
    @GetMapping("/selectFacilities")
    public AjaxResult selectFacilities() {
        List<CheckFacility> list = taskService.selectFacilities();
        return AjaxResult.success(list);
    }

    /**
     * 获取入库设备列表
     */
    @GetMapping("/facilities/in/{taskId}")
    public AjaxResult getInFacilities(@PathVariable Long taskId) {
        List<CheckFacility> list = taskService.selectInFacilities(taskId);
        return AjaxResult.success(list);
    }

    /**
     * 获取设备状态列表
     */
    @GetMapping("/facilityStatus/{taskId}")
    public AjaxResult getFacilityStatusList(@PathVariable Long taskId) {
        return AjaxResult.success(taskService.selectFacilityStatusList(taskId));
    }

    /**
     * 更新设备记录
     */
    @PutMapping("/facilityRecord")
    public AjaxResult updateFacilityRecord(@RequestBody Map<String, Object> recordData) {
        return toAjax(taskService.updateFacilityRecord(recordData, getUserId()));
    }

    /**
     * 复制任务
     */
    @Log(title = "任务复制", businessType = BusinessType.INSERT)
    @PostMapping("/duplicate")
    public AjaxResult duplicateTask(@RequestBody Map<String, Object> duplicateData) {
        Long originalTaskId = Long.valueOf(duplicateData.get("originalTaskId").toString());
        String roadName = duplicateData.get("roadName").toString();
        String tunnelIds = duplicateData.get("tunnelIds").toString();
        String remark = duplicateData.get("remark").toString();
        
        return toAjax(taskService.duplicateTask(originalTaskId, roadName, tunnelIds, remark, getUserId()));
    }
}