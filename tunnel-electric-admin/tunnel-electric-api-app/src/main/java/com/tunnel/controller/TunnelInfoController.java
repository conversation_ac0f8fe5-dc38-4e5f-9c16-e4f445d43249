package com.tunnel.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.CheckFacilityRecord;
import com.tunnel.domain.TunnelCheck;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.mapper.TunnelCheckMapper;
import com.tunnel.service.TunnelInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 隧道信息详情Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/app/electric/tunnelInfo")
public class TunnelInfoController extends BaseController {

    @Autowired
    private TunnelInfoService tunnelInfoService;
    @Resource
    private TunnelCheckMapper tunnelCheckMapper;


    /**
     * 查询隧道总列表
     */
    @PostMapping("/selectTunnelByPage")
    public AjaxResult selectTunnelByPage(@RequestBody TunnelInfo tunnel) {
        startPage();
        List<TunnelInfo> list = tunnelInfoService.selectDistinctTunnelList(tunnel);
        return AjaxResult.success(list);
    }

    /**
     * 查询任务列表列表
     */
    @PostMapping("/tunnelInfoList")
    public AjaxResult tunnelInfoList(@RequestBody TunnelInfo tunnelInfo) {
        startPage();
        List<TunnelInfo> list = tunnelInfoService.selectTunnelInfoList(tunnelInfo);
        return AjaxResult.success(list);
    }


    /**
     * 查询隧道编码
     */
    @PostMapping("/selectDistinctTunnelCode")
    public AjaxResult selectDistinctTunnelCode(@RequestBody TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctTunnelCode(tunnelInfo);
        return AjaxResult.success(list);
    }

    @PostMapping("/updateTunnelInfoPic")
    public AjaxResult updateTunnelInfoPic(@RequestBody TunnelInfo tunnelInfo) throws IOException {
        TunnelInfo tunnel = tunnelInfoService.updateTunnelInfoPic(tunnelInfo);
        return AjaxResult.success(tunnel);
    }

    /**
     * 更新隧道检测时间
     */
    @PostMapping("/updateTunnelInfoTime")
    public AjaxResult updateTunnelInfoTime(@RequestBody TunnelInfo tunnelInfo) {
        // 直接使用前端传递的时间进行更新
        tunnelInfoService.updateTunnelInfo(tunnelInfo);
        TunnelInfo updatedTunnel = tunnelInfoService.selectTunnelInfoById(tunnelInfo.getId());
        return AjaxResult.success(updatedTunnel);
    }

    /**
     * 查询运营公司
     */
    @PostMapping("/selectDistinctCompany")
    public AjaxResult selectDistinctCompany(@RequestBody TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctCompany(tunnelInfo);
        return AjaxResult.success(list);
    }

    /**
     * 查询线路
     */
    @PostMapping("/selectDistinctRoad")
    public AjaxResult selectDistinctRoad(@RequestBody TunnelInfo tunnelInfo) {
        List<TunnelInfo> list = tunnelInfoService.selectDistinctRoad(tunnelInfo);
        return AjaxResult.success(list);
    }

    /**
     * 查询线路
     */
    @PostMapping("/updateTunnelInfoCompleteStatus")
    public AjaxResult updateTunnelInfoCompleteStatus(@RequestBody TunnelInfo tunnelInfo) {
        tunnelInfoService.updateTunnelInfoCompleteStatus(tunnelInfo);
        return AjaxResult.success();
    }
    
    /**
     * 获取当前用户对应任务的检测设备
     */
    @PostMapping("/getCheckFacilitiesByUser")
    public AjaxResult getCheckFacilitiesByUser(@RequestBody Map<String, Object> params) {
        Long tunnelId = Long.valueOf(params.get("tunnelId").toString());
        Long userId = SecurityUtils.getUserId();
        List<CheckFacilityRecord> facilities = tunnelInfoService.getCheckFacilitiesByUser(tunnelId, userId);
        return AjaxResult.success(facilities);
    }


    /**
     * 获取当前用户对应任务的检测设备
     */
    @PostMapping("/getLastTunnelCheckByTunnelId")
    public AjaxResult getLastTunnelCheckByTunnelId(@RequestBody TunnelInfo tunnelInfo) {
        Long tunnelId = tunnelInfo.getTunnelId();
        TunnelCheck facilities = tunnelCheckMapper.getLastTunnelCheckByTunnelId(tunnelId);
        return AjaxResult.success(facilities);
    }

}
