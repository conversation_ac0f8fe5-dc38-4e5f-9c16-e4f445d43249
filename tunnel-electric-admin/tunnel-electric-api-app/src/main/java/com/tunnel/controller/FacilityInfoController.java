package com.tunnel.controller;

import com.alibaba.fastjson2.JSON;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.enums.LocationVO;
import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.domain.CheckEnum;
import com.tunnel.domain.FacilityInfo;
import com.tunnel.domain.Location;
import com.tunnel.mapper.CheckEnumMapper;
import com.tunnel.mapper.FacilityInfoMapper;
import com.tunnel.service.FacilityInfoService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 隧道资产信息详情Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/app/electric/facilityInfo")
public class FacilityInfoController extends BaseController {

    @Autowired
    private FacilityInfoService facilityInfoService;

    @Autowired
    private FacilityInfoMapper facilityInfoMapper;

    @Resource
    private CheckEnumMapper checkEnumMapper;

    /**
     * 根据隧道编码查询分部名称
     */
    @PostMapping("/selectDistinctPart")
    public AjaxResult selectDistinctPart(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectDistinctPart(facilityInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码和分部code查询分项
     */
    @PostMapping("/selectDistinctItem")
    public AjaxResult selectDistinctItem(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectDistinctItem(facilityInfo);
        return AjaxResult.success(list);
    }



    @PostMapping("/selectDistinctName")
    public AjaxResult selectDistinctName(@RequestBody FacilityInfo facilityInfo) {
        FacilityInfo temp = facilityInfoMapper.selectDistinctName(facilityInfo);
        if(Objects.isNull(temp)){
            throw new ServiceException("当前位置没有此隧道的任何设备资产");
        }
        int count = facilityInfoMapper.selectCountDistinctName(facilityInfo);
        temp.setNum(count);
        return AjaxResult.success(temp);
    }


    /**
     * 根据隧道编码,分部code,分项code查询位置
     */
    @PostMapping("/selectDistinctLocation")
    public AjaxResult selectDistinctLocation() {
        List<FacilityInfo> list=Lists.newArrayList();
        for (LocationVO value : LocationVO.values()) {
            FacilityInfo facilityInfo = new FacilityInfo();
            facilityInfo.setLocation(value.getCode());
            facilityInfo.setLocationName(value.getDesc());
            list.add(facilityInfo);
        }
        return AjaxResult.success(list);
    }



    @PostMapping("/selectHoleLocationByTunnelId")
    public AjaxResult selectHoleLocationByTunnelId(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectHoleLocationByTunnelId(facilityInfo);
        return AjaxResult.success(list);
    }


    /**
     * 根据隧道编码,分部code,分项code,位置查询code
     */
    @PostMapping("/selectDistinctCode")
    public AjaxResult selectDistinctCode(@RequestBody FacilityInfo facilityInfo) {
        List<FacilityInfo> list = facilityInfoMapper.selectDistinctCode(facilityInfo);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码,分部code,分项code查询检测内容
     */
    @PostMapping("/selectDistinctCheckContent")
    public AjaxResult selectDistinctCheckContent(@RequestBody CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumMapper.selectDistinctCheckContent(checkEnum);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码,分部code,分项code,检测内容查询缺陷描述
     */
    @PostMapping("/selectDistinctQuestionDesc")
    public AjaxResult selectDistinctQuestionDesc(@RequestBody CheckEnum checkEnum) {
        List<CheckEnum> list = checkEnumMapper.selectDistinctQuestionDesc(checkEnum);
        return AjaxResult.success(list);
    }

    /**
     * 根据隧道编码,分部code,分项code,检测内容,缺陷描述查询建议措施
     */
    @PostMapping("/selectByParams")
    public AjaxResult selectByParams(@RequestBody CheckEnum checkEnum) {
        CheckEnum check = checkEnumMapper.selectByParams(checkEnum);
        return AjaxResult.success(check);
    }


    /**
     * 保存或更新资产设备
     * @param facilityInfo
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody @Validated List<FacilityInfo> facilityInfo) {
        facilityInfoService.saveOrUpdate(facilityInfo);
        return AjaxResult.success();
    }

    /**
     * 保存或更新资产设备
     * @param facilityInfo
     * @return
     */
    @PostMapping("/deleteById")
    public AjaxResult deleteById(@RequestBody FacilityInfo facilityInfo) {
        facilityInfoService.deleteFacilityInfoById(facilityInfo.getId());
        return AjaxResult.success();
    }

    /**
     * 查询所有位置的枚举
     * @return
     */
    @PostMapping("/selectAllLocationCode")
    public AjaxResult selectAllLocationCode() {
        //将LocationVO枚举转换为实体集合
        List<Location> codes = Lists.newArrayList();
        for (LocationVO value : LocationVO.values()) {
            Location location = new Location();
            location.setCode(value.getCode());
            location.setDesc(value.getDesc());
            codes.add(location);
        }
        return AjaxResult.success(codes);
    }


    /**
     * 查询隧道资产信息详情列表
     * @param facilityInfo
     * @return
     */
    @PostMapping("/selectFacilityInfoList")
    public AjaxResult selectFacilityInfoList(@RequestBody FacilityInfo facilityInfo) {
        //只能查自己录入的
        facilityInfo.setCreator(SecurityUtils.getUserId());
        List<FacilityInfo> list = facilityInfoService.selectFacilityInfoList(facilityInfo);
        for (FacilityInfo info : list) {
            info.setDirection(LocationVO.getDirectionByCode(info.getLocation()));
        }
        return AjaxResult.success(list);
    }


    /**
     * 在检测录入界面新增
     * @param facilityInfo
     * @return
     */
    @PostMapping("/addOrUpdateByTunnelCheck")
    public AjaxResult addOrUpdateByTunnelCheck(@RequestBody FacilityInfo facilityInfo) {
        facilityInfoService.addOrUpdateByTunnelCheck(facilityInfo);
        return AjaxResult.success();
    }

}
