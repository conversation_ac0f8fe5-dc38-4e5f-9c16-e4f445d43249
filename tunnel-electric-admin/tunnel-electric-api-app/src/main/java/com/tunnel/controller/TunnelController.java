package com.tunnel.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.Tunnel;
import com.tunnel.service.TunnelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 隧道总Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/app/electric/tunnel")
public class TunnelController extends BaseController {
    @Autowired
    private TunnelService tunnelService;


    /**
     * 查询隧道总列表
     */
    @PostMapping("/tunnelList")
    public AjaxResult tunnelList(@RequestBody Tunnel tunnel) {
        List<Tunnel> list = tunnelService.selectTunnelList(tunnel);
        return AjaxResult.success(list);
    }

}
