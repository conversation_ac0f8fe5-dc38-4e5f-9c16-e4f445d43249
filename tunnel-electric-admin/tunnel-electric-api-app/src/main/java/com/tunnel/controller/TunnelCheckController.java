package com.tunnel.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.domain.Tunnel;
import com.tunnel.domain.TunnelCheck;
import com.tunnel.domain.TunnelInfo;
import com.tunnel.dto.BatchCheckDTO;
import com.tunnel.mapper.TunnelCheckMapper;
import com.tunnel.service.TunnelCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 机电设施检测Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/app/electric/check")
public class TunnelCheckController extends BaseController {
    @Autowired
    private TunnelCheckService tunnelCheckService;
    @Autowired
    private TunnelCheckMapper tunnelCheckMapper;

    /**
     * 查询机电设施检测列表
     */
    @PostMapping("/checkList")
    public AjaxResult checkList(@RequestBody TunnelInfo tunnelInfo) {
        TunnelInfo list = tunnelCheckService.checkList(tunnelInfo);
        return AjaxResult.success(list);
    }


    /**
     * 新增机电设施检测
     */
    @PostMapping("/saveOrUpdateTunnelCheck")
    public AjaxResult saveOrUpdateTunnelCheck(@RequestBody BatchCheckDTO dto) throws IOException {
        if(CollectionUtils.isEmpty(dto.getCheckList())){
            throw new RuntimeException("请求参数不能为空");
        }
        for (TunnelCheck temp : dto.getCheckList()) {
            temp.setPicUrl1(dto.getPicUrl1());
            temp.setPicUrl2(dto.getPicUrl2());
            temp.setPicUrl3(dto.getPicUrl3());
        }
        tunnelCheckService.saveOrUpdateTunnelCheck(dto.getCheckList());
        return AjaxResult.success();
    }


    /**
     * 查询任务列表列表
     */
    @PostMapping("/checkListByPage")
    public AjaxResult checkListByPage(@RequestBody TunnelCheck tunnelCheck) {
        startPage();
        List<TunnelCheck> list = tunnelCheckService.checkListByPage(tunnelCheck);
        return AjaxResult.success(list);
    }


    /**
     * 删除机电设施检测
     */
    @PostMapping("/deleteCheckById")
    public AjaxResult deleteCheckById(@RequestBody TunnelCheck tunnelCheck) {
        if(CollectionUtils.isEmpty(tunnelCheck.getIds())){
            throw new RuntimeException("请选择删除项");
        }
        tunnelCheckService.deleteCheckByIds(tunnelCheck.getIds());
        return AjaxResult.success();
    }


    /**
     * 删除机电设施检测
     */
    @PostMapping("/updateSuggestionById")
    public AjaxResult updateSuggestionById(@RequestBody TunnelCheck tunnelCheck) {
        if(Objects.isNull(tunnelCheck.getId())){
            throw new RuntimeException("请选择更新项");
        }
        tunnelCheckMapper.updateSuggestionById(tunnelCheck);
        return AjaxResult.success();
    }

}
