package com.tunnel.controller;

import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.domain.CheckStandard;
import com.tunnel.service.CheckStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 规范列表Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/app/checkStandard")
public class CheckStandardController extends BaseController {
    @Autowired
    private CheckStandardService checkStandardService;

    /**
     * 查询规范列表列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CheckStandard checkStandard) {
        startPage();
        List<CheckStandard> list = checkStandardService.selectCheckStandardList(checkStandard);
        return getDataTable(list);
    }


    /**
     * 获取规范列表详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkStandardService.selectCheckStandardById(id));
    }

}