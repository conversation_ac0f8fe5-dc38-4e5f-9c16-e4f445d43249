<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="tunnel-electric-common" />
        <module name="tunnel-electric-generator" />
        <module name="tunnel-electric-platform" />
        <module name="tunnel-electric-system" />
        <module name="tunnel-electric-api-app" />
        <module name="tunnel-electric-api-admin" />
        <module name="tunnel-electric-framework" />
        <module name="tunnel-electric-quartz" />
      </profile>
    </annotationProcessing>
  </component>
</project>