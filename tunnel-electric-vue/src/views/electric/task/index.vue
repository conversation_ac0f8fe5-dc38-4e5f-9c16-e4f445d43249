<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch">
      <el-form-item label="路段名称" prop="roadName">
        <el-input
          v-model="queryParams.roadName"
          placeholder="请输入路段名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="任务状态" clearable>
          <el-option label="已派发" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已完成" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery">搜索</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['electric:task:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['electric:task:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['electric:task:remove']"
        >删除</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" :index="indexMethod" width="60"/>
      <el-table-column label="运营公司" align="center" width="200">
        <template v-slot:default="scope">
          <span>隧道运营公司</span>
        </template>
      </el-table-column>
      <el-table-column label="所属路段" align="center" prop="roadName" width="200"/>
      <el-table-column label="路线编号" align="center" prop="roadCode" width="200"/>
      <el-table-column label="任务状态" align="center" prop="status" width="200">
        <template v-slot:default="scope">
          <el-tag v-if="scope.row.status === 0" type="warning">已派发</el-tag>
          <el-tag v-else-if="scope.row.status === 1" type="primary">进行中</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="success">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" width="200"/>
      <el-table-column label="创建人" align="center" prop="creatorName" width="100"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template v-slot:default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width" fixed="right">
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['electric:task:detail']"
          >详情</el-button>
          <el-button
            size="mini"
            type="info"
            @click="handleFacilityStatus(scope.row)"
            v-hasPermi="['electric:task:facilityStatus']"
          >设备状态</el-button>
          <el-button
            v-if="scope.row.status == 1"
            size="mini"
            type="warning"
            @click="handleDuplicateTask(scope.row)"
            v-hasPermi="['electric:task:add']"
          >复制任务</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改任务对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" destroy-on-close>
      <el-form ref="form" :model="form" :rules="dynamicRules" label-width="120px">
        <el-form-item label="路段" prop="roadName">
          <el-select v-model="form.roadName" placeholder="请选择路段" @change="handleRoadChange" :disabled="!!form.id">
            <el-option
              v-for="road in roadList"
              :key="road.roadCode"
              :label="road.roadName"
              :value="road.roadName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="隧道" prop="tunnelIds">
          <el-select v-model="form.tunnelIds" placeholder="请选择隧道" multiple :disabled="!!form.id">
            <el-option
              v-for="tunnel in tunnelList"
              :key="tunnel.tunnelId"
              :label="tunnel.tunnelName"
              :value="tunnel.tunnelId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态" prop="status" v-if="form.id" >
          <el-select v-model="form.status" placeholder="请选择任务状态" :disabled="!!form.id">
            <el-option label="已派发" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="人员配置" prop="taskUsers">
          <el-table :data="form.taskUsers" style="width: 100%">
            <el-table-column label="人员" width="200">
              <template v-slot:default="scope">
                <!-- 始终显示选择框，允许修改人员 -->
                <el-select v-model="scope.row.userId" placeholder="请选择人员">
                  <el-option
                    v-for="user in userList"
                    :key="user.userId"
                    :label="user.nickName"
                    :value="user.userId"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="职责" width="200">
              <template v-slot:default="scope">
                <el-select v-model="scope.row.title" placeholder="请选择职责">
                  <el-option label="记录" value="记录" />
                  <el-option label="复核" value="复核" />
                  <el-option label="检测" value="检测" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="300">
              <template v-slot:default="scope">
                <el-button size="mini" type="danger" @click="removeTaskUser(scope.$index)">删除</el-button>
                <el-button 
                  v-if="scope.$index === form.taskUsers.length - 1" 
                  size="mini" 
                  type="primary" 
                  @click="addTaskUser"
                  style="margin-left: 5px;">
                  添加
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 只有当没有人员配置时才显示添加按钮 -->
          <div v-if="!form.taskUsers || form.taskUsers.length === 0" style="margin-top: 10px">
            <el-button type="primary" size="mini" @click="addTaskUser">添加人员</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 隧道详情弹窗 -->
    <el-dialog title="隧道详情" v-model="detailOpen" width="60%" destroy-on-close>
      <!-- 申请状态展示 -->
      <div v-if="applyStatusInfo.outApply || applyStatusInfo.inApply" style="margin-bottom: 15px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h4 style="margin: 0 0 10px 0;">申请状态</h4>
        <el-row :gutter="20">
          <el-col :span="12" v-if="applyStatusInfo.outApply">
            <span><strong>出库申请：</strong></span>
            <el-tag :type="applyStatusInfo.outApply.status === 0 ? 'warning' : applyStatusInfo.outApply.status === 1 ? 'success' : 'danger'">
              {{ getStatusText(applyStatusInfo.outApply.status) }}
            </el-tag>
          </el-col>
          <el-col :span="12" v-if="applyStatusInfo.inApply">
            <span><strong>入库申请：</strong></span>
            <el-tag :type="applyStatusInfo.inApply.status === 0 ? 'warning' : applyStatusInfo.inApply.status === 1 ? 'success' : 'danger'">
              {{ getStatusText(applyStatusInfo.inApply.status) }}
            </el-tag>
          </el-col>
        </el-row>
      </div>
      
      <!-- 设备申请按钮 -->
      <div style="margin-bottom: 15px;">
        <el-button type="primary" size="small" @click="handleFacilityApply('out')">申请设备出库</el-button>
        <el-button 
          type="success" 
          size="small" 
          @click="handleFacilityApply('in')"
          :disabled="!canApplyIn">
          申请设备入库
        </el-button>
        <span v-if="!canApplyIn" style="color: #909399; font-size: 12px; margin-left: 10px;">
          (需要出库申请通过后才能申请入库)
        </span>
      </div>
      
      <el-table :data="tunnelDetailList" style="width: 100%">
        <el-table-column label="序号" type="index" width="60" :index="tunnelIndexMethod"/>
        <el-table-column label="运营公司" align="center" prop="companyName" width="150">
          <template v-slot:default="scope">
            <span>{{ scope.row.companyName || '隧道运营公司' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属路段" align="center" prop="roadName" />
        <el-table-column label="路段编号" align="center" prop="roadCode" />
        <el-table-column label="隧道名称" align="center" prop="tunnelName" />
        <el-table-column label="隧道类型" align="center" prop="tunnelType" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 设备列表弹窗 -->
    <el-dialog :title="facilityDialogTitle" v-model="facilityDialogOpen" width="60%" destroy-on-close>
      <!-- 申请信息展示（只读模式） -->
      <div v-if="!isEditMode && currentApplyRecord" style="margin-bottom: 15px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <span><strong>申请状态：</strong></span>
            <el-tag :type="currentApplyRecord.status === 0 ? 'warning' : currentApplyRecord.status === 1 ? 'success' : 'danger'">
              {{ getStatusText(currentApplyRecord.status) }}
            </el-tag>
          </el-col>
          <el-col :span="12">
            <span><strong>申请类型：</strong>{{ currentApplyRecord.type === 1 ? '出库' : '入库' }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">
          <el-col :span="12">
            <span><strong>创建时间：</strong>{{ currentApplyRecord.createTime }}</span>
          </el-col>
          <el-col :span="12">
            <span><strong>设备数量：</strong>{{ currentApplyRecord.facilityCount || 0 }}台</span>
          </el-col>
        </el-row>
        <el-row v-if="currentApplyRecord.remark" style="margin-top: 10px;">
          <el-col :span="24">
            <span><strong>备注：</strong>{{ currentApplyRecord.remark }}</span>
          </el-col>
        </el-row>
      </div>
      
      <div style="height: 500px; overflow-y: auto;">
        <el-table ref="facilityTable" :data="facilityList" style="width: 100%" @selection-change="handleFacilitySelectionChange">
          <el-table-column v-if="isEditMode || !currentApplyRecord" type="selection" width="55" align="center" />
          <el-table-column label="序号" type="index" width="60" :index="facilityIndexMethod"/>
          <el-table-column label="设备名称" align="center" prop="name" />
          <el-table-column label="设备型号" align="center" prop="model" />
          <el-table-column label="是否在库" align="center" prop="flag" width="100">
            <template v-slot:default="scope">
              <el-tag v-if="scope.row.flag === 1" type="success">是</el-tag>
              <el-tag v-else type="info">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column v-if="!isEditMode && currentApplyRecord" label="已选择" align="center" width="100">
            <template v-slot:default="scope">
              <el-tag v-if="isSelectedFacility(scope.row.id)" type="primary">已选择</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="isEditMode || !currentApplyRecord" type="primary" @click="submitFacilityApply">{{ isEditMode ? '修 改' : '确 定' }}</el-button>
        <el-button @click="closeFacilityDialog">{{ isEditMode || !currentApplyRecord ? '取 消' : '关 闭' }}</el-button>
      </div>
    </el-dialog>

    <!-- 设备运行详情弹窗 -->
    <el-dialog title="设备运行详情" v-model="facilityStatusDialogOpen" width="65%" destroy-on-close>
      <div style="height: 500px; overflow-y: auto;">
        <el-table :data="facilityStatusList" style="width: 100%">
          <el-table-column label="序号" type="index" width="60" :index="facilityStatusIndexMethod"/>
          <el-table-column label="设备名称" align="center" prop="facilityName" width="200"/>
          <el-table-column label="设备型号" align="center" prop="facilityModel" width="150"/>
          <el-table-column label="申请类型" align="center" prop="facilityType" width="100" >
            <template v-slot:default="scope">
              <el-tag type="primary" size="small" effect="plain">{{ formatApplyType(scope.row.applyType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="实验前" align="center" width="120">
            <template v-slot:default="scope">
              <el-select v-model="scope.row.beforeStatus" placeholder="请选择" size="mini" @change="updateFacilityRecord(scope.row)">
                <el-option label="正常" :value="1" />
                <el-option label="异常" :value="2" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="实验中" align="center" width="120">
            <template v-slot:default="scope">
              <el-select v-model="scope.row.useStatus" placeholder="请选择" size="mini" @change="updateFacilityRecord(scope.row)">
                <el-option label="正常" :value="1" />
                <el-option label="异常" :value="2" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="实验后" align="center" width="120">
            <template v-slot:default="scope">
              <el-select v-model="scope.row.afterStatus" placeholder="请选择" size="mini" @change="updateFacilityRecord(scope.row)">
                <el-option label="正常" :value="1" />
                <el-option label="异常" :value="2" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center">
            <template v-slot:default="scope">
              <el-input v-model="scope.row.remark" placeholder="请输入备注" size="mini" @change="updateFacilityRecord(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 复制任务对话框 -->
    <el-dialog title="复制任务" v-model="duplicateDialogOpen" width="50%" destroy-on-close>
      <el-form ref="duplicateForm" :model="duplicateForm" :rules="duplicateRules" label-width="120px">
        <el-form-item label="原任务信息" style="margin-bottom: 20px;">
          <div style="background-color: #f5f7fa; padding: 15px; border-radius: 4px;">
            <p><strong>路段：</strong>{{ originalTask.roadName }}</p>
            <p><strong>隧道：</strong>{{ originalTask.tunnelNames }}</p>
            <p><strong>备注：</strong>{{ originalTask.remark }}</p>
          </div>
        </el-form-item>
        <el-form-item label="路段" prop="roadName">
          <el-select v-model="duplicateForm.roadName" placeholder="请选择路段" @change="copyHandleRoadChange" :disabled="!!duplicateForm.id">
            <el-option
                v-for="road in roadList"
                :key="road.roadCode"
                :label="road.roadName"
                :value="road.roadName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="隧道" prop="tunnelIds">
          <el-select v-model="duplicateForm.tunnelIds" placeholder="请选择隧道" multiple :disabled="!!duplicateForm.id">
            <el-option
                v-for="tunnel in tunnelList"
                :key="tunnel.tunnelId"
                :label="tunnel.tunnelName"
                :value="tunnel.tunnelId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="duplicateForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDuplicateTask">确 定</el-button>
        <el-button @click="cancelDuplicateTask">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask, listRoad, listTunnelByRoadName, listUser, getTunnelDetails, selectFacilities, getInFacilities, getFacilityStatusList, updateFacilityRecord, duplicateTask } from "@/api/electric/task";
import { getCheckFacilityApplyByTaskId, addCheckFacilityApply, updateCheckFacilityApply } from "@/api/electric/checkFacilityApply";
import { getCompanyList, getRoadListByCompany, getTunnelListByParams } from "@/api/electric/tunnelInfo";

export default {
  name: "Task",
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      taskList: [],
      roadList: [],
      tunnelList: [],
      userList: [],
      title: "",
      open: false,
      detailOpen: false,
      tunnelDetailList: [],
      facilityDialogOpen: false,
      facilityDialogTitle: "",
      facilityList: [],
      selectedFacilities: [],
      currentTaskId: null,
      currentApplyType: null,
      currentApplyRecord: null, // 存储当前申请记录
      isEditMode: false, // 标识是否为编辑模式
      applyStatusInfo: { // 申请状态信息
        outApply: null, // 出库申请
        inApply: null   // 入库申请
      },
      facilityStatusDialogOpen: false, // 设备状态弹窗
      facilityStatusList: [], // 设备状态列表
      currentTaskForStatus: null, // 当前查看状态的任务
      // 复制任务相关
      duplicateDialogOpen: false,
      originalTask: {},
      companyList: [],
      duplicateRoadList: [],
      duplicateTunnelList: [],
      duplicateForm: {
        companyName: null,
        roadName: null,
        tunnelIds: [],
        remark: null
      },
      duplicateRules: {
        roadName: [
          { required: true, message: "路段不能为空", trigger: "change" }
        ],
        tunnelIds: [
          { required: true, message: "隧道不能为空", trigger: "change" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ]
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roadName: null,
        status: null,
      },
      form: {},
      rules: {
        roadName: [
          { required: true, message: "路段不能为空", trigger: "change" }
        ],
        tunnelIds: [
          { required: true, type: 'array', min: 1, message: "请至少选择一个隧道", trigger: "change" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ],
        taskUsers: [
          { 
            required: true, 
            validator: this.validateTaskUsers, 
            trigger: "change" 
          }
        ]
      }
    };
  },
  computed: {
    // 动态计算验证规则，修改时不验证路段和隧道
    dynamicRules() {
      const baseRules = {
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ],
        taskUsers: [
          { 
            required: true, 
            validator: this.validateTaskUsers, 
            trigger: "change" 
          }
        ]
      };
      
      // 新增时需要验证路段和隧道
      if (!this.form.id) {
        baseRules.roadName = [
          { required: true, message: "路段不能为空", trigger: "change" }
        ];
        baseRules.tunnelIds = [
          { required: true, type: 'array', min: 1, message: "请至少选择一个隧道", trigger: "change" }
        ];
      }
      
      return baseRules;
    },
    // 判断是否可以申请入库
    canApplyIn() {
      return this.applyStatusInfo.outApply && this.applyStatusInfo.outApply.status === 1;
    }
  },
  created() {
    this.getList();
    this.getRoadList();
    this.getUserList();
    this.loadCompanyList();
  },
  methods: {
    formatApplyType(val){
      if(val==2){
        return "入库";
      }else{
        return "出库";
      }
    },
    // 验证人员配置
    validateTaskUsers(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error('请至少添加一个人员配置'));
        return;
      }
      
      for (let i = 0; i < value.length; i++) {
        const user = value[i];
        if (!user.userId) {
          callback(new Error(`第${i + 1}行人员不能为空`));
          return;
        }
        if (!user.title) {
          callback(new Error(`第${i + 1}行职责不能为空`));
          return;
        }
      }
      
      callback();
    },
    indexMethod(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    tunnelIndexMethod(index) {
      return index + 1;
    },
    getList() {
      this.loading = true;
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getRoadList() {
      listRoad().then(response => {
        this.roadList = response.data;
      });
    },
    getUserList() {
      listUser().then(response => {
        // 确保userList中的userId为字符串类型，以便与taskUsers中的userId匹配
        this.userList = response.data.map(user => ({
          ...user,
          userId: String(user.userId)
        }));
      });
    },
    handleRoadChange(roadName) {
      this.form.roadCode = this.roadList.find(road => road.roadName === roadName)?.roadCode;
      // 清空隧道选择
      this.form.tunnelIds = [];
      if (this.form.roadName) {
        listTunnelByRoadName(this.form.roadName).then(tunnelResponse => {
          this.tunnelList = tunnelResponse.data;
          // 确保tunnelList中的tunnelId也是字符串类型，以便正确匹配
          this.tunnelList = this.tunnelList.map(tunnel => ({
            ...tunnel,
            tunnelId: String(tunnel.tunnelId)
          }));
        });
      } else {
        this.tunnelList = [];
      }
    },

    copyHandleRoadChange(roadName) {
      this.duplicateForm.roadCode = this.roadList.find(road => road.roadName === roadName)?.roadCode;
      // 清空隧道选择
      this.duplicateForm.tunnelIds = [];
      if (this.duplicateForm.roadName) {
        listTunnelByRoadName(this.duplicateForm.roadName).then(tunnelResponse => {
          this.tunnelList = tunnelResponse.data;
          // 确保tunnelList中的tunnelId也是字符串类型，以便正确匹配
          this.tunnelList = this.tunnelList.map(tunnel => ({
            ...tunnel,
            tunnelId: String(tunnel.tunnelId)
          }));
        });
      } else {
        this.tunnelList = [];
      }
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        roadName: null,
        tunnelIds: [], // 确保初始化为数组
        status: 0,
        remark: null,
        taskUsers: []
      };
      this.tunnelList = []; // 重置隧道列表
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务";
    },
    handleDetail(row) {
      this.currentTaskId = row.id; // 保存当前任务ID
      if (row.tunnelIds) {
        this.getTunnelDetails(row.tunnelIds);
      }
      // 加载申请状态信息
      this.loadApplyStatusInfo();
      this.detailOpen = true;
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTask(id).then(response => {
        this.form = response.data;
        // tunnelIds在后端是逗号分隔的字符串，前端el-select multiple需要数组格式
        // 需要确保tunnelIds转换为字符串数组，因为tunnelId在数据库中可能是数字
        this.form.tunnelIds = this.form.tunnelIds ? 
          this.form.tunnelIds.split(',').map(id => String(id.trim())) : [];
        this.form.taskUsers = this.form.taskUsers || [];
        
        // 确保taskUsers中的userId为字符串类型
        this.form.taskUsers = this.form.taskUsers.map(user => ({
          ...user,
          userId: String(user.userId)
        }));
        
        // 如果有路段名称，根据路段名称找到对应的路段代码，然后加载对应的隧道列表
        if (this.form.roadName) {
          const road = this.roadList.find(r => r.roadName === this.form.roadName);
          if (road) {
            listTunnelByRoadName(road.roadName).then(tunnelResponse => {
              this.tunnelList = tunnelResponse.data;
              // 确保tunnelList中的tunnelId也是字符串类型，以便正确匹配
              this.tunnelList = this.tunnelList.map(tunnel => ({
                ...tunnel,
                tunnelId: String(tunnel.tunnelId)
              }));
            });
          }
        }
        
        this.open = true;
        this.title = "修改任务";
      });
    },
    addTaskUser() {
      if (!this.form.taskUsers) {
        this.form.taskUsers = [];
      }
      this.form.taskUsers.push({
        userId: null,
        title: null
      });
      // 触发验证
      this.$nextTick(() => {
        this.$refs.form.validateField('taskUsers');
      });
    },
    removeTaskUser(index) {
      this.form.taskUsers.splice(index, 1);
      // 触发验证
      this.$nextTick(() => {
        this.$refs.form.validateField('taskUsers');
      });
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            // 修改时的数据格式 - 必须包含tunnelIds
            const formData = {
              id: this.form.id,
              roadName: this.form.roadName,
              tunnelIds: Array.isArray(this.form.tunnelIds) ? this.form.tunnelIds.join(',') : this.form.tunnelIds,
              status: this.form.status,
              remark: this.form.remark,
              // 确保taskUsers中的userId为字符串类型
              taskUsers: this.form.taskUsers.map(user => ({
                userId: user.userId ? String(user.userId) : null,
                title: user.title
              }))
            };
            updateTask(formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 新增时的数据格式
            const formData = {
              ...this.form,
              tunnelIds: this.form.tunnelIds.join(','),
              // 确保taskUsers中的userId为字符串类型
              taskUsers: this.form.taskUsers.map(user => ({
                userId: user.userId ? String(user.userId) : null,
                title: user.title
              }))
            };
            addTask(formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除任务编号为"' + ids + '"的数据项？').then(function() {
        return delTask(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 根据userId获取用户姓名
    getUserName(userId) {
      const user = this.userList.find(u => u.userId === userId);
      return user ? user.nickName : userId;
    },
    getTunnelDetails(tunnelIds) {
      this.tunnelDetailList = [];
      if (tunnelIds) {
        // 调用API获取隧道详情
        getTunnelDetails(tunnelIds).then(response => {
          this.tunnelDetailList = response.data;
        });
      }
    },
    // 加载申请状态信息
    loadApplyStatusInfo() {
      // 重置申请状态信息
      this.applyStatusInfo = {
        outApply: null,
        inApply: null
      };
      
      // 查询出库申请状态
      getCheckFacilityApplyByTaskId(this.currentTaskId, 1).then(response => {
        if (response.code === 200 && response.data) {
          this.applyStatusInfo.outApply = response.data;
        }
      }).catch(() => {
        // 查询失败，不影响主流程
      });
      
      // 查询入库申请状态
      getCheckFacilityApplyByTaskId(this.currentTaskId, 2).then(response => {
        if (response.code === 200 && response.data) {
          this.applyStatusInfo.inApply = response.data;
        }
      }).catch(() => {
        // 查询失败，不影响主流程
      });
    },
    facilityIndexMethod(index) {
      return index + 1;
    },
    // 设备状态序号方法
    facilityStatusIndexMethod(index) {
      return index + 1;
    },
    // 处理设备状态按钮点击
    handleFacilityStatus(row) {
      this.currentTaskForStatus = row;
      this.facilityStatusList = [];
      this.loadFacilityStatusList(row.id);
      this.facilityStatusDialogOpen = true;
    },
    // 加载设备状态列表
    loadFacilityStatusList(taskId) {
      // 调用API获取设备使用记录列表
      getFacilityStatusList(taskId).then(response => {
        this.facilityStatusList = response.data || [];
      }).catch(error => {
        this.$modal.msgError("获取设备状态列表失败");
      });
    },
    // 更新设备记录
    updateFacilityRecord(record) {
      // 调用API更新设备使用记录
      updateFacilityRecord(record).then(response => {
        // 更新成功，不需要提示
      }).catch(error => {
        this.$modal.msgError("更新设备记录失败");
        // 重新加载数据以恢复原始状态
        this.loadFacilityStatusList(this.currentTaskForStatus.id);
      });
    },
    handleFacilityApply(type) {
      this.currentApplyType = type;
      this.facilityDialogTitle = type === 'out' ? '申请设备出库' : '申请设备入库';
      this.selectedFacilities = [];
      this.facilityList = [];
      this.currentApplyRecord = null; // 清空当前申请记录
      this.isEditMode = false; // 重置编辑模式
      
      // 获取当前任务ID
      this.currentTaskId = this.getCurrentTaskId();
      
      // 如果是入库申请，先检查出库申请前置条件
      if (type === 'in') {
        this.checkOutApplyStatus().then(() => {
          // 出库申请检查通过，继续处理入库申请
          this.checkExistingApply(type);
        }).catch(error => {
          this.$modal.msgError(error.message || "无法申请入库");
        });
      } else {
        // 出库申请直接处理
        this.checkExistingApply(type);
      }
    },
    getCurrentTaskId() {
      // 从隧道详情获取当前任务ID，这里需要根据实际情况调整
      // 可能需要在handleDetail方法中保存currentTaskId
      return this.currentTaskId;
    },
    // 获取状态文本
    getStatusText(status) {
      switch(status) {
        case 0: return '待审批';
        case 1: return '已通过';
        case 2: return '已驳回';
        default: return '未知状态';
      }
    },
    // 判断设备是否被选择
    isSelectedFacility(facilityId) {
      if (!this.currentApplyRecord || !this.currentApplyRecord.facilityIdList) {
        return false;
      }
      // 确保数据类型匹配
      const selectedIds = Array.isArray(this.currentApplyRecord.facilityIdList) ? 
        this.currentApplyRecord.facilityIdList.map(id => String(id)) : 
        this.currentApplyRecord.facilityIdList.split(',').map(id => String(id.trim()));
      
      const facilityIdStr = String(facilityId);
      const isSelected = selectedIds.includes(facilityIdStr);
      return isSelected;
    },
    // 检查出库申请状态
    checkOutApplyStatus() {
      return new Promise((resolve, reject) => {
        getCheckFacilityApplyByTaskId(this.currentTaskId, 1).then(response => {
          if (response.code === 200 && response.data) {
            // 存在出库申请记录
            if (response.data.status === 1) {
              // 出库申请已通过，可以申请入库
              resolve();
            } else {
              // 出库申请未通过
              const statusText = this.getStatusText(response.data.status);
              reject(new Error(`设备出库申请${statusText}，无法申请入库`));
            }
          } else {
            // 不存在出库申请记录
            reject(new Error("请先申请设备出库"));
          }
        }).catch(() => {
          reject(new Error("查询出库申请状态失败"));
        });
      });
    },
    // 检查是否已存在申请记录
    checkExistingApply(type) {
      const applyType = type === 'out' ? 1 : 2;
      getCheckFacilityApplyByTaskId(this.currentTaskId, applyType).then(response => {
        if (response.code === 200 && response.data) {
          // 存在申请记录
          this.currentApplyRecord = response.data;
          // 只有已驳回状态(status=2)才允许修改
          if (response.data.status === 2) {
            this.isEditMode = true;
            this.facilityDialogTitle = (type === 'out' ? '修改设备出库申请' : '修改设备入库申请');
            // 加载设备列表并回显选中的设备
            this.loadFacilityListAndSelect(type, response.data.facilityIdList);
          } else {
            // 其他状态只能查看，不能修改
            this.isEditMode = false;
            const statusText = this.getStatusText(response.data.status);
            this.facilityDialogTitle = `${type === 'out' ? '设备出库申请' : '设备入库申请'} (${statusText})`;
            // 加载设备列表并回显选中的设备（只读模式）
            this.loadFacilityListAndSelect(type, response.data.facilityIdList);
          }
        } else {
          // 不存在申请记录，正常新增流程
          console.log('No existing apply record found');
          this.loadFacilityList(type);
        }
        this.facilityDialogOpen = true;
      }).catch((error) => {
        console.log('getCheckFacilityApplyByTaskId error:', error);
      });
    },
    // 加载设备列表并选中指定设备
    loadFacilityListAndSelect(type, facilityIds) {
      if (type === 'out') {
        // 出库：获取flag=1的设备列表
        this.selectFacilities().then(() => {
          this.selectPreviouslySelectedFacilities(facilityIds);
        });
      } else {
        // 入库：获取基于出库申请记录的设备列表
        this.getInFacilityList().then(() => {
          this.selectPreviouslySelectedFacilities(facilityIds);
        });
      }
    },
    // 选中之前已选择的设备
    selectPreviouslySelectedFacilities(facilityIdList) {
      if (facilityIdList && this.facilityList.length > 0) {
        // 确保facilityIdList是数组，并将所有ID转换为字符串进行比较
        this.selectedFacilities = this.facilityList.filter(facility => {
          const facilityIdStr = String(facility.id);
          const isSelected = facilityIdList.includes(facilityIdStr);
          return isSelected;
        });
        // 只有在编辑模式下才设置表格勾选状态
        if (this.isEditMode) {
          this.$nextTick(() => {
            if (this.$refs.facilityTable) {
              // 清空当前选择
              this.$refs.facilityTable.clearSelection();
              // 重新勾选对应的行
              this.selectedFacilities.forEach(facility => {
                this.$refs.facilityTable.toggleRowSelection(facility, true);
              });
              console.log('表格勾选状态已设置');
            }
          });
        }
      }
    },
    loadFacilityList(type) {
      if (type === 'out') {
        // 出库：获取flag=1的设备列表
        this.selectFacilities();
      } else {
        // 入库：获取基于出库申请记录的设备列表
        this.getInFacilityList();
      }
    },
    selectFacilities() {
      // 调用API获取出库设备列表
      return selectFacilities().then(response => {
        this.facilityList = response.data;
        return response;
      });
    },
    getInFacilityList() {
      // 调用API获取入库设备列表
      return getInFacilities(this.currentTaskId).then(response => {
        this.facilityList = response.data;
        return response;
      });
    },
    handleFacilitySelectionChange(selection) {
      this.selectedFacilities = selection;
    },
    // 关闭设备申请弹窗
    closeFacilityDialog() {
      this.facilityDialogOpen = false;
      // 清理状态
      this.selectedFacilities = [];
      this.facilityList = [];
      this.currentApplyRecord = null;
      this.isEditMode = false;
      if (this.$refs.facilityTable) {
        this.$refs.facilityTable.clearSelection();
      }
    },
    submitFacilityApply() {
      if (this.selectedFacilities.length === 0) {
        this.$modal.msgError("请选择设备");
        return;
      }
      
      const facilityIdList = this.selectedFacilities.map(f => f.id);
      const applyData = {
        taskId: this.currentTaskId,
        facilityIdList: facilityIdList,
        type: this.currentApplyType === 'out' ? 1 : 2
      };
      
      // 根据是否为编辑模式选择不同的API
      if (this.isEditMode && this.currentApplyRecord) {
        // 修改模式
        applyData.id = this.currentApplyRecord.id;
        updateCheckFacilityApply(applyData).then(response => {
          this.$modal.msgSuccess("修改申请成功");
          this.closeFacilityDialog();
          // 刷新申请状态信息
          this.loadApplyStatusInfo();
        }).catch(error => {
          this.$modal.msgError(error.message || "修改申请失败");
        });
      } else {
        // 新增模式
        addCheckFacilityApply(applyData).then(response => {
          this.$modal.msgSuccess("申请提交成功");
          this.closeFacilityDialog();
          // 刷新申请状态信息
          this.loadApplyStatusInfo();
        }).catch(error => {
          this.$modal.msgError(error.message || "申请提交失败");
        });
      }
    },
    // 复制任务相关方法
    /** 加载运营公司列表 */
    loadCompanyList() {
      getCompanyList().then(response => {
        this.companyList = response.data || [];
      }).catch(error => {
        console.error('Failed to load company list:', error);
      });
    },
    /** 处理复制任务按钮点击 */
    handleDuplicateTask(row) {
      // 获取原任务详情
      getTask(row.id).then(response => {
        const taskData = response.data;
        // 获取隧道名称列表用于显示
        if (taskData.tunnelIds) {
          getTunnelDetails(taskData.tunnelIds).then(tunnelResponse => {
            const tunnelNames = tunnelResponse.data.map(tunnel => tunnel.tunnelName).join('、');
            this.originalTask = {
              ...taskData,
              tunnelNames: tunnelNames
            };
          });
        } else {
          this.originalTask = {
            ...taskData,
            tunnelNames: '无'
          };
        }
        
        // 重置复制表单
        this.resetDuplicateForm();
        // 加载初始路段列表
        this.loadDuplicateRoadList();
        // 打开对话框
        this.duplicateDialogOpen = true;
      }).catch(error => {
        this.$modal.msgError('获取任务详情失败');
      });
    },
    /** 重置复制表单 */
    resetDuplicateForm() {
      this.duplicateForm = {
        companyName: null,
        roadName: null,
        tunnelIds: [],
        remark: null
      };
      this.duplicateRoadList = [];
      this.duplicateTunnelList = [];
    },
    /** 加载复制任务的路段列表 */
    loadDuplicateRoadList(companyName = null) {
      getRoadListByCompany(companyName).then(response => {
        this.duplicateRoadList = (response.data || []).map(item => ({
          roadName: item.roadName,
          roadCode: item.roadCode
        }));
      }).catch(error => {
        console.error('Failed to load road list:', error);
      });
    },
    /** 运营公司变化处理 */
    handleDuplicateCompanyChange(companyName) {
      // 清空路段和隧道选择
      this.duplicateForm.roadName = null;
      this.duplicateForm.tunnelIds = [];
      this.duplicateTunnelList = [];
      
      // 根据运营公司加载路段列表
      this.loadDuplicateRoadList(companyName);
    },
    /** 路段变化处理 */
    handleDuplicateRoadChange(roadName) {
      // 清空隧道选择
      this.duplicateForm.tunnelIds = [];
      this.duplicateTunnelList = [];
      
      if (roadName) {
        // 根据运营公司和路段加载隧道列表
        const params = {
          roadName: roadName
        };
        if (this.duplicateForm.companyName) {
          params.companyName = this.duplicateForm.companyName;
        }
        
        getTunnelListByParams(params.companyName, params.roadName, null).then(response => {
          this.duplicateTunnelList = (response.data || []).map(item => ({
            id: Number(item.id), // 确保 ID 是数字类型
            tunnelName: item.tunnelName,
            tunnelCode: item.tunnelCode
          }));
        }).catch(error => {
          console.error('Failed to load tunnel list:', error);
        });
      }
    },
    /** 提交复制任务 */
    submitDuplicateTask() {
      this.$refs['duplicateForm'].validate(valid => {
        if (valid) {
          // 检查是否选择了隧道
          if (!this.duplicateForm.tunnelIds || this.duplicateForm.tunnelIds.length === 0) {
            this.$modal.msgError('请至少选择一个隧道');
            return;
          }
          
          // 调用后端 API 进行任务复制
          const duplicateData = {
            originalTaskId: this.originalTask.id,
            roadName: this.duplicateForm.roadName,
            tunnelIds: this.duplicateForm.tunnelIds.join(','), // 将数组转换为逗号分隔的字符串
            remark: this.duplicateForm.remark
          };
          
          // 调用后端的复制任务 API
          duplicateTask(duplicateData).then(response => {
            this.$modal.msgSuccess('任务复制成功！原任务已完成，设备已入库；新任务已派发，设备已出库');
            this.duplicateDialogOpen = false;
            this.getList(); // 刷新任务列表
          }).catch(error => {
            this.$modal.msgError(error.message || '任务复制失败');
          });
        }
      });
    },
    /** 取消复制任务 */
    cancelDuplicateTask() {
      this.duplicateDialogOpen = false;
      this.resetDuplicateForm();
    }
  }
};
</script>