<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item label="运营公司:" prop="companyName">
        <el-select v-model="queryParams.companyName" placeholder="请选择运营公司" clearable filterable @change="handleCompanyChange">
          <el-option v-for="item in companyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="路段名称:" prop="roadName">
        <el-select v-model="queryParams.roadName" placeholder="请选择" clearable filterable @change="handleRoadChange">
          <el-option v-for="item in roadList" :key="item.label" :label="item.label" :value="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="隧道名称/编号:" prop="tunnelId">
        <el-select v-model="queryParams.tunnelId" placeholder="请选择" clearable filterable @change="selectDistinctPart(1)">
          <el-option v-for="item in tunnelCodeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="录入类型:" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择" clearable>
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="分部名称/编号:" prop="partCode">
        <el-select v-model="queryParams.partCode" placeholder="请选择" clearable filterable @change="selectDistinctItem(1)">
          <el-option v-for="item in partCodeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="分项名称/编号:" prop="partCode">
        <el-select v-model="queryParams.itemCode" placeholder="请选择" clearable filterable @change="selectDistinctQuestionDesc(1)">
          <el-option v-for="item in itemCodeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="缺陷描述:" prop="partCode">
        <el-select v-model="queryParams.questionDesc" placeholder="请选择" clearable filterable>
          <el-option v-for="item in questionDescList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:check:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:check:edit']"-->
<!--        >修改</el-button>-->
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:check:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAdd"
        >人工导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAddCar"
        >自动化导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      <el-dropdown trigger="click" :hide-on-click="false" popper-class="column-dropdown" teleported style="margin-left: 10px; display: inline-block;">
        <el-button type="primary" plain size="mini">
          列显示<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div style="padding: 10px; max-height: 300px; overflow-y: auto; z-index: 9999;">
              <el-checkbox v-for="(item, index) in columns" :key="index" v-model="item.visible" style="display: block; margin-bottom: 8px;">
                {{ item.label }}
              </el-checkbox>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-row>

    <el-table v-loading="loading" :data="checkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" :index="indexMethod" width="50"/>
      <el-table-column label="隧道名称" align="center" prop="tunnelName" width="150" v-if="columns[0].visible"/>
      <el-table-column label="设备编码" align="center" prop="equipCode" width="250" v-if="columns[1].visible"/>
      <el-table-column label="分部名称" align="center" prop="partName" v-if="columns[2].visible"/>
      <el-table-column label="分部code" align="center" prop="partCode" v-if="columns[3].visible"/>
      <el-table-column label="分项名称" align="center" prop="itemName" v-if="columns[4].visible"/>
      <el-table-column label="分项code" align="center" prop="itemCode" v-if="columns[5].visible"/>
      <el-table-column label="缺陷描述" align="center" prop="questionDesc" v-if="columns[6].visible"/>
      <el-table-column label="缺陷详情" align="center" prop="checkContent" v-if="columns[7].visible"/>
      <el-table-column label="检测缺陷设备数量" align="center" prop="questionNum" v-if="columns[8].visible" width="80"/>
<!--      <el-table-column label="检测正常设备数量" align="center" prop="goodFacilityNum" width="80"/>-->
      <el-table-column label="设备总量" align="center" prop="facilityNum" v-if="columns[9].visible" width="80"/>
      <el-table-column label="单位" align="center" prop="unit" v-if="columns[10].visible" width="50"/>
      <el-table-column label="建议措施" align="center" prop="suggestion" v-if="columns[11].visible"/>
      <el-table-column label="录入类型" align="center" v-if="columns[12].visible">
        <template v-slot:default="scope">
          {{ scope.row.type === 1 ? '人工' : scope.row.type === 2 ? '自动化' : '' }}
        </template>
      </el-table-column>
      <el-table-column label="缺陷照片1" align="center" prop="picUrl1" v-if="columns[13].visible">
        <template v-slot:default="scope">
          <el-image :src="scope.row.picUrl1" fit="cover" @click="viewImage(scope.row.picUrl1)"/>
        </template>
      </el-table-column>
      <el-table-column label="缺陷照片2" align="center" prop="picUrl2" v-if="columns[14].visible">
        <template v-slot:default="scope">
          <el-image :src="scope.row.picUrl2" fit="cover" @click="viewImage(scope.row.picUrl2)"/>
        </template>
      </el-table-column>
      <el-table-column label="缺陷照片3" align="center" prop="picUrl3" v-if="columns[15].visible">
        <template v-slot:default="scope">
          <el-image :src="scope.row.picUrl3" fit="cover" @click="viewImage(scope.row.picUrl3)"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" v-if="columns[16].visible" width="160"/>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
          <template v-slot:default="scope">
            <el-button
                size="mini"
                type="primary"
                @click="handleDetail(scope.row)"
            >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="handlePagination" />

    <!-- 检测明细对话框 -->
    <el-dialog title="检测明细" v-model="detailVisible" width="95%">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            size="mini"
            :disabled="detailMultiple"
            @click="handleBatchReview"
          >一键复核</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            size="mini"
            :disabled="detailMultiple"
            :loading="recheckLoading"
            @click="handleOneClickRecheck"
          >一键复检</el-button>
        </el-col>
        <el-col :span="1.5" v-if="isSpecialUser">
          <el-select v-model="detailParams.recheckStatus" placeholder="复检状态" clearable size="mini" @change="getDetailList">
            <el-option label="未复检" :value="0" />
            <el-option label="已复检" :value="1" />
          </el-select>
        </el-col>
        <el-dropdown trigger="click" :hide-on-click="false" popper-class="column-dropdown" teleported style="margin-left: 10px; display: inline-block;">
          <el-button type="primary" plain size="mini">
            列显示<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <div style="padding: 10px; max-height: 300px; overflow-y: auto; z-index: 9999;">
                <el-checkbox v-for="(item, index) in detailColumns" :key="index" v-model="item.visible" style="display: block; margin-bottom: 8px;">
                  {{ item.label }}
                </el-checkbox>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-row>
      <el-table v-loading="detailLoading" :data="detailList" @selection-change="handleDetailSelectionChange">
        <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
        <el-table-column label="序号" type="index" width="50"/>
        <el-table-column label="分部名称" align="center" prop="partName" v-if="detailColumns[0].visible"/>
        <el-table-column label="分项名称" align="center" prop="itemName" v-if="detailColumns[1].visible"/>
        <el-table-column label="位置" align="center" prop="locationName" v-if="detailColumns[2].visible"/>
        <el-table-column label="位置编号" align="center" prop="location" />
        <el-table-column label="设备名称" align="center" prop="name"/>
        <el-table-column label="检测桩号" align="center" prop="checkCode"/>
        <el-table-column label="缺陷描述" align="center" prop="questionDesc" v-if="detailColumns[3].visible"/>
        <el-table-column label="复核状态" align="center" prop="checkStatus" v-if="detailColumns[11].visible" width="100">
          <template v-slot:default="scope">
            <el-tag :type="scope.row.checkStatus === 1 ? 'success' : 'warning'">
              {{ scope.row.checkStatus === 1 ? '已复核' : '待复核' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="复核人员" align="center" prop="reviewerName" v-if="detailColumns[12].visible"/>
        <el-table-column label="检测人员" align="center" prop="checkUserName" v-if="detailColumns[13].visible"/>
        <el-table-column label="检测时间" align="center" prop="createTime" v-if="detailColumns[14].visible" width="180">
          <template v-slot:default="scope">
            {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column label="检测条件" align="center" prop="checkRemark" v-if="detailColumns[15].visible"/>
<!--        <el-table-column label="缺陷设备数量" align="center" prop="questionNum" v-if="detailColumns[4].visible"/>-->
        <el-table-column label="修复建议" align="center" prop="suggestion" width="350" v-if="detailColumns[5].visible"/>
        <el-table-column label="扣分值" align="center" prop="score" />
        <el-table-column label="录入类型" align="center" v-if="detailColumns[6].visible">
          <template v-slot:default="scope">
            {{ scope.row.type === 1 ? '人工' : scope.row.type === 2 ? '自动化' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="缺陷照片1" align="center" prop="picUrl1" v-if="detailColumns[7].visible">
          <template v-slot:default="scope">
            <el-image :src="scope.row.picUrl1" fit="cover" @click="viewImage(scope.row.picUrl1)"/>
          </template>
        </el-table-column>
        <el-table-column label="缺陷照片2" align="center" prop="picUrl2" v-if="detailColumns[8].visible">
          <template v-slot:default="scope">
            <el-image :src="scope.row.picUrl2" fit="cover" @click="viewImage(scope.row.picUrl2)"/>
          </template>
        </el-table-column>
        <el-table-column label="缺陷照片3" align="center" prop="picUrl3" v-if="detailColumns[9].visible">
          <template v-slot:default="scope">
            <el-image :src="scope.row.picUrl3" fit="cover" @click="viewImage(scope.row.picUrl3)"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" v-if="detailColumns[10].visible"/>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width" fixed="right">
          <template v-slot:default="scope">
            <el-button
                size="mini"
                type="success"
                @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination v-show="detailTotal > 0"
                  :total="detailTotal"
                  v-model:page="detailParams.pageNum"
                  v-model:limit="detailParams.pageSize"
                  @pagination="handleDetailPagination" />
    </el-dialog>

    <!-- 添加或修改机电设施检测对话框 -->
    <el-dialog :title="title" v-model="open" width="70%">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        
        <!-- 第一块：隧道检测记录 -->
        <div style="margin-bottom: 30px;">
          <h3 style="color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 10px; margin-bottom: 20px;">隧道检测记录</h3>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="隧道名称/编号:" prop="tunnelId">
                <el-select v-model="form.tunnelId" placeholder="请选择" clearable filterable @change="handleTunnelChange" :disabled="isEdit">
                  <el-option v-for="item in tunnelCodeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测时间范围:" prop="timeRange">
                <el-date-picker
                  v-model="form.timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始检测时间"
                  end-placeholder="结束检测时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="handleTimeRangeChange">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测设备:" prop="checkFacilityIds">
                <el-select v-model="form.checkFacilityIds" placeholder="请选择检测设备" multiple clearable filterable style="width: 100%" :disabled="checkFacilityDisabled">
                  <el-option v-for="item in checkFacilityList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测依据:" prop="standardIds">
                <el-select v-model="form.standardIds" placeholder="请选择检测依据" multiple clearable filterable style="width: 100%">
                  <el-option v-for="item in standardList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测条件-温度(℃):" style="margin-bottom: 10px;">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input v-model="form.lowTemperature" placeholder="最低温度" type="number" />
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="form.highTemperature" placeholder="最高温度" type="number" />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测条件-湿度(%RH):" style="margin-bottom: 10px;">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input v-model="form.lowHumidity" placeholder="最低湿度" type="number" />
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="form.highHumidity" placeholder="最高湿度" type="number" />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <!-- 第二块：缺陷详情 -->
        <div>
          <h3 style="color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 10px; margin-bottom: 20px;">缺陷详情</h3>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分部名称/编号:" prop="partCode">
                <el-select v-model="form.partCode" placeholder="请选择" clearable filterable @change="partCodeChange">
                  <el-option v-for="item in partCodeList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分项名称/编号:" prop="itemCode">
                <el-select v-model="form.itemCode" placeholder="请选择" clearable filterable @change="itemCodeChange">
                  <el-option v-for="item in itemCodeList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="位置:" prop="location">
                <el-select v-model="form.location" placeholder="请选择" clearable filterable @change="handleLocationChange" >
                  <el-option v-for="item in locationList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编号:" prop="code">
                <el-select v-model="form.code" placeholder="请选择" clearable filterable @change="handleCodeChange">
                  <el-option v-for="item in codeList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称:" prop="name">
                <el-input v-model="form.name" placeholder="请输入设备名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="缺陷描述:">
                <el-select v-model="form.questionDesc" placeholder="请选择" clearable filterable>
                  <el-option v-for="item in questionDescList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item v-if="electricVisible" :label="electricLabel">
                <el-input v-model="form.resistor" placeholder="请输入电阻值" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="rateVisible" label="灯具损坏比:">
                <el-input v-model="form.rate" placeholder="请输入灯具损坏比" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="numberVisible" label="数量:">
                <el-input type="number" v-model="form.num" placeholder="报警主机等需要录入" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="建议措施">
                <el-input v-model="form.suggestion" placeholder="为空时,取默认值" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="扣分值">
                <el-input type="number" v-model="form.score" placeholder="为空时,取默认值" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="8">
              <el-form-item label="缺陷照片1" prop="picUrl1" label-width="150px">
                <el-upload
                    class="avatar-uploader"
                    action="/dev-api/electric/upload/uploadPicture"
                    :show-file-list="false"
                    :on-remove="picUrl1Remove"
                    :on-success="picUrl1Upload"
                    :headers="{'Authorization': 'Bearer ' + token}"
                    :file-list="picUrl1List"
                    limit="1">
                  <img v-if="form.picUrl1" :src="form.picUrl1" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缺陷照片2" prop="picUrl2" label-width="150px">
                <el-upload
                    class="avatar-uploader"
                    action="/dev-api/electric/upload/uploadPicture"
                    :show-file-list="false"
                    :on-remove="picUrl2Remove"
                    :on-success="picUrl2Upload"
                    :headers="{'Authorization': 'Bearer ' + token}"
                    :file-list="picUrl2List"
                    limit="1">
                  <img v-if="form.picUrl2" :src="form.picUrl2" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缺陷照片3" prop="picUrl3" label-width="150px">
                <el-upload
                    class="avatar-uploader"
                    action="/dev-api/electric/upload/uploadPicture"
                    :show-file-list="false"
                    :on-remove="picUrl3Remove"
                    :on-success="picUrl3Upload"
                    :headers="{'Authorization': 'Bearer ' + token}"
                    :file-list="picUrl3List"
                    limit="1">
                  <img v-if="form.picUrl3" :src="form.picUrl3" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="经度">
                <el-input v-model="form.longitude" placeholder="请输入经度" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="纬度">
                <el-input v-model="form.latitude" placeholder="请输入纬度" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址">
                <el-input v-model="form.address" placeholder="请输入地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="图片详情" v-model="imageVisible" >
      <div style="width: 100%;height: auto;">
        <el-image :src="imageUrl" fit="cover" :preview-src-list="[imageUrl]"/>
      </div>
    </el-dialog>

    <!-- 批量复核对话框 -->
    <el-dialog title="批量复核" v-model="batchReviewVisible" width="30%">
      <el-form ref="reviewForm" :model="reviewForm" label-width="100px">
        <el-form-item label="检测条件:" prop="checkRemark">
          <el-input 
            v-model="reviewForm.checkRemark" 
            type="textarea" 
            placeholder="请输入检测条件"
            :rows="4" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchReviewVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitBatchReview">确 定</el-button>
      </div>
    </el-dialog>

    <ExcelUpload :file="file" @refreshDataList="handleQuery"></ExcelUpload>
    <ExcelUpload :file="fileCar" @refreshDataList="handleQuery"></ExcelUpload>
  </div>
</template>

<script>
import {listCheck, getCheck, delCheck, addCheck, updateCheck, saveOrUpdateTunnelCheck, listCheckDetail, batchReview, oneClickRecheck} from "@/api/electric/check";
import {listDistinctRoad, selectDistinctTunnelCode, selectDistinctCompany, selectDistinctRoad} from "@/api/electric/tunnelInfo";
import {selectDistinctItem, selectDistinctPart, selectDistinctQuestionDesc,selectDistinctLocation,selectDistinctCode,selectDistinctType, selectDistinctName} from "@/api/electric/facilityInfo";
import {listCheckStandard} from "@/api/electric/checkStandard";
import {getCheckFacilitiesByUser, listAllCheckFacility, listScCheckFacility} from "@/api/electric/checkFacility";

import {getToken} from "@/utils/auth";
import ExcelUpload from "@/views/common/excelUpload.vue";

export default {
  name: "Check",
  components: {ExcelUpload},
  data() {
    return {
      isEdit:false,
      checkFacilityDisabled: false, // 检测设备是否禁用
      roadList:[],
      companyList: [],
      file:{
        batchAddOpen:false,
        templateUrl:'checkImport.xlsx',
        uploadUrl:'/tunnel/electric/check/batchImport',
        excelName:'检测导入模板.xlsx'
      },
      fileCar:{
        batchAddOpen:false,
        templateUrl:'checkImport.xlsx',
        uploadUrl:'/tunnel/electric/check/batchImportCar',
        excelName:'检测导入模板.xlsx'
      },
      batchAddTitle:'',
      typeList:[],
      typeList2:[],
      questionDescList:[],
      partCodeList:[],
      itemCodeList:[],
      questionDescList2:[],
      partCodeList2:[],
      itemCodeList2:[],
      locationList:[],
      locationList2:[],
      codeList:[],
      codeList2:[],
      checkFacilityList:[],
      standardList:[],
      token:null,
      picUrl1List:[],
      picUrl2List:[],
      picUrl3List:[],
      roadCodeList:[],
      tunnelCodeList:[],
      imageUrl:null,
      imageVisible:false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机电设施检测表格数据
      checkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roadCode: null,
        tunnelId: null,
        partName: null,
        itemName: null,
        checkProject: null,
        checkContent: null,
        questionDesc: null,
        score:null,
        picUrl1:null,
        picUrl2:null,
        picUrl3:null,
        remark:null,
        longitude:null,
        latitude:null,
        address:null,
        type: null,
        companyName: null,
        roadName: null
      },
      // 表单参数
      form: {
        status: 2,
        id: null,
        tunnelTotalId: null,
        tunnelId: null,
        facilityId: null,
        itemId: null,
        checkProject: null,
        checkItem: null,
        checkContent: null,
        questionDesc: null,
        suggestion: null,
        score: null,
        picUrl1: null,
        picUrl2: null,
        picUrl3: null,
        remark: null,
        createTime: null,
        updateTime: null,
        resistor: null,
        rate: null,
        num: null,
        name: '',
        sameName: false,
        startTime: null,
        endTime: null,
        timeRange: null,
        checkFacilityIds: [],
        standardIds: [],
        lowTemperature: null,
        highTemperature: null,
        lowHumidity: null,
        highHumidity: null,
      },
      // 表单校验
      rules: {
        tunnelId: [
          { required: true, message: "隧道不能为空", trigger: "blur" }
        ],
        partCode: [
          { required: true, message: "分部不能为空", trigger: "blur" }
        ],
        itemCode: [
          { required: true, message: "分项不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "位置不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "编号不能为空", trigger: "blur" }
        ],
        questionDesc: [
          { required: true, message: "缺陷描述不能为空", trigger: "blur" }
        ],
        timeRange: [
          { required: true, message: "检测时间范围不能为空", trigger: "change" }
        ],
      },
      // 详情对话框显示状态
      detailVisible: false,
      // 详情加载状态
      detailLoading: false,
      // 详情列表数据
      detailList: [],
      // 详情总条数
      detailTotal: 0,
      // 详情查询参数
      detailParams: {
        pageNum: 1,
        pageSize: 10,
        tunnelId: null,
        partCode: null,
        itemCode: null,
        questionDesc: null,
        recheckStatus: null
      },
      // 列显示控制
      columns: [
        { label: '隧道名称', visible: true },
        { label: '设备编码', visible: false },
        { label: '分部名称', visible: false },
        { label: '分部code', visible: false },
        { label: '分项名称', visible: true },
        { label: '分项code', visible: false },
        { label: '缺陷描述', visible: true },
        { label: '缺陷详情', visible: true },
        { label: '缺陷设备数量', visible: true },
        { label: '设备总量', visible: true },
        { label: '单位', visible: true },
        { label: '建议措施', visible: false },
        { label: '录入类型', visible: false },
        { label: '缺陷照片1', visible: false },
        { label: '缺陷照片2', visible: false },
        { label: '缺陷照片3', visible: false },
        { label: '备注', visible: true },
      ],
      // 详情列显示控制
      detailColumns: [
        { label: '分部名称', visible: true },
        { label: '分项名称', visible: true },
        { label: '位置', visible: true },
        { label: '缺陷描述', visible: true },
        { label: '缺陷设备数量', visible: true },
        { label: '修复建议', visible: true },
        { label: '录入类型', visible: true },
        { label: '缺陷照片1', visible: true },
        { label: '缺陷照片2', visible: true },
        { label: '缺陷照片3', visible: true },
        { label: '备注', visible: true },
        { label: '复核状态', visible: true },
        { label: '复核人员', visible: true },
        { label: '检测人员', visible: true },
        { label: '检测时间', visible: true },
        { label: '检测条件', visible: true },
      ],
      // 录入类型选项
      typeOptions: [
        { value: 1, label: '人工' },
        { value: 2, label: '自动化' }
      ],
      // 特殊字段状态变量
      electricVisible: false,
      electricLabel: '电阻值:',
      rateVisible: false,
      numberVisible: false,
      // 批量复核相关
      batchReviewVisible: false,
      detailMultiple: true,
      detailSelection: [],
      reviewForm: {
        checkRemark: ''
      },
      // 一键复检加载状态
      recheckLoading: false,
      // 特殊用户标识
      isSpecialUser: false,
    };
  },
  created() {
    this.getCompanyList();
    this.getRoadList();
    this.selectDistinctTunnelCode();
    this.loadStandards();
    this.token=getToken();
    this.checkSpecialUser();
    this.getList();
  },
  methods: {
    // 计算序号
    indexMethod(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    formatterEquipCode(row, column, cellValue) {
      return row.tunnelCode+'-'+row.equipCode;
    },
    itemChange(type){
      this.selectDistinctLocation(type);
      this.selectDistinctCode(type);
      this.selectDistinctQuestionDesc(type);
    },
    selectDistinctQuestionDesc(type){
      var _this = this;
      let req={};
      if(type==1){
        _this.questionDescList = [];
        _this.queryParams.questionDesc=null;
        req.partCode=_this.queryParams.partCode;
        req.itemCode=_this.queryParams.itemCode;
      } else if(type==3) {
        // 修改模式
        _this.questionDescList2 = [];
        req.partCode=_this.form.partCode;
        req.itemCode=_this.form.itemCode;
      } else {
        _this.questionDescList2 = [];
        _this.form.questionDesc=null;
        req.partCode=_this.form.partCode;
        req.itemCode=_this.form.itemCode;
      }
      selectDistinctQuestionDesc(req).then(response => {
        response.data.forEach(function (e){
          let temp = {};
          temp.value = e.questionDesc;
          temp.label = e.questionDesc;
          if(type==1) {
            _this.questionDescList.push(temp);
          }else{
            _this.questionDescList2.push(temp);
          }
        })
      });
    },
    selectDistinctCode(type) {
      return new Promise((resolve, reject) => {
        var _this = this;
        let req = {};
        
        // 根据 type 初始化参数
        if (type === 1) {
          _this.codeList = [];
          _this.queryParams.code = null;
          req.tunnelId = _this.queryParams.tunnelId;
          req.partCode = _this.queryParams.partCode;
          req.itemCode = _this.queryParams.itemCode;
          req.location = _this.queryParams.location;
        } else if (type === 2) {
          _this.codeList2 = [];
          _this.form.code = null;
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          req.itemCode = _this.form.itemCode;
          req.location = _this.form.location;
        } else if (type === 3) {
          // 修改模式，保留当前值
          _this.codeList2 = [];
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          req.itemCode = _this.form.itemCode;
          // 确保location是字符串而不是数组
          req.location = Array.isArray(_this.form.location) ? _this.form.location[0] : _this.form.location;
          // 不清空已有数据
        } else {
          _this.codeList2 = [];
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          req.itemCode = _this.form.itemCode;
          req.location = _this.form.location;
        }
        
        // 确保请求参数有效
        if (!req.tunnelId || !req.partCode || !req.itemCode || !req.location) {
          console.error('tunnelId、partCode、itemCode或location不能为空');
          resolve();
          return;
        }
        
        // 确保location是字符串而不是数组
        if (Array.isArray(req.location)) {
          req.location = req.location[0];
        }
        
        // 发起请求
        selectDistinctCode(req).then(response => {
          for (let e of response.data) {
            let temp = {};
            temp.value = e.code;
            temp.label = e.code;
            if (type === 1) {
              _this.codeList.push(temp);
            } else {
              _this.codeList2.push(temp);
            }
          }
          // 请求成功后调用 resolve
          resolve();
        }).catch(error => {
          console.error('selectDistinctCode 请求失败:', error);
          // 请求失败后调用 reject
          reject(error);
        });
      });
    },
    selectDistinctType(type){
      var _this = this;
      let req={};
      if(type==1){
        _this.typeList = [];
        _this.queryParams.type=null;
        req.tunnelId=_this.queryParams.tunnelId;
        req.partCode=_this.queryParams.partCode;
        req.itemCode=_this.queryParams.itemCode;
      }else if(type==2){
        _this.typeList2 = [];
        _this.form.type=null;
        req.tunnelId=_this.form.tunnelId;
        req.partCode=_this.form.partCode;
        req.itemCode=_this.form.itemCode;
      }else{
        _this.typeList2 = [];
        req.tunnelId=_this.form.tunnelId;
        req.partCode=_this.form.partCode;
        req.itemCode=_this.form.itemCode;
      }
      selectDistinctType(req).then(response => {
        response.data.forEach(function (e){
          let temp = {};
          temp.value = e.type;
          temp.label = e.type;
          if(type==1) {
            _this.typeList.push(temp);
          }else{
            _this.typeList2.push(temp);
          }
        })
      });
    },
    selectDistinctLocation(type) {
      return new Promise((resolve, reject) => {
        var _this = this;
        let req = {};
        
        // 根据 type 初始化参数
        if (type === 1) {
          _this.locationList = [];
          _this.queryParams.location = null;
          req.tunnelId = _this.queryParams.tunnelId;
          req.partCode = _this.queryParams.partCode;
          req.itemCode = _this.queryParams.itemCode;
        } else if (type === 2) {
          _this.locationList2 = [];
          _this.form.location = null;
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          req.itemCode = _this.form.itemCode;
        } else if (type === 3) {
          // 修改模式，保留当前值
          _this.locationList2 = [];
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          req.itemCode = _this.form.itemCode;
        } else {
          _this.locationList2 = [];
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          req.itemCode = _this.form.itemCode;
        }
        
        // 确保请求参数有效
        if (!req.tunnelId || !req.partCode || !req.itemCode) {
          console.error('tunnelId、partCode或itemCode不能为空');
          resolve();
          return;
        }
        
        // 发起请求
        selectDistinctLocation(req).then(response => {
          for (let e of response.data) {
            let temp = {};
            temp.value = e.location;
            temp.label = e.locationName;
            if (type === 1) {
              _this.locationList.push(temp);
            } else {
              _this.locationList2.push(temp);
            }
          }
          // 请求成功后调用 resolve
          resolve();
        }).catch(error => {
          console.error('selectDistinctLocation 请求失败:', error);
          // 请求失败后调用 reject
          reject(error);
        });
      });
    },
    selectDistinctItem(type) {
      return new Promise((resolve, reject) => {
        var _this = this;
        let req = {};
        // 根据 type 初始化参数
        if (type === 1) {
          _this.itemCodeList = [];
          _this.questionDescList = [];
          _this.queryParams.itemCode = null;
          _this.queryParams.questionDesc = null;
          _this.queryParams.location = null;
          _this.queryParams.code = null;
          req.tunnelId = _this.queryParams.tunnelId;
          req.partCode = _this.queryParams.partCode;
        } else if(type === 2) {
          _this.itemCodeList2 = [];
          _this.questionDescList2 = [];
          _this.form.itemCode = null;
          _this.form.questionDesc = null;
          _this.form.location = null;
          _this.form.code = null;
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
        } else if(type === 3) {
          // 修改模式，保留当前值
          _this.itemCodeList2 = [];
          req.tunnelId = _this.form.tunnelId;
          req.partCode = _this.form.partCode;
          // 不清空已有数据
        } else {
          _this.itemCodeList2 = [];
          req.partCode = _this.form.partCode;
          req.tunnelId = _this.form.tunnelId;
        }
        
        // 确保请求参数有效
        if (!req.tunnelId || !req.partCode) {
          console.error('tunnelId或partCode不能为空');
          resolve();
          return;
        }
        
        // 发起请求
        selectDistinctItem(req).then(response => {
          for (let e of response.data) {
              let temp = {};
              temp.value = e.itemCode;
              temp.label = e.itemName + "/" + e.itemCode;
              if (type === 1) {
                _this.itemCodeList.push(temp);
              } else {
                _this.itemCodeList2.push(temp);
              }
            }
            // 请求成功后调用 resolve
            resolve();
          }).catch(error => {
            console.error('selectDistinctItem 请求失败:', error);
            // 请求失败后调用 reject
            reject(error);
          });
      });
    },
    selectDistinctPart(type) {
      return new Promise((resolve, reject) => {
        var _this = this;
        let req = {};
        // 根据 type 初始化参数
        if (type === 1) {
          _this.partCodeList = [];
          _this.itemCodeList = [];
          _this.questionDescList = [];
          _this.queryParams.partCode = null;
          _this.queryParams.itemCode = null;
          _this.queryParams.location = null;
          _this.queryParams.code = null;
          _this.queryParams.questionDesc = null;
          req.tunnelId = _this.queryParams.tunnelId;
        } else if(type === 2) {
          _this.partCodeList2 = [];
          _this.itemCodeList2 = [];
          _this.questionDescList2 = [];
          _this.form.partCode = null;
          _this.form.itemCode = null;
          _this.form.questionDesc = null;
          _this.form.location = null;
          _this.form.code = null;
          req.tunnelId = _this.form.tunnelId;
        } else if(type === 3) {
          // 修改模式，保留当前值
          _this.partCodeList2 = [];
          req.tunnelId = _this.form.tunnelId;
          // 不清空已有数据
        } else {
          _this.partCodeList2 = [];
          req.tunnelId = _this.form.tunnelId;
        }
        
        // 确保请求参数有效
        if (!req.tunnelId) {
          console.error('tunnelId不能为空');
          resolve();
          return;
        }
        
        // 发起请求
        selectDistinctPart(req).then(response => {
          for (let e of response.data) {
              let temp = {};
              temp.value = e.partCode;
              temp.label = e.partName + "/" + e.partCode;
              if (type === 1) {
                _this.partCodeList.push(temp);
              } else {
                _this.partCodeList2.push(temp);
              }
            }
            // 请求成功后调用 resolve
            resolve();
          }).catch(error => {
            console.error('selectDistinctPart 请求失败:', error);
            // 请求失败后调用 reject
            reject(error);
          });
      });
    },
    selectDistinctTunnelCode(){
      var _this = this;
      _this.tunnelCodeList = [];
      let req = {};
      // 如果有路段名称和公司名称筛选则添加条件，否则获取所有隧道
      if (_this.queryParams.roadName) {
        req.roadName = _this.queryParams.roadName;
      }
      if (_this.queryParams.companyName) {
        req.companyName = _this.queryParams.companyName;
      }
      selectDistinctTunnelCode(req).then(response => {
        response.data.forEach(function (e){
          let temp = {};
          temp.value = e.id;
          temp.label = e.tunnelName + "/" + e.tunnelCode;
          _this.tunnelCodeList.push(temp);
        })
      });
    },
    viewImage(url){
      this.imageVisible=true;
      this.imageUrl=url;
    },
    /** 查询机电设施检测列表 */
    getList() {
      this.loading = true;
      listCheck(this.queryParams).then(response => {
        this.checkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tunnelTotalId: null,
        tunnelId: null,
        facilityId: null,
        itemId: null,
        checkProject: null,
        checkItem: null,
        checkContent: null,
        questionDesc: null,
        suggestion: null,
        score: null,
        picUrl1: null,
        picUrl2: null,
        picUrl3: null,
        remark: null,
        createTime: null,
        updateTime: null,
        resistor: null,
        rate: null,
        num: null,
        name: '',
        sameName: false,
        startTime: null,
        endTime: null,
        timeRange: null,
        checkFacilityIds: [],
        standardIds: [],
        lowTemperature: null,
        highTemperature: null,
        lowHumidity: null,
        highHumidity: null,
      };
      this.electricVisible = false;
      this.rateVisible = false;
      this.numberVisible = false;
      this.checkFacilityDisabled = false; // 重置检测设备禁用状态
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 清空所有列表数据和筛选条件
      this.roadList = [];
      this.tunnelCodeList = [];
      this.partCodeList = [];
      this.itemCodeList = [];
      this.questionDescList = [];
      this.queryParams.companyName = null;
      this.queryParams.roadName = null;
      this.queryParams.tunnelId = null;
      this.queryParams.partCode = null;
      this.queryParams.itemCode = null;
      this.queryParams.questionDesc = null;
      this.queryParams.type = null;
      // 重新获取公司列表
      this.getCompanyList();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加机电设施检测";
      this.isEdit = false;
      this.form.sameName = false;
      this.form.name = '';
      this.form.resistor = null;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let _this = this;
      this.reset(); // 确保先重置表单
      _this.form = { ...row }; // 使用浅拷贝避免直接修改原始对象
      this.isEdit = true; // 标记为编辑模式
      
      // 将startTime和endTime组合成timeRange
      if (_this.form.startTime && _this.form.endTime) {
        _this.form.timeRange = [_this.form.startTime, _this.form.endTime];
      }
      
      // 处理检测设备字段 - 将逗号分隔的字符串转换为数组
      if (_this.form.checkFacilityIds && typeof _this.form.checkFacilityIds === 'string') {
        _this.form.checkFacilityIds = _this.form.checkFacilityIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      } else if (!_this.form.checkFacilityIds) {
        _this.form.checkFacilityIds = [];
      }
      
      // 处理检测依据字段 - 将逗号分隔的字符串转换为数组  
      if (_this.form.standardIds && typeof _this.form.standardIds === 'string') {
        _this.form.standardIds = _this.form.standardIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      } else if (!_this.form.standardIds) {
        _this.form.standardIds = [];
      }
      
      // 保存原始的standardIds用于后续设备名称映射
      _this.originalStandardIds = _this.form.standardIds ? [..._this.form.standardIds] : [];
      
      // 处理位置字段 - 改为单选值而非数组
      if (_this.form.location && typeof _this.form.location === 'string') {
        // 如果是字符串，直接使用不转为数组
        _this.form.location = _this.form.location;
      } else if (Array.isArray(_this.form.location) && _this.form.location.length > 0) {
        // 如果是数组，取第一个元素
        _this.form.location = _this.form.location[0];
      } else if (!_this.form.location) {
        _this.form.location = null;
      }
      
      // 处理编号字段 - 改为单选值而非数组
      if (_this.form.code && typeof _this.form.code === 'string') {
        // 如果是字符串，直接使用不转为数组
        _this.form.code = _this.form.code;
      } else if (Array.isArray(_this.form.code) && _this.form.code.length > 0) {
        // 如果是数组，取第一个元素
        _this.form.code = _this.form.code[0];
      } else if (!_this.form.code) {
        _this.form.code = null;
      }
      
      // 加载检测设备（修改模式下也需要加载）
      if (_this.form.tunnelId) {
        _this.getCheckFacilitiesByUser(_this.form.tunnelId);
      }
      
      // 按照依赖顺序加载下拉选项
      _this.selectDistinctPart(3).then(() => {
        _this.selectDistinctItem(3).then(() => {
          _this.selectDistinctQuestionDesc(3);
          _this.selectDistinctLocation(3).then(() => {
            // 位置加载完成后再加载编号
            setTimeout(() => {
              _this.selectDistinctCode(3).then(() => {
                // 编号加载完成后再加载设备名称
                if (_this.form.tunnelId && _this.form.partCode && 
                    _this.form.itemCode && _this.form.location && _this.form.code) {
                  _this.selectDistinctName(1);
                }
              });
            }, 100);
          });
        });
      }).catch(error => {
        console.error('请求出错:', error);
      });
      
      this.open = true;
      this.title = "修改机电设施检测";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let arrays = [];
          if (!this.form.code || this.form.code.length === 0) {
            this.$message.error("编号不能为空");
            return;
          }
          if ((!this.form.questionDesc || this.form.questionDesc== null) && this.form.id==null) {
            this.$message.error("缺陷描述不能为空");
            return;
          }
          let location = this.form.location;
          if (!location) {
            this.$message.error("位置不能为空");
            return;
          }
          let obj = JSON.parse(JSON.stringify(this.form));
          obj.questionDesc=this.form.questionDesc;
          if(this.form.questionDesc== null || this.form.questionDesc== ""){
            obj.status=1;
          }else{
            obj.status=2;
          }
          // 将数组字段转换为逗号分隔的字符串
          if (Array.isArray(obj.checkFacilityIds)) {
            obj.checkFacilityIds = obj.checkFacilityIds.join(',');
          }
          if (Array.isArray(obj.standardIds)) {
            obj.standardIds = obj.standardIds.join(',');
          }
          arrays.push(obj);
          saveOrUpdateTunnelCheck(arrays).then(response => {
            this.$modal.msgSuccess("操作成功");
            this.open = false;
            this.getDetailList();
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除机电设施检测编号为"' + ids + '"的数据项？').then(function() {
        return delCheck(ids);
      }).then(() => {
        this.getList();
        this.getDetailList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tunnel/electric/check/export', {
        ...this.queryParams
      }, `检测缺陷清单${new Date().getTime()}.xlsx`)
    },
    picUrl3Upload: function (response, file, fileList) {
      let pictureList = [response.msg];
      this.picUrl3List = pictureList.map(item => {
        return {
          name: item,
          url: item
        }
      });
      this.form.picUrl3 = response.msg;
    },
    //上传图片
    picUrl3Remove() {
      this.form.picUrl3 = null;
    },
    picUrl2Upload: function (response, file, fileList) {
      let pictureList = [response.msg];
      this.picUrl2List = pictureList.map(item => {
        return {
          name: item,
          url: item
        }
      });
      this.form.picUrl2 = response.msg;
    },
    //上传图片
    picUrl2Remove() {
      this.form.picUrl2 = null;
    },
    picUrl1Upload: function (response, file, fileList) {
      let pictureList = [response.msg];
      this.picUrl1List = pictureList.map(item => {
        return {
          name: item,
          url: item
        }
      });
      this.form.picUrl1 = response.msg;
    },
    //上传图片
    picUrl1Remove() {
      this.form.picUrl1 = null;
    },
    handleBatchAdd() {
      this.file.batchAddOpen = true;
      this.file.batchAddTitle = "人工导入";
    },
    handleBatchAddCar() {
      this.fileCar.batchAddOpen = true;
      this.fileCar.batchAddTitle = "自动化导入";
    },
    /** 查看详情按钮操作 */
    handleDetail(row) {
      this.detailVisible = true;
      this.detailParams.tunnelId = row.tunnelId;
      this.detailParams.partCode = row.partCode;
      this.detailParams.itemCode = row.itemCode;
      if(this.detailParams.questionDesc!=null && this.detailParams.questionDesc!=''){
        this.detailParams.questionDesc = row.questionDesc;
      }
      this.detailParams.type = this.queryParams.type;
      this.getDetailList();
    },

    /** 获取详情列表 */
    getDetailList() {
      this.detailLoading = true;
      listCheckDetail(this.detailParams).then(response => {
        this.detailList = response.rows;
        this.detailTotal = response.total;
        this.detailLoading = false;
      });
    },
    /* 分页处理 */
    handlePagination(val) {
      this.queryParams.pageNum = val.page;
      this.queryParams.pageSize = val.limit;
      this.getList();
    },
    /* 详情分页处理 */
    handleDetailPagination(val) {
      this.detailParams.pageNum = val.page;
      this.detailParams.pageSize = val.limit;
      this.getDetailList();
    },
    partCodeChange(value) {
      let _this = this;
      _this.form.itemCode = null;
      _this.form.location = null;
      _this.form.code = null;
      _this.form.questionDesc = null;
      _this.form.score = null;
      _this.form.suggestion = null;
      
      // 清空对应的列表
      _this.itemCodeList2 = [];
      _this.locationList2 = [];
      _this.codeList2 = [];
      _this.questionDescList2 = [];
      
      if(!value) {
        _this.form.partCode = null;
        return;
      }
      
      // 加载分项数据
      _this.selectDistinctItem(2);
    },
    itemCodeChange(value) {
      let _this = this;
      _this.form.location = null;
      _this.form.code = null;
      _this.form.questionDesc = null;
      _this.form.score = null;
      _this.form.suggestion = null;
      
      // 清空对应的列表
      _this.locationList2 = [];
      _this.codeList2 = [];
      _this.questionDescList2 = [];
      
      if(!value) {
        _this.form.itemCode = null;
        return;
      }
      
      // 特殊字段处理
      // 根据分部和分项的组合来控制特殊字段的显示
      if(_this.form.partCode === '4' && (value === "01" || value === "02")) {
        _this.numberVisible = true;
      } else {
        _this.numberVisible = false;
      }
      
      if(_this.form.partCode === '1' && value === "11") {
        _this.electricVisible = true;
        _this.electricLabel = "接地电阻值:";
        _this.rateVisible = false;
      } else if(_this.form.partCode === '1' && value === "05") {
        _this.electricVisible = true;
        _this.electricLabel = "绝缘电阻值:";
        _this.rateVisible = false;
      } else if(_this.form.partCode === '2' && value === "01") {
        _this.electricVisible = true;
        _this.electricLabel = "亮度值:";
        _this.rateVisible = true;
      } else {
        _this.electricVisible = false;
        _this.rateVisible = false;
      }
      
      // 加载位置和编号数据
      _this.selectDistinctLocation(2);
      _this.selectDistinctCode(2);
      _this.selectDistinctQuestionDesc(2);
    },
    locationChange(value) {
      let _this = this;
      _this.form.code = null;
      _this.form.questionDesc = null;
      
      // 清空对应的列表
      _this.codeList2 = [];
      _this.questionDescList2 = [];
      
      if(!value || value.length === 0) {
        return;
      }
      
      // 加载编号数据
      _this.selectDistinctCode(2);
      _this.selectDistinctQuestionDesc(2);
    },
    // 编号变更处理方法
    handleCodeChange(value) {
      let _this = this;
      
      if (!value) {
        _this.form.code = null;
        _this.form.name = '';
        _this.form.resistor = null;
        return;
      }
      
      _this.form.code = value;
      
      // 确保必要参数存在
      if (_this.form.tunnelId && _this.form.partCode && _this.form.itemCode && _this.form.location) {
        // 创建请求对象
        let req = {
          tunnelId: _this.form.tunnelId,
          partCode: _this.form.partCode,
          itemCode: _this.form.itemCode,
          locationList: [_this.form.location], // 包装成数组
          code: _this.form.code
        };
        
        // 调用API获取设备名称
        selectDistinctName(req).then(response => {
          if (response.data) {
            let data = response.data;
            _this.form.name = data.name || '';
            _this.form.resistor = data.resistor || null;
          } else {
            // 如果没有数据，清空相关字段
            _this.form.name = '';
            _this.form.resistor = null;
          }
        }).catch(error => {
          console.error('获取设备名称失败:', error);
          _this.form.name = '';
          _this.form.resistor = null;
        });
      } else {
        console.warn('缺少获取设备名称所需的参数');
      }
    },
    selectDistinctName(type) {
      return new Promise((resolve, reject) => {
        var _this = this;
        
        // 当form.sameName为true时不请求
        if (type === 2 && _this.form.sameName === true) {
          resolve();
          return;
        }
        
        // 确保必须的参数存在
        if (!_this.form.tunnelId || !_this.form.partCode || !_this.form.itemCode || !_this.form.location) {
          console.warn('selectDistinctName: 缺少必要参数 tunnelId、partCode、itemCode 或 location');
          resolve();
          return;
        }
        
        let req = {};
        req.tunnelId = _this.form.tunnelId;
        req.partCode = _this.form.partCode;
        req.itemCode = _this.form.itemCode;
        req.locationList = [_this.form.location]; // 将单个location包装为数组
        
        // 如果type为1，添加code参数
        if (type === 1 && _this.form.code) {
          req.code = _this.form.code;
        }
        
        selectDistinctName(req).then(response => {
          if (response.data) {
            let data = response.data;
            _this.form.name = data.name || '';
            _this.form.resistor = data.resistor || null;
            
            // 根据返回的设备名称设置sameName属性
            if (type === 0) {
              if (data.num === 1 || !data.name || data.name === '') {
                _this.form.sameName = true;
              } else {
                _this.form.sameName = false;
              }
            }
          } else {
            // 如果没有返回数据，设置默认值
            _this.form.name = '';
            _this.form.resistor = null;
            if (type === 0) {
              _this.form.sameName = true;
            }
          }
          
          resolve();
        }).catch(error => {
          console.error('selectDistinctName 请求失败:', error);
          // 出错时也设置默认值
          _this.form.name = '';
          _this.form.resistor = null;
          if (type === 0) {
            _this.form.sameName = true;
          }
          resolve(); // 即使出错也resolve，避免阻塞流程
        });
      });
    },
    // 位置变更处理方法
    handleLocationChange(value) {
      let _this = this;
      
      // 清空相关字段
      if (!value) {
        _this.form.location = null;
        _this.form.code = null;
        _this.form.name = '';
        _this.form.resistor = null;
        _this.codeList2 = []; // 清空编号列表
        return;
      }
      
      _this.form.location = value;
      _this.form.code = null; // 清空编号
      _this.form.name = ''; // 清空设备名称
      _this.form.resistor = null; // 清空电阻值
      
      // 确保必要参数存在
      if (_this.form.tunnelId && _this.form.partCode && _this.form.itemCode) {
        // 创建请求对象
        let req = {
          tunnelId: _this.form.tunnelId,
          partCode: _this.form.partCode,
          itemCode: _this.form.itemCode,
          location: _this.form.location
        };
        
        // 加载编号列表
        _this.selectDistinctCode(2).then(() => {
          // 检查是否有默认的编号可以选择
          if (_this.codeList2 && _this.codeList2.length > 0) {
            // 如果只有一个编号，则自动选择它
            if (_this.codeList2.length === 1) {
              _this.form.code = _this.codeList2[0].value;
              _this.handleCodeChange(_this.form.code);
            }
          }
        }).catch(error => {
          console.error('加载编号列表失败:', error);
        });
        
        // 尝试获取位置对应的设备名称（无需编号）
        let nameReq = {
          tunnelId: _this.form.tunnelId,
          partCode: _this.form.partCode,
          itemCode: _this.form.itemCode,
          locationList: [_this.form.location]
        };
        
        selectDistinctName(nameReq).then(response => {
          if (response.data) {
            let data = response.data;
            // 如果所有设备名称相同，直接设置
            if (data.num === 1) {
              _this.form.name = data.name || '';
              _this.form.resistor = data.resistor || null;
              _this.form.sameName = true;
            } else {
              _this.form.sameName = false;
            }
          }
        }).catch(error => {
          console.error('获取位置设备名称失败:', error);
        });
      } else {
        console.warn('缺少加载编号所需的参数');
      }
    },
    // 获取运营公司列表
    getCompanyList() {
      const _this = this;
      _this.companyList = [];
      selectDistinctCompany({}).then(response => {
        response.data.forEach(item => {
          if (item.companyName) {
            const temp = {
              value: item.companyName,
              label: item.companyName
            };
            _this.companyList.push(temp);
          }
        });
      });
    },
    // 获取路段列表
    getRoadList() {
      const _this = this;
      _this.roadList = [];
      const req = {};
      // 如果有公司名称筛选则添加条件，否则获取所有路段
      if (_this.queryParams.companyName) {
        req.companyName = _this.queryParams.companyName;
      }
      selectDistinctRoad(req).then(response => {
        response.data.forEach(item => {
          if (item.roadName) {
            const temp = {
              value: item.roadName,
              label: item.roadName
            };
            _this.roadList.push(temp);
          }
        });
      });
    },
    // 处理公司变化
    handleCompanyChange() {
      this.queryParams.roadName = null;
      this.queryParams.tunnelId = null;
      this.tunnelCodeList = [];
      this.roadList = [];
      if (this.queryParams.companyName) {
        this.getRoadList();
      } else {
        // 如果清空公司，仍然要加载所有路段
        this.getRoadList();
      }
    },
    // 处理路段变化
    handleRoadChange() {
      this.queryParams.tunnelId = null;
      this.tunnelCodeList = [];
      this.selectDistinctTunnelCode();
    },
    // 判断检测明细是否可勾选 - 只有待复核状态才能勾选
    checkSelectable(row, index) {
      return row.checkStatus !== 1; // checkStatus为1表示已复核，不可勾选
    },
    // 详情多选框选中数据
    handleDetailSelectionChange(selection) {
      this.detailSelection = selection;
      this.detailMultiple = !selection.length;
    },
    // 处理批量复核
    handleBatchReview() {
      if (this.detailSelection.length === 0) {
        this.$message.warning('请选择需要复核的记录');
        return;
      }
      this.reviewForm.checkRemark = '';
      this.batchReviewVisible = true;
    },
    // 提交批量复核
    submitBatchReview() {
      if (!this.reviewForm.checkRemark.trim()) {
        this.$message.warning('请输入检测条件');
        return;
      }
      
      const ids = this.detailSelection.map(item => item.id);
      const data = {
        ids: ids,
        checkRemark: this.reviewForm.checkRemark.trim()
      };
      
      batchReview(data).then(response => {
        this.$modal.msgSuccess(`成功复核 ${response.data} 条记录`);
        this.batchReviewVisible = false;
        this.getDetailList(); // 刷新详情列表
        this.getList(); // 刷新主列表
      }).catch(() => {
        this.$modal.msgError('批量复核失败');
      });
    },
    // 处理一键复检
    handleOneClickRecheck() {
      if (this.detailSelection.length === 0) {
        this.$message.warning('请选择需要复检的记录');
        return;
      }
      
      this.$modal.confirm('确认要对选中的 ' + this.detailSelection.length + ' 条记录进行复检吗？\n复检将复制当前数据并标记原数据的复检状态。').then(() => {
        const ids = this.detailSelection.map(item => item.id);
        this.performOneClickRecheck(ids);
      }).catch(() => {
        // 用户取消操作
      });
    },
    // 执行一键复检
    performOneClickRecheck(ids) {
      const data = {
        ids: ids
      };
      
      this.recheckLoading = true;
      // 调用后端API进行复检
      oneClickRecheck(data).then(response => {
        this.$modal.msgSuccess(`成功复检 ${response.data} 条记录`);
        this.getDetailList(); // 刷新详情列表
        this.getList(); // 刷新主列表
      }).catch(() => {
        this.$modal.msgError('复检失败，请稍后重试');
      }).finally(() => {
        this.recheckLoading = false;
      });
    },
    // 检查是否为特殊用户
    checkSpecialUser() {
      try {
        // 获取当前用户信息，使用若依框架标准方式
        const userInfo = this.$store.state.user;
        debugger
        // 检查用户名或昵称是否为陈慧
        this.isSpecialUser = (userInfo && (userInfo.userName === '陈慧' || userInfo.nickName === '陈慧'));
        
        // 如果不是特殊用户，设置默认筛选条件
        if (!this.isSpecialUser) {
          this.detailParams.recheckStatus = 0;
        }
      } catch (error) {
        console.warn('无法获取用户信息，默认为普通用户:', error);
        // 默认为普通用户
        this.isSpecialUser = false;
        this.detailParams.recheckStatus = 0;
      }
    },
    // 处理隧道变化
    handleTunnelChange(tunnelId) {
      // 清空相关数据
      this.form.checkFacilityIds = [];
      this.checkFacilityList = [];
      
      // 调用原有的分部加载逻辑
      this.selectDistinctPart(2);
      
      // 加载检测设备
      if (tunnelId) {
        this.getCheckFacilitiesByUser(tunnelId);
      }
    },
    // 加载检测设备列表
    getCheckFacilitiesByUser(tunnelId) {
      const _this = this;
      _this.checkFacilityList = [];
      _this.checkFacilityDisabled = false; // 默认不禁用
      if (!tunnelId) {
        console.warn('tunnelId为空，无法加载检测设备');
        return;
      }
      
      getCheckFacilitiesByUser({tunnelId: tunnelId}).then(response => {
        if (response.data && response.data.length > 0) {
          // 如果按隧道查询到了检测设备，使用这些设备
          response.data.forEach(item => {
            _this.checkFacilityList.push({
              value: item.facilityId,
              label: item.facilityName + '(' + item.facilityModel + ')'
            });
          });
          _this.checkFacilityDisabled = false; // 允许修改
        } else {
          // 如果没有查到值，查询所有检测设备，并禁用修改
          console.log('按隧道未查到检测设备，加载所有检测设备并设为只读');
          _this.loadAllCheckFacilities();
        }
      }).catch(error => {
        console.error('加载检测设备失败:', error);
        // 出错时也加载所有检测设备
        _this.loadAllCheckFacilities();
      });
    },
    
    // 加载所有检测设备
    loadAllCheckFacilities() {
      const _this = this;
      listAllCheckFacility({}).then(response => {
        console.log('loadAllCheckFacilities API响应:', response);
        if (response.rows && response.rows.length > 0) {
          response.rows.forEach(item => {
            console.log('处理设备项:', item);
            _this.checkFacilityList.push({
              value: item.id,
              label: item.name + '(' + item.model + ')'
            });
          });
          // 根据form.checkFacilityIds（来自数据库的standard_ids）设置显示的设备
          if (_this.form.checkFacilityIds && _this.form.checkFacilityIds.length > 0) {
            // 确保checkFacilityIds中的值在checkFacilityList中有对应的选项
            // 这样当设备被禁用时，仍然能正确显示已选中的设备名称
            const validFacilityIds = [];

            _this.form.checkFacilityIds.forEach(id => {
              console.log('正在查找ID:', id, '类型:', typeof id);
              const facility = _this.checkFacilityList.find(f => {
                console.log('比较:', f.value, '(类型:', typeof f.value, ') === ', id, '(类型:', typeof id, ')');
                return f.value == id; // 使用宽松比较
              });
              if (facility) {
                validFacilityIds.push(id);
                console.log('找到对应的检测设备:', facility.label);
              } else {
                console.warn('未找到ID为' + id + '的检测设备');
              }
            });
            _this.form.checkFacilityIds = validFacilityIds;
            
            // 确保Vue重新渲染el-select以显示正确的设备名称
            _this.$nextTick(() => {
              console.log('设备列表加载完成，强制重新渲染');
            });
          }
        }
        _this.checkFacilityDisabled = true; // 禁用修改
      }).catch(error => {
        console.error('加载所有检测设备失败:', error);
        _this.checkFacilityDisabled = true; // 即使失败也禁用
      });
    },
    // 加载检测依据列表
    loadStandards() {
      const _this = this;
      _this.standardList = [];
      listCheckStandard({}).then(response => {
        if (response.rows && response.rows.length > 0) {
          response.rows.forEach(item => {
            _this.standardList.push({
              value: item.id,
              label: item.name
            });
          });
        }
      }).catch(error => {
        console.error('加载检测依据失败:', error);
      });
    },
    // 处理时间范围变化
    handleTimeRangeChange(value) {
      if (value && Array.isArray(value) && value.length === 2) {
        this.form.startTime = value[0];
        this.form.endTime = value[1];
      } else {
        this.form.startTime = null;
        this.form.endTime = null;
      }
    },
  }
};
</script>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>