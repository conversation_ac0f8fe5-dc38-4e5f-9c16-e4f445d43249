<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch" label-width="168px">
      <el-form-item label="运营公司" prop="companyName">
        <el-select v-model="queryParams.companyName" placeholder="请选择运营公司" clearable filterable @change="onCompanyChange">
          <el-option v-for="item in companyList" :key="item.companyName" :label="item.companyName" :value="item.companyName" />
        </el-select>
      </el-form-item>
      <el-form-item label="所属路段" prop="roadName">
        <el-select v-model="queryParams.roadName" placeholder="请选择所属路段" clearable filterable @change="onRoadChange">
          <el-option v-for="item in roadList" :key="item.roadName" :label="item.roadName" :value="item.roadName" />
        </el-select>
      </el-form-item>
      <el-form-item label="线路名称" prop="roadCode">
        <el-select v-model="queryParams.roadCode" placeholder="请选择线路名称" clearable filterable @change="onLineChange">
          <el-option v-for="item in lineList" :key="item.roadCode" :label="item.roadCode" :value="item.roadCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-select v-model="queryParams.tunnelName" placeholder="请选择隧道名称" clearable filterable>
          <el-option v-for="item in tunnelNameList" :key="item.tunnelName" :label="item.tunnelName" :value="item.tunnelName" />
        </el-select>
      </el-form-item>
      <el-form-item label="方向:" prop="direction">
        <el-select v-model="queryParams.direction" placeholder="请选择" clearable filterable>
          <el-option v-for="item in directionList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="隧道编码" prop="tunnelCode">
        <el-input
            v-model="queryParams.tunnelCode"
            placeholder="请输入隧道编码"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['electric:tunnelInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['electric:tunnelInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['electric:tunnelInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['electric:tunnelInfo:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAdd"
        >批量导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      <el-dropdown trigger="click" :hide-on-click="false" popper-class="column-dropdown" teleported style="margin-left: 10px; display: inline-block;">
        <el-button type="primary" plain size="mini">
          列显示<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div style="padding: 10px; max-height: 300px; overflow-y: auto; z-index: 9999;">
              <el-checkbox v-for="(item, index) in columns" :key="index" v-model="item.visible" style="display: block; margin-bottom: 8px;">
                {{ item.label }}
              </el-checkbox>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-row>

    <el-table v-loading="loading" :data="tunnelInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" :index="indexMethod" width="50"/>
      <el-table-column label="运营公司" align="center" prop="companyName" width="280" v-if="columns[0].visible"/>
      <el-table-column label="所属路段" align="center" prop="roadName" width="150" v-if="columns[1].visible"/>
      <el-table-column label="路线编号" align="center" prop="roadCode" v-if="columns[2].visible"/>
      <el-table-column label="隧道名称" align="center" prop="tunnelName" width="130" v-if="columns[3].visible"/>
      <el-table-column label="隧道编号" align="center" prop="tunnelCode" width="130"/>
      <el-table-column label="上行编码" align="center" prop="upTunnelCode" v-if="columns[4].visible"/>
      <el-table-column label="下行编码" align="center" prop="downTunnelCode" v-if="columns[5].visible"/>
      <el-table-column label="隧道类型" align="center" prop="tunnelType" :formatter="formatTunnelType" v-if="columns[6].visible"/>
      <el-table-column label="上行起点桩号" align="center" prop="upStartCode" v-if="columns[7].visible"/>
      <el-table-column label="上行终点桩号" align="center" prop="upEndCode" v-if="columns[8].visible"/>
      <el-table-column label="下行起点桩号" align="center" prop="downStartCode" v-if="columns[9].visible"/>
      <el-table-column label="下行终点桩号" align="center" prop="downEndCode" v-if="columns[10].visible"/>
      <el-table-column label="上行长度/m" align="center" prop="upTunnelLength" v-if="columns[11].visible"/>
      <el-table-column label="下行长度/m" align="center" prop="downTunnelLength" v-if="columns[12].visible"/>
      <el-table-column label="上下行合计长度/m" align="center" prop="totalTunnelLength" v-if="columns[13].visible"/>
      <el-table-column label="备注" align="center" prop="remark" v-if="columns[14].visible"/>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['electric:tunnelInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['electric:tunnelInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList" />

    <!-- 添加或修改隧道信息详情对话框 -->
    <el-dialog :title="title" v-model="open" width="700px">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目" prop="tunnelId">
              <el-select v-model="form.tunnelId" placeholder="请选择项目" clearable filterable :disabled="title!='添加隧道信息详情'">
                <el-option v-for="item in totalTunnelList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道名称" prop="tunnelName">
              <el-input v-model="form.tunnelName" placeholder="请输入隧道名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属路段" prop="roadName">
              <el-input v-model="form.roadName" placeholder="请输入所属路段" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总编码" prop="tunnelCode">
              <el-input v-model="form.tunnelCode" placeholder="请输入总编码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="路线编号" prop="roadCode">
              <el-input v-model="form.roadCode" placeholder="请输入路线编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道类型" prop="tunnelType">
              <el-select v-model="form.tunnelType" placeholder="请选择隧道类型">
                <el-option v-for="item in tunnelTypeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="上行编码" prop="upTunnelCode">
              <el-input v-model="form.upTunnelCode" placeholder="请输入上行编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下行编码" prop="downTunnelCode">
              <el-input v-model="form.downTunnelCode" placeholder="请输入下行编码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="上行起点桩号" prop="upStartCode">
              <el-input v-model="form.upStartCode" placeholder="请输入上行起点桩号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上行终点桩号" prop="upEndCode">
              <el-input v-model="form.upEndCode" placeholder="请输入上行终点桩号" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="下行起点桩号" prop="downStartCode">
              <el-input v-model="form.downStartCode" placeholder="请输入下行起点桩号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下行终点桩号" prop="downEndCode">
              <el-input v-model="form.downEndCode" placeholder="请输入下行终点桩号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="上行长度/m" prop="upTunnelLength">
              <el-input v-model="form.upTunnelLength" placeholder="请输入上行长度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下行长度/m" prop="downTunnelLength">
              <el-input v-model="form.downTunnelLength" placeholder="请输入下行长度" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="上下行合计长度/m" prop="totalTunnelLength">
              <el-input v-model="form.totalTunnelLength" placeholder="请输入上下行合计长度" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <ExcelUpload :file="file" @refreshDataList="handleQuery"></ExcelUpload>
  </div>
</template>

<script>
import { listTunnelInfo, getTunnelInfo, delTunnelInfo, addTunnelInfo, updateTunnelInfo } from "@/api/electric/tunnelInfo";
import {selectAllTotalTunnel} from "@/api/electric/tunnel";
import ExcelUpload from "@/views/common/excelUpload.vue";
import { getCompanyList, getRoadListByCompany, getLineListByCompanyAndRoad, getTunnelListByParams } from "@/api/electric/tunnelInfo";

export default {
  name: "TunnelInfo",
  components: {ExcelUpload},
  data() {
    return {
      file:{
        batchAddOpen:false,
        templateUrl:'tunnel.xlsx',
        uploadUrl:'/tunnel/electric/tunnelInfo/batchImport',
        excelName:'隧道导入模板.xlsx'
      },
      batchAddTitle:'',
      totalTunnelList:[],
      // 级联下拉框数据
      companyList: [],
      roadList: [],
      lineList: [],
      tunnelNameList: [],
      tunnelTypeList: [
        {
          label: '短',
          value: 1
        }, {
          label: '中',
          value: 2
        }, {
          label: '长',
          value: 3
        }, {
          label: '特长',
          value: 4
        }
      ],
      directionList: [
        {
          label: '左幅',
          value: 1
        }, {
          label: '右幅',
          value: 2
        }
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道信息详情表格数据
      tunnelInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tunnelCode: null,
        tunnelName: null,
        direction: null,
        companyName: null,
        roadName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tunnelId: [
          { required: true, message: "项目不能为空", trigger: "change" }
        ],
        tunnelName: [
          { required: true, message: "隧道名称不能为空", trigger: "blur" }
        ],
        tunnelCode: [
          { required: true, message: "总编码不能为空", trigger: "blur" }
        ],
        roadName: [
          { required: true, message: "所属路段不能为空", trigger: "blur" }
        ],
        roadCode: [
          { required: true, message: "路段编码不能为空", trigger: "blur" }
        ],
        tunnelType: [
          { required: true, message: "隧道类型不能为空", trigger: "change" }
        ],
        upTunnelCode: [
          { required: true, message: "上行编码不能为空", trigger: "blur" }
        ],
        downTunnelCode: [
          { required: true, message: "下行编码不能为空", trigger: "blur" }
        ],
        // totalTunnelLength: [
        //   { required: true, message: "隧道合计长度不能为空", trigger: "blur" }
        // ]
      },
      // 列显示控制
      columns: [
        { label: '运营公司', visible: true },
        { label: '所属路段', visible: true },
        { label: '路线编号', visible: true },
        { label: '隧道名称', visible: true },
        { label: '上行编码', visible: false },
        { label: '下行编码', visible: false },
        { label: '隧道类型', visible: true },
        { label: '上行起点桩号', visible: true },
        { label: '上行终点桩号', visible: true },
        { label: '下行起点桩号', visible: true },
        { label: '下行终点桩号', visible: true },
        { label: '上行长度/m', visible: true },
        { label: '下行长度/m', visible: true },
        { label: '上下行合计长度/m', visible: true },
        { label: '备注', visible: true },
      ],
    };
  },
  created() {
    this.selectAllTotalTunnel();
    this.getList();
    // 初始化加载所有下拉框数据
    this.loadCompanyList();
    this.loadRoadList();
    this.loadLineList();
    this.loadTunnelNameList();
  },
  watch: {
    // 监视上行长度和下行长度的变化，自动计算总长度
    'form.upTunnelLength': {
      handler: function(val) {
        this.calculateTotalLength();
      }
    },
    'form.downTunnelLength': {
      handler: function(val) {
        this.calculateTotalLength();
      }
    }
  },
  methods: {
    // 计算序号
    indexMethod(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    selectAllTotalTunnel(){
      var _this = this;
      _this.totalTunnelList = [];
      selectAllTotalTunnel().then(response => {
        response.data.forEach(function (e){
          let temp = {};
          temp.value = e.id;
          temp.label = e.companyName + "/" + e.section;
          _this.totalTunnelList.push(temp);
        })
      });
    },
    formatTunnelType(row, column, cellValue) {
      var _this = this;
      var columnValue = "";
      Object.keys(_this.tunnelTypeList).forEach(function (key) {
        if (_this.tunnelTypeList[key].value == row.tunnelType) {
          columnValue = _this.tunnelTypeList[key].label
        }
      });
      return columnValue
    },
    formatDirection(row, column, cellValue) {
      var _this = this;
      var columnValue = "";
      Object.keys(_this.directionList).forEach(function (key) {
        if (_this.directionList[key].value == row.direction) {
          columnValue = _this.directionList[key].label
        }
      });
      return columnValue
    },
    /** 查询隧道信息详情列表 */
    getList() {
      this.loading = true;
      listTunnelInfo(this.queryParams).then(response => {
        this.tunnelInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tunnelId: null,
        tunnelCode: null,
        tunnelName: null,
        upTunnelCode: null,
        downTunnelCode: null,
        companyName: null,
        roadName: null,
        roadCode: null,
        upStartCode: null,
        upEndCode: null,
        downStartCode: null,
        downEndCode: null,
        upTunnelLength: null,
        downTunnelLength: null,
        totalTunnelLength: null,
        tunnelType: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 重置级联下拉框数据，重新加载所有选项（不进行过滤）
      this.loadRoadList();
      this.loadLineList();
      this.loadTunnelNameList();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道信息详情";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTunnelInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改隧道信息详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 提交前重新计算总长度确保数据一致
          this.calculateTotalLength();
          
          if (this.form.id != null) {
            updateTunnelInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTunnelInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除隧道信息详情编号为"' + ids + '"的数据项？').then(function() {
        return delTunnelInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tunnel/electric/tunnelInfo/export', {
        ...this.queryParams
      }, `隧道信息详情_${new Date().getTime()}.xlsx`)
    },
    handleBatchAdd() {
      this.file.batchAddOpen = true;
      this.file.batchAddTitle = "批量导入";
    },
    /** 计算总长度 */
    calculateTotalLength() {
      let upLength = parseFloat(this.form.upTunnelLength || 0);
      let downLength = parseFloat(this.form.downTunnelLength || 0);
      this.form.totalTunnelLength = (upLength + downLength).toFixed(2);
    },
    
    /** 加载运营公司列表 */
    loadCompanyList() {
      getCompanyList().then(response => {
        this.companyList = response.data || [];
      }).catch(error => {
        console.error('加载运营公司列表失败:', error);
      });
    },
    
    /** 运营公司变化时的处理 */
    onCompanyChange(value) {
      // 清空下级选项
      this.queryParams.roadName = null;
      this.queryParams.roadCode = null;
      this.queryParams.tunnelName = null;
      
      // 重新加载下级数据，如果有选择公司则进行过滤，否则显示所有数据
      this.loadRoadList(value);
      this.loadLineList(value, null);
      this.loadTunnelNameList(value, null, null);
    },
    
    /** 加载路段列表 */
    loadRoadList(companyName) {
      getRoadListByCompany(companyName).then(response => {
        this.roadList = response.data || [];
      }).catch(error => {
        console.error('加载路段列表失败:', error);
      });
    },
    
    /** 路段变化时的处理 */
    onRoadChange(value) {
      // 清空下级选项
      this.queryParams.roadCode = null;
      this.queryParams.tunnelName = null;
      
      // 重新加载下级数据，传入已选择的上级参数进行过滤
      this.loadLineList(this.queryParams.companyName, value);
      this.loadTunnelNameList(this.queryParams.companyName, value, null);
    },
    
    /** 加载线路列表 */
    loadLineList(companyName, roadName) {
      getLineListByCompanyAndRoad(companyName, roadName).then(response => {
        this.lineList = response.data || [];
      }).catch(error => {
        console.error('加载线路列表失败:', error);
      });
    },
    
    /** 线路变化时的处理 */
    onLineChange(value) {
      // 清空下级选项
      this.queryParams.tunnelName = null;
      
      // 重新加载隧道名称数据，传入已选择的上级参数进行过滤
      this.loadTunnelNameList(this.queryParams.companyName, this.queryParams.roadName, value);
    },
    
    /** 加载隧道名称列表 */
    loadTunnelNameList(companyName, roadName, roadCode) {
      getTunnelListByParams(companyName, roadName, roadCode).then(response => {
        this.tunnelNameList = response.data || [];
      }).catch(error => {
        console.error('加载隧道名称列表失败:', error);
      });
    }
  }
};
</script>
