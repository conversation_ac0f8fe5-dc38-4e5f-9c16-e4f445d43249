<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备型号" prop="model">
        <el-input
          v-model="queryParams.model"
          placeholder="请输入设备型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主要用途" prop="mainUsage">
        <el-input
          v-model="queryParams.mainUsage"
          placeholder="请输入主要用途"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="校准起始时间" prop="startPeriod">
        <el-date-picker
          v-model="queryParams.startPeriod"
          type="date"
          placeholder="请选择校准起始时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="校准结束时间" prop="endPeriod">
        <el-date-picker
          v-model="queryParams.endPeriod"
          type="date"
          placeholder="请选择校准结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="是否在库" prop="flag">
        <el-select v-model="queryParams.flag" placeholder="请选择是否在库" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery">搜索</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['electric:scCheckFacility:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['electric:scCheckFacility:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['electric:scCheckFacility:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          @click="handleImport"
          v-hasPermi="['electric:scCheckFacility:import']"
        >导入设备</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['electric:scCheckFacility:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scCheckFacilityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" :index="indexMethod" width="60"/>
      <el-table-column label="设备名称" align="center" prop="name" width="150"/>
      <el-table-column label="设备型号" align="center" prop="model" width="150"/>
      <el-table-column label="设备编号" align="center" prop="code" width="150"/>
      <el-table-column label="主要用途" align="center" prop="mainUsage" width="200" show-overflow-tooltip/>
      <el-table-column label="校准周期" align="center" prop="period" width="200">
        <template v-slot:default="scope">
          <span>{{ formatPeriod(scope.row.startPeriod, scope.row.endPeriod) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否在库" align="center" prop="flag" width="100">
        <template v-slot:default="scope">
          <el-tag
            :type="scope.row.flag === 1 ? 'success' : 'danger'"
            size="mini"
          >{{ scope.row.flag === 1 ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="当前所属任务" align="center" prop="currentTaskRoadName" width="200"/>
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template v-slot:default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width" fixed="right">
        <template v-slot:default="scope">
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            @click="handleView(scope.row)"-->
<!--          >查看</el-button>-->
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['electric:scCheckFacility:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['electric:scCheckFacility:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList" />

    <!-- 添加或修改检测设备对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="code">
              <el-input v-model="form.code" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否在库" prop="flag">
              <el-radio-group v-model="form.flag">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="主要用途" prop="mainUsage">
          <el-input v-model="form.mainUsage" placeholder="请输入主要用途" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="校准起始时间" prop="startPeriod">
              <el-date-picker
                v-model="form.startPeriod"
                type="date"
                placeholder="请选择校准起始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="校准结束时间" prop="endPeriod">
              <el-date-picker
                v-model="form.endPeriod"
                type="date"
                placeholder="请选择校准结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="检测设备详情" v-model="viewOpen" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ viewData.name }}</el-descriptions-item>
        <el-descriptions-item label="设备型号">{{ viewData.model }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{ viewData.code }}</el-descriptions-item>
        <el-descriptions-item label="是否在库">
          <el-tag :type="viewData.flag === 1 ? 'success' : 'danger'" size="mini">
            {{ viewData.flag === 1 ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="主要用途" :span="2">{{ viewData.mainUsage }}</el-descriptions-item>
        <el-descriptions-item label="校准周期">{{ formatPeriod(viewData.startPeriod, viewData.endPeriod) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" :span="2">{{ parseTime(viewData.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 检测设备导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的设备数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click.stop="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listScCheckFacility, getScCheckFacility, delScCheckFacility, addScCheckFacility, updateScCheckFacility } from "@/api/electric/checkFacility";
import {parseTime} from "../../../utils/ruoyi";
import {getToken} from "@/utils/auth";

export default {
  name: "ScCheckFacility",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检测设备表格数据
      scCheckFacilityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 查看详情数据
      viewData: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: import.meta.env.VITE_APP_BASE_API + "/tunnel/electric/checkFacility/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        model: null,
        code: null,
        mainUsage: null,
        startPeriod: null,
        endPeriod: null,
        flag: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        model: [
          { required: true, message: "设备型号不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "设备编号不能为空", trigger: "blur" }
        ],
        mainUsage: [
          { required: true, message: "主要用途不能为空", trigger: "blur" }
        ],
        startPeriod: [
          { required: true, message: "校准起始时间不能为空", trigger: "blur" }
        ],
        endPeriod: [
          { required: true, message: "校准结束时间不能为空", trigger: "blur" }
        ],
        flag: [
          { required: true, message: "是否在库不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,
    // 计算序号
    indexMethod(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    // 格式化校准周期显示
    formatPeriod(startPeriod, endPeriod) {
      if (!startPeriod && !endPeriod) {
        return '暂无';
      }
      if (startPeriod && endPeriod) {
        return `${startPeriod} ~ ${endPeriod}`;
      }
      if (startPeriod) {
        return `${startPeriod} ~ 未设置`;
      }
      if (endPeriod) {
        return `未设置 ~ ${endPeriod}`;
      }
      return '暂无';
    },
    /** 查询检测设备列表 */
    getList() {
      this.loading = true;
      listScCheckFacility(this.queryParams).then(response => {
        this.scCheckFacilityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        model: null,
        code: null,
        mainUsage: null,
        startPeriod: null,
        endPeriod: null,
        flag: 1,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检测设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getScCheckFacility(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检测设备";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewData = row;
      this.viewOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateScCheckFacility(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addScCheckFacility(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除检测设备编号为"' + ids + '"的数据项？').then(function() {
        return delScCheckFacility(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tunnel/electric/checkFacility/export', {
        ...this.queryParams
      }, `检测设备_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "检测设备导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download("tunnel/electric/checkFacility/importTemplate", {}, `检测设备导入模板_${new Date().getTime()}.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs["uploadRef"].handleRemove(file);
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs["uploadRef"].submit();
    }
  }
};
</script>