<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item label="运营公司:" prop="companyName">
        <el-select v-model="queryParams.companyName" placeholder="请选择运营公司" clearable filterable @change="handleCompanyChange">
          <el-option v-for="item in companyList" :key="item.companyName" :label="item.companyName" :value="item.companyName" />
        </el-select>
      </el-form-item>
      <el-form-item label="路段名称:" prop="roadName">
        <el-select v-model="queryParams.roadName" placeholder="请选择" clearable filterable @change="handleRoadChange">
          <el-option v-for="item in roadList" :key="item.label" :label="item.label" :value="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="隧道名称:" prop="tunnelId">
        <el-select v-model="queryParams.tunnelId" placeholder="请选择" clearable filterable>
          <el-option v-for="item in filteredTunnelList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="录入类型:" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择" clearable>
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备桩号" prop="checkCode">
        <el-input
          v-model="queryParams.checkCode"
          placeholder="请输入设备桩号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分部名称" prop="partName">
        <el-input
          v-model="queryParams.partName"
          placeholder="请输入分部名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分部编码" prop="partCode">
        <el-input
          v-model="queryParams.partCode"
          placeholder="请输入分部编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分项名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入分项名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分项编码" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入分项编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="位置" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备编码" prop="equipCode">
        <el-input
          v-model="queryParams.equipCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          @click="handleExportDetail"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAdd"
        >人工导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAddCar"
        >自动化导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      <el-dropdown trigger="click" :hide-on-click="false" popper-class="column-dropdown" teleported style="margin-left: 10px; display: inline-block;">
        <el-button type="primary" plain size="mini">
          列显示<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div style="padding: 10px; max-height: 300px; overflow-y: auto; z-index: 9999;">
              <el-checkbox v-for="(item, index) in columns" :key="index" v-model="item.visible" style="display: block; margin-bottom: 8px;">
                {{ item.label }}
              </el-checkbox>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" :index="indexMethod" width="50"/>
      <el-table-column label="隧道名称" align="center" prop="tunnelName" v-if="columns[0].visible"/>
      <el-table-column label="设备桩号" align="center" prop="checkCode" v-if="columns[1].visible"/>
      <el-table-column label="隧道编码" align="center" prop="tunnelCode" v-if="columns[2].visible"/>
      <el-table-column label="分部" align="center" prop="partName" v-if="columns[3].visible"/>
      <el-table-column label="分部编码" align="center" prop="partCode" v-if="columns[4].visible"/>
      <el-table-column label="分项" align="center" prop="itemName" v-if="columns[5].visible"/>
      <el-table-column label="分项编码" align="center" prop="itemCode" v-if="columns[6].visible"/>
      <el-table-column label="设备位置" align="center" prop="locationName" v-if="columns[7].visible"/>
      <el-table-column label="设备数量" align="center" prop="num" v-if="columns[8].visible"/>
      <el-table-column label="编码" align="center" prop="code" v-if="columns[9].visible"/>
      <el-table-column label="设备编码" align="center" prop="equipCode" width="250" v-if="columns[10].visible"/>
      <el-table-column label="单位" align="center" prop="unit" v-if="columns[11].visible"/>
      <el-table-column label="照片路径" align="center" prop="picUrl" v-if="columns[13].visible"/>
      <el-table-column label="备注" align="center" prop="remark" v-if="columns[14].visible"/>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template v-slot:default="scope">
            <el-button
                size="mini"
                type="success"
                @click="viewDetail(scope.row)"
            >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList" />

    <!-- 添加或修改隧道资产信息详情对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="隧道名称:" prop="tunnelId">
          <el-select v-model="form.tunnelId" placeholder="请选择" clearable filterable :disabled="isUpdate">
            <el-option v-for="item in tunnelInfoList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备桩号">
          <el-input v-model="form.checkCode" placeholder="请输入设备桩号" />
        </el-form-item>
        <el-form-item label="分部名称" prop="partName">
          <el-input v-model="form.partName" placeholder="请输入分部名称" />
        </el-form-item>
        <el-form-item label="分部编码" prop="partCode">
          <el-input v-model="form.partCode" placeholder="请输入分部编码" />
        </el-form-item>
        <el-form-item label="分项名称" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入分项名称" />
        </el-form-item>
        <el-form-item label="分项编码" prop="itemCode">
          <el-input v-model="form.itemCode" placeholder="请输入分项编码" />
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入位置" />
        </el-form-item>
        <el-form-item label="位置名称" prop="locationName">
          <el-input v-model="form.locationName" placeholder="请输入位置" />
        </el-form-item>
        <el-form-item label="编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
<!--        <el-form-item label="设备编码" prop="equipCode">-->
<!--          <el-input v-model="form.equipCode" placeholder="请输入设备编码" />-->
<!--        </el-form-item>-->
        <el-form-item label="单位" >
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
<!--        <el-form-item label="照片路径" prop="picUrl">-->
<!--          <el-input v-model="form.picUrl" placeholder="请输入照片路径" />-->
<!--        </el-form-item>-->
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="设备列表" v-model="detailVisible" width="96%">
      <el-row :gutter="10" class="mb8">
        <el-dropdown trigger="click" :hide-on-click="false" popper-class="column-dropdown" teleported style="margin-left: 10px; display: inline-block;">
          <el-button type="primary" plain size="mini">
            列显示<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <div style="padding: 10px; max-height: 300px; overflow-y: auto; z-index: 9999;">
                <el-checkbox v-for="(item, index) in detailColumns" :key="index" v-model="item.visible" style="display: block; margin-bottom: 8px;">
                  {{ item.label }}
                </el-checkbox>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-row>
      <el-table v-loading="loading" :data="detailList" @selection-change="handleSelectionChange">
        <el-table-column label="序号" type="index" :index="indexMethod" width="50"/>
        <el-table-column label="隧道名称" align="center" prop="tunnelName" width="150" v-if="detailColumns[0].visible"/>
        <el-table-column label="设备桩号" align="center" prop="checkCode" v-if="detailColumns[1].visible"/>
        <el-table-column label="分部" align="center" prop="partName" width="150" v-if="detailColumns[2].visible"/>
        <el-table-column label="分项" align="center" prop="itemName" width="150" v-if="detailColumns[3].visible"/>
        <el-table-column label="设备位置" align="center" prop="locationName" width="150"  v-if="detailColumns[4].visible"/>
        <el-table-column label="编码" align="center" prop="code" v-if="detailColumns[5].visible"/>
        <el-table-column label="设备编码" align="center" prop="equipCode" width="250" v-if="detailColumns[6].visible"/>
        <el-table-column label="设备名称" align="center" prop="name" width="250" v-if="detailColumns[7].visible"/>
        <el-table-column label="录入类型" align="center">
          <template v-slot:default="scope">
            {{ scope.row.type === 1 ? '人工' : scope.row.type === 2 ? '自动化' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" v-if="detailColumns[9].visible"/>
        <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
          <template v-slot:default="scope">
            <el-button
                size="mini"
                type="success"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:info:edit']"
            >修改</el-button>
            <el-button
                size="mini"
                type="primary"
                @click="seeFacilityInfoDetail(scope.row)"
            >详情</el-button>
            <el-button
                size="mini"
                type="danger"
                @click="handleDeleteByIds(scope.row)"
                v-hasPermi="['system:info:remove']"
            >删除</el-button>

          </template>
        </el-table-column>
      </el-table>
    </el-dialog>


    <el-dialog title="设备详情列表" v-model="facilityInfoDetailVisible" width="96%">
      <el-table v-loading="detailLoading" :data="facilityInfoDetailList">
        <el-table-column label="序号" type="index" :index="indexMethod" width="50"/>
        <el-table-column label="设备桩号" align="center" prop="checkCode"/>
        <el-table-column label="分部" align="center" prop="partName" width="150"/>
        <el-table-column label="分项" align="center" prop="itemName" width="150"/>
        <el-table-column label="设备位置" align="center" prop="locationName" width="150"/>
        <el-table-column label="编码" align="center" prop="code"/>
        <el-table-column label="设备编码" align="center" prop="equipCode" width="250"/>
        <el-table-column label="设备名称" align="center" prop="name" width="250"/>
        <el-table-column label="录入类型" align="center">
          <template v-slot:default="scope">
            {{ scope.row.type === 1 ? '人工' : scope.row.type === 2 ? '自动化' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark"/>
<!--        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">-->
<!--          <template v-slot:default="scope">-->
<!--            <el-button-->
<!--                size="mini"-->
<!--                type="success"-->
<!--                @click="handleUpdate(scope.row)"-->
<!--                v-hasPermi="['system:info:edit']"-->
<!--            >修改</el-button>-->
<!--            <el-button-->
<!--                size="mini"-->
<!--                type="danger"-->
<!--                @click="handleDeleteByIds(scope.row)"-->
<!--                v-hasPermi="['system:info:remove']"-->
<!--            >删除</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
    </el-dialog>


    <ExcelUpload :file="file" @refreshDataList="handleQuery"></ExcelUpload>

    <ExcelUpload :file="fileCar" @refreshDataList="handleQuery"></ExcelUpload>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
  listDistinctInfo,
  delTunnelInfo, selectFacilityInfoDetailByPage
} from "@/api/electric/facilityInfo";
import ExcelUpload from "@/views/common/excelUpload.vue";
import {listAll, listDistinctRoad, selectDistinctTunnelCode, getCompanyList, getRoadListByCompany} from "@/api/electric/tunnelInfo";
import facilityInfo from "@/views/electric/facilityInfo/index.vue";
export default {
  name: "Info",
  computed: {
    facilityInfo() {
      return facilityInfo
    }
  },
  components: {ExcelUpload},
  data() {
    return {
      detailLoading:false,
      facilityInfoDetailList:[],
      companyList: [],
      roadList:[],
      tunnelInfoList:[],
      filteredTunnelList: [],
      detailList:[],
      detailVisible:false,
      facilityInfoDetailVisible:false,
      columns: [
        { label: '隧道名称', visible: true },
        { label: '设备桩号', visible: false },
        { label: '隧道编码', visible: false },
        { label: '分部', visible: true },
        { label: '分部编码', visible: false },
        { label: '分项', visible: true },
        { label: '分项编码', visible: false },
        { label: '设备位置', visible: true },
        { label: '设备数量', visible: true },
        { label: '编码', visible: false },
        { label: '设备编码', visible: false },
        { label: '单位', visible: false },
        { label: '录入类型', visible: false },
        { label: '照片路径', visible: false },
        { label: '备注', visible: false },
      ],
      isUpdate:false,
      file:{
        batchAddOpen:false,
        templateUrl:'facilityInfo.xlsx',
        uploadUrl:'/tunnel/electric/facilityInfo/batchImport',
        excelName:'隧道资产导入模板.xlsx'
      },
      fileCar:{
        batchAddOpen:false,
        templateUrl:'facilityInfo_car.xlsx',
        uploadUrl:'/tunnel/electric/facilityInfo/batchImportCar',
        excelName:'隧道资产导入模板.xlsx'
      },
      batchAddTitle:'',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道资产信息详情表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tunnelCode: null,
        checkCode: null,
        partName: null,
        partCode: null,
        itemName: null,
        itemCode: null,
        location: null,
        equipCode: null,
        type: null,
        companyName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [
          { required: true, message: "编号不能为空", trigger: "blur" }
        ],
        locationName: [
          { required: true, message: "位置名称不能为空", trigger: "blur" }
        ],
        tunnelId: [
          { required: true, message: "隧道编码不能为空", trigger: "blur" }
        ],
        checkCode: [
          { required: true, message: "设备桩号不能为空", trigger: "blur" }
        ],
        partName: [
          { required: true, message: "分部名称不能为空", trigger: "blur" }
        ],
        partCode: [
          { required: true, message: "分部编码不能为空", trigger: "blur" }
        ],
        itemName: [
          { required: true, message: "分项名称不能为空", trigger: "blur" }
        ],
        itemCode: [
          { required: true, message: "分项编码不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "位置不能为空", trigger: "blur" }
        ],
        equipCode: [
          { required: true, message: "设备编码不能为空", trigger: "blur" }
        ],
        unit: [
          { required: true, message: "单位不能为空", trigger: "blur" }
        ],
        picUrl: [
          { required: true, message: "照片路径不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ]
      },
      // 详情列显示控制
      detailColumns: [
        { label: '隧道名称', visible: true },
        { label: '设备桩号', visible: true },
        { label: '分部', visible: true },
        { label: '分项', visible: true },
        { label: '设备位置', visible: true },
        { label: '编码', visible: true },
        { label: '设备编码', visible: false },
        { label: '设备名称', visible: true },
        { label: '照片路径', visible: true },
        { label: '备注', visible: true },
      ],
      typeOptions: [
        { value: 1, label: '人工' },
        { value: 2, label: '自动化' }
      ],
    };
  },
  created() {
    this.loadCompanyList();
    this.getListDistinctRoad();
    this.selectDistinctTunnelCodeList();
    this.getList();
  },
  methods: {
    /** 加载运营公司列表 */
    loadCompanyList() {
      getCompanyList().then(response => {
        this.companyList = response.data || [];
      }).catch(error => {
        console.error('Failed to load company list:', error);
      });
    },
    /** 运营公司变化处理 */
    handleCompanyChange(companyName) {
      // 清空路段和隧道选择
      this.queryParams.roadName = null;
      this.queryParams.tunnelId = null;
      this.filteredTunnelList = [];
      
      // 根据运营公司加载路段列表
      this.loadRoadListByCompany(companyName);
    },
    /** 根据运营公司加载路段列表 */
    loadRoadListByCompany(companyName = null) {
      getRoadListByCompany(companyName).then(response => {
        this.roadList = [];
        const roadNameSet = new Set();
        (response.data || []).forEach(item => {
          if (item.roadName && !roadNameSet.has(item.roadName)) {
            roadNameSet.add(item.roadName);
            this.roadList.push({
              label: item.roadName,
              value: item.roadName
            });
          }
        });
      }).catch(error => {
        console.error('Failed to load road list:', error);
      });
    },
    getListDistinctRoad(){
      let _this=this;
      listDistinctRoad({}).then(response => {
        _this.roadList=[];
        // 使用Set进行去重处理
        const roadNameSet = new Set();
        response.data.forEach(function (e){
          if (e.roadName && !roadNameSet.has(e.roadName)) {
            roadNameSet.add(e.roadName);
            let temp = {};
            temp.label = e.roadName;
            temp.value = e.roadName;
            _this.roadList.push(temp);
          }
        })
      });
    },
    indexMethod(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    formatterEquipCode(row, column, cellValue) {
      return row.tunnelCode+'-'+row.equipCode;
    },
    /** 查询隧道资产信息详情列表 */
    getList() {
      this.loading = true;
      listDistinctInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    selectDistinctTunnelCodeList(){
      selectDistinctTunnelCode({}).then(response => {
        this.filteredTunnelList = [];
        this.tunnelInfoList = []; // 同时初始化tunnelInfoList
        if (response.data && response.data.length > 0) {
          response.data.forEach(item => {
            if (item.id && item.tunnelName) {
              const temp = {
                value: item.id,
                label: item.tunnelName + (item.tunnelCode ? "/" + item.tunnelCode : "")
              };
              this.filteredTunnelList.push(temp);
              this.tunnelInfoList.push(temp); // 同时添加到tunnelInfoList
            }
          });
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取隧道列表失败:", error);
        this.loading = false;
      });
    },

    // 添加路段变化处理方法
    handleRoadChange(val) {
      // 清空已选择的隧道
      this.queryParams.tunnelId = null;
      
      if (!val) {
        // 如果清空路段选择，清空隧道列表
        this.filteredTunnelList = [];
        this.tunnelInfoList = []; // 同时清空tunnelInfoList
        return;
      }
      
      // 根据选择的路段和运营公司获取对应的隧道列表
      const req = {
        roadName: val
      };
      
      // 如果有选择运营公司，也加入查询条件
      if (this.queryParams.companyName) {
        req.companyName = this.queryParams.companyName;
      }
      
      // 显示加载状态
      this.loading = true;
      
      selectDistinctTunnelCode(req).then(response => {
        this.filteredTunnelList = [];
        this.tunnelInfoList = []; // 同时清空tunnelInfoList
        if (response.data && response.data.length > 0) {
          response.data.forEach(item => {
            if (item.id && item.tunnelName) {
              const temp = {
                value: item.id,
                label: item.tunnelName + (item.tunnelCode ? "/" + item.tunnelCode : "")
              };
              this.filteredTunnelList.push(temp);
              this.tunnelInfoList.push(temp); // 同时添加到tunnelInfoList
            }
          });
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取隧道列表失败:", error);
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tunnelCode: null,
        checkCode: null,
        partName: null,
        partCode: null,
        itemName: null,
        itemCode: null,
        location: null,
        equipCode: null,
        unit: null,
        picUrl: null,
        remark: null,
        createTime: null,
        updateTime: null,
        type:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 重置所有下拉列表数据
      this.loadCompanyList();
      this.getListDistinctRoad();
      this.selectDistinctTunnelCodeList();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      for(let p of selection){
        let q={};
        q.tunnelId = p.tunnelId;
        q.partCode = p.partCode;
        q.itemCode = p.itemCode;
        q.location = p.location;
        this.ids.push(q);
      }
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isUpdate = false;
      this.title = "添加隧道资产信息详情";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isUpdate = true;
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改隧道资产信息详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = this.ids;
      this.$modal.confirm('是否确认删除隧道资产信息的数据项？').then(function() {
        return delInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDeleteByIds(row) {
      const ids = [row.id];
      this.$modal.confirm('是否确认删除隧道资产信息的数据项？').then(function() {
        return delTunnelInfo(ids);
      }).then(() => {
        this.getList();
        this.viewDetail(row);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    viewDetail(row){
      this.loading = true;
      this.detailVisible=true;
      let req={};
      req.tunnelId=row.tunnelId;
      req.partCode=row.partCode;
      req.itemCode=row.itemCode;
      req.location=row.location;
      req.type=this.queryParams.type;
      req.checkCode=this.queryParams.checkCode;
      listInfo(req).then(response => {
        this.detailList = response;
        this.loading = false;
      });
    },
    seeFacilityInfoDetail(row){
      this.detailLoading = true;
      this.facilityInfoDetailVisible=true;
      let req={};
      req.tunnelId=row.tunnelId;
      req.partCode=row.partCode;
      req.itemCode=row.itemCode;
      req.location=row.location;
      req.code=row.code;
      selectFacilityInfoDetailByPage(req).then(response => {
        this.facilityInfoDetailList = response;
        this.detailLoading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    },
    /** 导出设备明细数据 */
    handleExportDetail() {
      this.$modal.confirm('是否确认导出设备明细数据？').then(() => {
        this.download('tunnel/electric/facilityInfo/exportFacilityDetail', {
          ...this.queryParams
        }, `设备明细数据_${new Date().getTime()}.xlsx`)
      });
    },
    handleBatchAdd() {
      this.file.batchAddOpen = true;
      this.file.batchAddTitle = "人工-批量导入";
    },
    handleBatchAddCar() {
      this.fileCar.batchAddOpen = true;
      this.fileCar.batchAddTitle = "自动化-批量导入";
    },
  }
};
</script>
