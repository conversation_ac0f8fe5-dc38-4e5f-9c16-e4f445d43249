import request from '@/utils/request'

// 查询隧道信息详情列表
export function listTunnelInfo(query) {
  return request({
    url: '/tunnel/electric/tunnelInfo/list',
    method: 'get',
    params: query
  })
}


export function listDistinctRoad(query) {
  return request({
    url: '/tunnel/electric/tunnelInfo/listDistinctRoad',
    method: 'get',
    params: query
  })
}




export function listAll(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo/listAll',
    method: 'post',
    params: data
  })
}

// 查询隧道信息详情详细
export function getTunnelInfo(id) {
  return request({
    url: '/tunnel/electric/tunnelInfo/' + id,
    method: 'get'
  })
}

// 新增隧道信息详情
export function addTunnelInfo(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo',
    method: 'post',
    data: data
  })
}

// 修改隧道信息详情
export function updateTunnelInfo(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo',
    method: 'put',
    data: data
  })
}

// 删除隧道信息详情
export function delTunnelInfo(id) {
  return request({
    url: '/tunnel/electric/tunnelInfo/' + id,
    method: 'delete'
  })
}


export function selectDistinctTunnelCode(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo/selectDistinctTunnelCode',
    method: 'post',
    data: data
  })
}

// 查询隧道评分详情
export function getTunnelScoreDetail(tunnelId) {
  return request({
    url: '/tunnel/electric/relation/getTunnelScoreDetail',
    method: 'get',
    params: { tunnelId }
  })
}

// 更新隧道复核状态
export function updateTunnelReviewStatus(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo/review',
    method: 'post',
    data: data
  })
}

// 查询不同的运营公司
export function selectDistinctCompany(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo/selectDistinctCompany',
    method: 'post',
    data: data
  })
}

// 根据运营公司查询不同的路段
export function selectDistinctRoad(data) {
  return request({
    url: '/tunnel/electric/tunnelInfo/selectDistinctRoad',
    method: 'post',
    data: data
  })
}

// 获取分部得分详情
export function getPartScoreDetail(tunnelId, partName) {
  return request({
    url: '/tunnel/electric/relation/getPartScoreDetail',
    method: 'get',
    params: {
      tunnelId,
      partName
    }
  })
}

// 获取运营公司列表（用于级联下拉框）
export function getCompanyList() {
  return request({
    url: '/tunnel/electric/tunnelInfo/getCompanyList',
    method: 'get'
  })
}

// 根据运营公司获取路段列表（用于级联下拉框）
export function getRoadListByCompany(companyName) {
  const params = {};
  if (companyName) {
    params.companyName = companyName;
  }
  return request({
    url: '/tunnel/electric/tunnelInfo/getRoadListByCompany',
    method: 'get',
    params: params
  })
}

// 根据运营公司和路段获取线路列表（用于级联下拉框）
export function getLineListByCompanyAndRoad(companyName, roadName) {
  const params = {};
  if (companyName) {
    params.companyName = companyName;
  }
  if (roadName) {
    params.roadName = roadName;
  }
  return request({
    url: '/tunnel/electric/tunnelInfo/getLineListByCompanyAndRoad',
    method: 'get',
    params: params
  })
}

// 根据运营公司、路段和线路获取隧道列表（用于级联下拉框）
export function getTunnelListByParams(companyName, roadName, roadCode) {
  const params = {};
  if (companyName) {
    params.companyName = companyName;
  }
  if (roadName) {
    params.roadName = roadName;
  }
  if (roadCode) {
    params.roadCode = roadCode;
  }
  return request({
    url: '/tunnel/electric/tunnelInfo/getTunnelListByParams',
    method: 'get',
    params: params
  })
}

