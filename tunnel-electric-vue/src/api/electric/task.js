import request from '@/utils/request'

// 查询任务列表
export function listTask(query) {
  return request({
    url: '/tunnel/electric/task/list',
    method: 'get',
    params: query
  })
}

// 查询任务详细
export function getTask(id) {
  return request({
    url: '/tunnel/electric/task/' + id,
    method: 'get'
  })
}

// 新增任务
export function addTask(data) {
  return request({
    url: '/tunnel/electric/task',
    method: 'post',
    data: data
  })
}

// 修改任务
export function updateTask(data) {
  return request({
    url: '/tunnel/electric/task',
    method: 'put',
    data: data
  })
}

// 删除任务
export function delTask(id) {
  return request({
    url: '/tunnel/electric/task/' + id,
    method: 'delete'
  })
}

// 查询路段列表
export function listRoad() {
  return request({
    url: '/tunnel/electric/task/roads',
    method: 'get'
  })
}

// 根据路段编号查询隧道列表
export function listTunnelByRoadName(roadName) {
  return request({
    url: '/tunnel/electric/task/tunnels/' + roadName,
    method: 'get'
  })
}

// 查询用户列表
export function listUser() {
  return request({
    url: '/tunnel/electric/task/users',
    method: 'get'
  })
}

// 根据隧道ID查询隧道详情
export function getTunnelDetails(tunnelIds) {
  return request({
    url: '/tunnel/electric/task/tunnelDetails/' + tunnelIds,
    method: 'get'
  })
}

// 获取出库设备列表
export function selectFacilities() {
  return request({
    url: '/tunnel/electric/task/selectFacilities',
    method: 'get'
  })
}

// 获取入库设备列表
export function getInFacilities(taskId) {
  return request({
    url: '/tunnel/electric/task/facilities/in/' + taskId,
    method: 'get'
  })
}

// 获取设备状态列表
export function getFacilityStatusList(taskId) {
  return request({
    url: '/tunnel/electric/task/facilityStatus/' + taskId,
    method: 'get'
  })
}

// 更新设备记录
export function updateFacilityRecord(data) {
  return request({
    url: '/tunnel/electric/task/facilityRecord',
    method: 'put',
    data: data
  })
}

// 复制任务
export function duplicateTask(data) {
  return request({
    url: '/tunnel/electric/task/duplicate',
    method: 'post',
    data: data
  })
}