import request from '@/utils/request'

// 查询机电设施检测列表
export function listCheck(query) {
  return request({
    url: '/tunnel/electric/check/list',
    method: 'get',
    params: query
  })
}

// 查询机电设施检测详细
export function getCheck(id) {
  return request({
    url: '/tunnel/electric/check/' + id,
    method: 'get'
  })
}

// 新增机电设施检测
export function addCheck(data) {
  return request({
    url: '/tunnel/electric/check',
    method: 'post',
    data: data
  })
}

// 修改机电设施检测
export function updateCheck(data) {
  return request({
    url: '/tunnel/electric/check',
    method: 'put',
    data: data
  })
}

// 删除机电设施检测
export function delCheck(id) {
  return request({
    url: '/tunnel/electric/check/' + id,
    method: 'delete'
  })
}



export function saveOrUpdateTunnelCheck(data) {
  return request({
    url: '/tunnel/electric/check/saveOrUpdateTunnelCheck',
    method: 'post',
    data:data
  })
}

// 查询机电设施检测明细列表
export function listCheckDetail(query) {
  return request({
    url: '/tunnel/electric/check/detail/list',
    method: 'get',
    params: query
  })
}

// 批量复核检测记录
export function batchReview(data) {
  return request({
    url: '/tunnel/electric/check/batchReview',
    method: 'post',
    data: data
  })
}

// 一键复检检测记录
export function oneClickRecheck(data) {
  return request({
    url: '/tunnel/electric/check/oneClickRecheck',
    method: 'post',
    data: data
  })
}